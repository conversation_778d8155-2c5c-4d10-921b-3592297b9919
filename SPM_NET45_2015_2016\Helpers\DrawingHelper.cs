﻿using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;

namespace SPM_NET45_2015_2016.Helpers
{
    public static class DrawingHelper
    {
        #region AutoCAD Properties
        private static Document ActiveDocument => Application.DocumentManager.MdiActiveDocument;
        private static Database ActiveDatabase => ActiveDocument?.Database;
        private static Editor ActiveEditor => ActiveDocument?.Editor;
        #endregion

        #region Point Settings
        public static void SetPointStyle(int pdmode, double pdsize)
        {
            using (ActiveDocument.LockDocument())
            {
                ActiveDatabase.Pdmode = pdmode;
                ActiveDatabase.Pdsize = pdsize;
            }
        }

        public static int GetPointMode() => ActiveDocument == null ? 0 : ActiveDatabase.Pdmode;
        public static double GetPointSize() => ActiveDocument == null ? 0.0 : ActiveDatabase.Pdsize;
        #endregion

        /*
        #region Object Snap Helpers
        /// <summary>
        /// Ensures running Osnap (F3) is enabled and Node (Point) snap bit is checked.
        /// </summary>
        public static void EnableNodeSnap()
        {
            using (ActiveDocument.LockDocument())
            {
                // Ensure F3 Osnap is on
                const short SnapModeOn = 1;
                short snapMode = (short)Application.GetSystemVariable("SNAPMODE");
                if (snapMode == 0)
                {
                    Application.SetSystemVariable("SNAPMODE", SnapModeOn);
                    ActiveEditor.WriteMessage("\nOSNAP (running snaps) was off—now enabled.");
                }

                // Set the Node (Point) snap bit in OSMODE
                const short NodeMask = 8;  // Node snap
                short osmode = (short)Application.GetSystemVariable("OSMODE");
                if ((osmode & NodeMask) == 0)
                {
                    Application.SetSystemVariable("OSMODE", (short)(osmode | NodeMask));
                    ActiveEditor.WriteMessage("\nNode snap has been enabled.");
                }
                else
                {
                    ActiveEditor.WriteMessage("\nNode snap was already enabled.");
                }
            }
        }
        #endregion

        */


        #region Object Snap Helpers
        /// <summary>
        /// Ensures running OSNAP (F3) is on and adds Node (8) + Insertion (64)
        /// to whatever snaps the user already has enabled.
        /// </summary>
        public static void EnsureOsnapNodeAndInsertion()
        {
            // Get document & editor
            var doc = Application.DocumentManager.MdiActiveDocument;
            var ed = doc.Editor;

            // Read current OSMODE (bitmask of active snaps)
            short osnap = (short)Application.GetSystemVariable("OSMODE");

            // 1) Clear the “suppress all snaps” flag if present (bit 16384)
            const short SuppressAll = 0x4000;
            if ((osnap & SuppressAll) != 0)
            {
                osnap &= (short)~SuppressAll;
            }

            // 2) Bits we want to add
            const short NodeBit = 8;   // Point snap
            const short InsertionBit = 64;  // Insertion-point snap

            // Check what was already on
            bool hadNode = (osnap & NodeBit) != 0;
            bool hadInsertion = (osnap & InsertionBit) != 0;

            // 3) OR in our two bits (leaves all other bits untouched)
            osnap |= (short)(NodeBit | InsertionBit);

            // 4) Write back the updated mask
            Application.SetSystemVariable("OSMODE", osnap);

            // 5) Report to the user
            ed.WriteMessage(hadNode
                ? "\nNode snap was already enabled."
                : "\nNode snap has been enabled.");
            ed.WriteMessage(hadInsertion
                ? "\nInsertion snap was already enabled."
                : "\nInsertion snap has been enabled.");
        }
        #endregion




    }
}
