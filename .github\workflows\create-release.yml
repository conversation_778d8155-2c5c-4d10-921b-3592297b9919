name: 📦 Create Private Release for Autodesk App Store Reference

on:
  push:
    tags:
      - 'v*'  # Triggers on version tags like v1.0.0, v2.1.0, etc.
      - 'autodesk-*'  # Also triggers on autodesk-v1.0.0, autodesk-v2.1.0, etc.

jobs:
  create-release:
    runs-on: windows-latest
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
      
    - name: 🔧 Setup .NET 8
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '8.0.x'
        
    - name: 🔧 Setup MSBuild
      uses: microsoft/setup-msbuild@v2
      
    - name: 📦 Restore NuGet Packages
      run: nuget restore SurveyPointsManager.sln
        
    - name: 🏗️ Build All Projects
      run: |
        echo "🔨 Building all AutoCAD versions..."
        msbuild SurveyPointsManager.sln /p:Configuration=Release /p:Platform="Any CPU"
        
    - name: 📁 Create Release Bundle
      run: |
        echo "📁 Creating release bundle structure..."
        
        # Create bundle directories
        New-Item -ItemType Directory -Force -Path "Release/SurveyPointsManager.bundle/Contents/Windows/AutoCAD2015-2016_NETFramework45"
        New-Item -ItemType Directory -Force -Path "Release/SurveyPointsManager.bundle/Contents/Windows/AutoCAD2017-2018_NETFramework46"
        New-Item -ItemType Directory -Force -Path "Release/SurveyPointsManager.bundle/Contents/Windows/AutoCAD2019-2020_NETFramework47"
        New-Item -ItemType Directory -Force -Path "Release/SurveyPointsManager.bundle/Contents/Windows/AutoCAD2021-2024_NETFramework48"
        New-Item -ItemType Directory -Force -Path "Release/SurveyPointsManager.bundle/Contents/Windows/AutoCAD2025-2026_NET8"
        New-Item -ItemType Directory -Force -Path "Release/SurveyPointsManager.bundle/Contents/Resources"
        New-Item -ItemType Directory -Force -Path "Release/SurveyPointsManager.bundle/Contents/Help"
        
        # Copy PackageContents.xml from the Deployment folder
        Copy-Item "Deployment/SurveyPointsManager.bundle/PackageContents.xml" "Release/SurveyPointsManager.bundle/" -ErrorAction SilentlyContinue
        # Fallback to root if Deployment folder doesn't exist
        if (-not (Test-Path "Release/SurveyPointsManager.bundle/PackageContents.xml")) {
            Copy-Item "SurveyPointsManager.bundle/PackageContents.xml" "Release/SurveyPointsManager.bundle/" -ErrorAction SilentlyContinue
        }
        
        # Copy Help file
        Copy-Item "Deployment/SurveyPointsManager.bundle/Contents/Help/SurveyPointManagerHelp.htm" "Release/SurveyPointsManager.bundle/Contents/Help/" -ErrorAction SilentlyContinue
        
        # Function to copy complete Release folder
        function Copy-CompleteReleaseFolder {
            param($SourcePath, $DestinationPath, $ProjectName, $MainDllName)
            
            if (Test-Path $SourcePath) {
                echo "✅ Copying complete Release folder for $ProjectName..."
                echo "   📂 Source: $SourcePath"
                echo "   📂 Destination: $DestinationPath"
                
                # Get all files in the source Release folder
                $sourceFiles = Get-ChildItem $SourcePath -File
                echo "   📋 Found $($sourceFiles.Count) files to copy:"
                
                foreach ($file in $sourceFiles) {
                    echo "     - $($file.Name) ($($file.Length) bytes)"
                    
                    # Determine destination filename
                    $destFileName = $file.Name
                    if ($file.Name -eq $MainDllName) {
                        $destFileName = "PointFlowCAD.dll"
                        echo "     🔄 Renaming $($file.Name) to PointFlowCAD.dll"
                    }
                    
                    # Copy the file
                    $destPath = Join-Path $DestinationPath $destFileName
                    try {
                        Copy-Item $file.FullName $destPath -Force
                        echo "     ✅ Copied: $($file.Name) -> $destFileName"
                    } catch {
                        echo "     ❌ Failed to copy $($file.Name): $($_.Exception.Message)"
                    }
                }
                
                # Verify what was copied
                $copiedFiles = Get-ChildItem $DestinationPath -File -ErrorAction SilentlyContinue
                echo "   📊 Final result: $($copiedFiles.Count) files in destination"
                $copiedFiles | ForEach-Object { echo "     ✅ $($_.Name) ($($_.Length) bytes)" }
                
            } else {
                echo "⚠️ Warning: $SourcePath not found, skipping $ProjectName"
            }
        }
        
        # Copy complete Release folders for each AutoCAD version
        Copy-CompleteReleaseFolder "SPM_NET45_2015_2016/bin/Release" "Release/SurveyPointsManager.bundle/Contents/Windows/AutoCAD2015-2016_NETFramework45" "AutoCAD 2015-2016" "SPM_NET45_2015_2016.dll"
        Copy-CompleteReleaseFolder "SPM_NET46_2017_2018/bin/Release" "Release/SurveyPointsManager.bundle/Contents/Windows/AutoCAD2017-2018_NETFramework46" "AutoCAD 2017-2018" "SPM_NET46_2017_2018.dll"
        Copy-CompleteReleaseFolder "SPM_NET47_2019_2020/bin/Release" "Release/SurveyPointsManager.bundle/Contents/Windows/AutoCAD2019-2020_NETFramework47" "AutoCAD 2019-2020" "SPM_NET47_2019_2020.dll"
        Copy-CompleteReleaseFolder "SPM_NET48_2021_2024/bin/Release" "Release/SurveyPointsManager.bundle/Contents/Windows/AutoCAD2021-2024_NETFramework48" "AutoCAD 2021-2024" "SPM_NET48_2021_2024.dll"
        Copy-CompleteReleaseFolder "SPM_NET8_2025_2026/bin/Release/net8.0-windows" "Release/SurveyPointsManager.bundle/Contents/Windows/AutoCAD2025-2026_NET8" "AutoCAD 2025-2026" "SPM_NET8_2025_2026.dll"
        
        # Copy Resources from Deployment folder if available
        if (Test-Path "Deployment/SurveyPointsManager.bundle/Contents/Resources") {
            echo "📁 Copying Resources from Deployment folder..."
            Copy-Item "Deployment/SurveyPointsManager.bundle/Contents/Resources/*" "Release/SurveyPointsManager.bundle/Contents/Resources/" -Recurse -Force -ErrorAction SilentlyContinue
        }
        
    - name: 📋 Verify Bundle Contents
      run: |
        echo "📋 Verifying complete bundle contents..."
        if (Test-Path "Release/SurveyPointsManager.bundle") {
            echo "🎯 Complete bundle structure:"
            Get-ChildItem -Recurse "Release/SurveyPointsManager.bundle" | ForEach-Object {
                if ($_.PSIsContainer) {
                    echo "📁 $($_.FullName.Replace((Get-Location).Path + '\Release\', ''))"
                } else {
                    echo "📄 $($_.FullName.Replace((Get-Location).Path + '\Release\', '')) ($($_.Length) bytes)"
                }
            }
            
            # Summary count
            $allFiles = Get-ChildItem -Recurse "Release/SurveyPointsManager.bundle" -File
            $allFolders = Get-ChildItem -Recurse "Release/SurveyPointsManager.bundle" -Directory
            echo ""
            echo "📊 Bundle Summary:"
            echo "   📁 Total folders: $($allFolders.Count)"
            echo "   📄 Total files: $($allFiles.Count)"
            echo "   💾 Total size: $([math]::Round(($allFiles | Measure-Object Length -Sum).Sum / 1MB, 2)) MB"
        } else {
            echo "❌ Bundle folder not found!"
        }
        
    - name: 📦 Create Release Package
      run: |
        echo "📦 Creating release package..."
        
        # Create the main bundle zip
        Compress-Archive -Path "Release/SurveyPointsManager.bundle" -DestinationPath "SurveyPointsManager-${{ github.ref_name }}.zip" -Force
        
        # Create installation package with documentation
        New-Item -ItemType Directory -Force -Path "Installation"
        Copy-Item "SurveyPointsManager-${{ github.ref_name }}.zip" "Installation/"
        Copy-Item "README.md" "Installation/" -ErrorAction SilentlyContinue
        Copy-Item "INSTALLATION_GUIDE.md" "Installation/" -ErrorAction SilentlyContinue
        Copy-Item "USER_GUIDE.md" "Installation/" -ErrorAction SilentlyContinue
        Copy-Item "CHANGELOG.md" "Installation/" -ErrorAction SilentlyContinue
        Copy-Item "LICENSE" "Installation/" -ErrorAction SilentlyContinue
        
        Compress-Archive -Path "Installation/*" -DestinationPath "SurveyPointsManager-Complete-${{ github.ref_name }}.zip" -Force
        
    - name: 📋 Generate Release Notes
      run: |
        echo "📋 Generating release notes..."
        $version = "${{ github.ref_name }}"
        $releaseNotes = @"
        # 📦 Survey Points Manager $version - Autodesk App Store Reference

        **🔒 PRIVATE RELEASE - For Autodesk App Store Submission Reference Only**

        ## 🎯 Purpose
        This release is created for:
        - **Autodesk App Store submission reference**
        - **Version tracking for published versions**
        - **Internal development milestone**
        - **NOT for public customer distribution**

        ## 📦 Bundle Contents
        - **SurveyPointsManager.bundle** - Complete AutoCAD plugin bundle
        - **All Release Files** - Complete with dependencies, config files, PDB files
        - **Resources** - Icons, UI files, help documentation
        - **PackageContents.xml** - Configured for all AutoCAD versions

        ## ✅ AutoCAD Version Support
        - **AutoCAD 2015-2016** (.NET Framework 4.5) - Complete Release folder
        - **AutoCAD 2017-2018** (.NET Framework 4.6) - Complete Release folder
        - **AutoCAD 2019-2020** (.NET Framework 4.7) - Complete Release folder
        - **AutoCAD 2021-2024** (.NET Framework 4.8) - Complete Release folder
        - **AutoCAD 2025-2026** (.NET 8.0) - Complete Release folder

        ## 🏪 Autodesk App Store Details
        - **App Store ID:** 5391612840032124388
        - **Command:** SURVEYPOINTMANAGER
        - **Bundle Format:** AutoCAD .bundle (industry standard)
        - **Automatic Version Detection:** Yes

        ## 🔧 Technical Build Info
        - **Build Date:** $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
        - **Commit SHA:** ${{ github.sha }}
        - **Build Type:** Release (production-ready)
        - **Dependencies:** Complete with all required files
        - **Configuration:** All .dll.config and .deps.json files included
        - **Debug Symbols:** PDB files included for troubleshooting

        ## 📋 Submission Checklist
        - ✅ All AutoCAD versions supported (2015-2026)
        - ✅ Official App Store ID configured
        - ✅ Complete dependencies included (not just DLLs)
        - ✅ Professional bundle structure
        - ✅ Help documentation included
        - ✅ Icons and UI resources included
        - ✅ Configuration files included
        - ✅ Debug symbols included

        ## 🔒 Access Level
        **PRIVATE** - Only visible to repository collaborators

        ---
        *Automated build with complete Release folders* 🏪
        "@
        $releaseNotes | Out-File -FilePath "release-notes.md" -Encoding UTF8
        
    - name: 🔒 Create Private Release for Autodesk Reference
      uses: softprops/action-gh-release@v1
      with:
        name: 📦 SPM ${{ github.ref_name }} - Autodesk App Store Reference
        body_path: release-notes.md
        files: |
          SurveyPointsManager-${{ github.ref_name }}.zip
          SurveyPointsManager-Complete-${{ github.ref_name }}.zip
        draft: false
        prerelease: true
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        
    - name: 🎊 Success Notification
      run: |
        echo "🎊 SUCCESS! Release ${{ github.ref_name }} created successfully!"
        echo "📦 Bundle: SurveyPointsManager-${{ github.ref_name }}.zip"
        echo "📚 Complete Package: SurveyPointsManager-Complete-${{ github.ref_name }}.zip"
        echo "🚀 Ready for customer distribution with complete Release folders!"