# Survey Points Manager - GitHub Repository Setup Script
# PowerShell version for better error handling

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Survey Points Manager - GitHub Setup" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if Git is installed
try {
    $gitVersion = git --version
    Write-Host "✅ Git is installed: $gitVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ ERROR: Git is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Git from: https://git-scm.com/download/win" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Get repository URL from user
do {
    $repoUrl = Read-Host "Enter your GitHub repository URL (e.g., https://github.com/username/survey-points-manager.git)"
    if ([string]::IsNullOrWhiteSpace($repoUrl)) {
        Write-Host "❌ Repository URL is required" -ForegroundColor Red
    }
} while ([string]::IsNullOrWhiteSpace($repoUrl))

Write-Host ""
Write-Host "Repository URL: $repoUrl" -ForegroundColor Yellow
Write-Host ""

# Confirm current directory
$currentDir = Get-Location
Write-Host "Current directory: $currentDir" -ForegroundColor Yellow
$confirm = Read-Host "Is this the correct directory with your Survey Points Manager files? (y/n)"
if ($confirm -ne 'y' -and $confirm -ne 'Y') {
    Write-Host "Please navigate to the correct directory and run this script again." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

try {
    # Initialize Git repository
    Write-Host "🔄 Initializing Git repository..." -ForegroundColor Blue
    git init
    if ($LASTEXITCODE -ne 0) { throw "Failed to initialize Git repository" }

    # Add all files
    Write-Host "🔄 Adding all files to Git..." -ForegroundColor Blue
    git add .
    if ($LASTEXITCODE -ne 0) { throw "Failed to add files to Git" }

    # Check if there are files to commit
    $stagedFiles = git diff --cached --name-only
    if (-not $stagedFiles) {
        Write-Host "❌ No files to commit. Please check if files are in the directory." -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }

    Write-Host "📁 Files to be committed:" -ForegroundColor Green
    $stagedFiles | ForEach-Object { Write-Host "  - $_" -ForegroundColor Gray }
    Write-Host ""

    # Create initial commit
    Write-Host "🔄 Creating initial commit..." -ForegroundColor Blue
    $commitMessage = @"
Initial commit: Survey Points Manager complete solution

- Added SPM_NET8_2025_2026 (.NET 8 for AutoCAD 2025/2026)
- Added legacy versions for AutoCAD 2016-2024
- Complete documentation suite (README, USER_GUIDE, INSTALLATION_GUIDE, etc.)
- Professional PackageContents.xml with automatic version detection
- Bundle structure for automatic AutoCAD version loading
- Commercial licensing and customer support documentation
- Deployment guide for developers
- Roadmap for future development
"@

    git commit -m $commitMessage
    if ($LASTEXITCODE -ne 0) { throw "Failed to create commit" }

    # Set main branch
    Write-Host "🔄 Setting main branch..." -ForegroundColor Blue
    git branch -M main
    if ($LASTEXITCODE -ne 0) { throw "Failed to set main branch" }

    # Add remote origin
    Write-Host "🔄 Adding remote origin..." -ForegroundColor Blue
    git remote add origin $repoUrl
    if ($LASTEXITCODE -ne 0) { throw "Failed to add remote origin" }

    # Push to GitHub
    Write-Host "🔄 Pushing to GitHub..." -ForegroundColor Blue
    Write-Host "Note: You may be prompted for GitHub authentication..." -ForegroundColor Yellow
    git push -u origin main
    if ($LASTEXITCODE -ne 0) { throw "Failed to push to GitHub" }

    # Success message
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "✅ SUCCESS! Repository created successfully!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Your Survey Points Manager repository is now available at:" -ForegroundColor Green
    Write-Host "$repoUrl" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "1. Go to your GitHub repository" -ForegroundColor White
    Write-Host "2. Check that all files are uploaded correctly" -ForegroundColor White
    Write-Host "3. Review the README.md file" -ForegroundColor White
    Write-Host "4. Set up repository settings (if needed)" -ForegroundColor White
    Write-Host ""

} catch {
    Write-Host ""
    Write-Host "❌ ERROR: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "This might be due to:" -ForegroundColor Yellow
    Write-Host "1. Authentication issues (need to login to GitHub)" -ForegroundColor White
    Write-Host "2. Repository doesn't exist on GitHub" -ForegroundColor White
    Write-Host "3. Network connectivity issues" -ForegroundColor White
    Write-Host "4. Permission issues" -ForegroundColor White
    Write-Host ""
    Write-Host "Please check your GitHub authentication and try again." -ForegroundColor Yellow
    Write-Host ""
}

Read-Host "Press Enter to exit"
