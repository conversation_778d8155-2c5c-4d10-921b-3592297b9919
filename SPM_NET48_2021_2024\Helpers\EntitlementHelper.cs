﻿using System;
using System.IO;
using SPM_NET48_2021_2024.Services;

namespace SPM_NET48_2021_2024.Helpers
{
    public static class EntitlementHelper
    {
        private const string EntitlementFileName = "entitlement.json";
        private const int OfflineDays = 30; // Allow offline use for 30 days

        public static bool CheckEntitlement(string userId, string appId)
        {
            var storedEntitlement = ReadStoredEntitlement();

            if (storedEntitlement != null &&
                storedEntitlement.UserId == userId &&
                storedEntitlement.AppId == appId &&
                (DateTime.Now - storedEntitlement.LastChecked).TotalDays <= OfflineDays)
            {
                return storedEntitlement.IsValid;
            }

            var entitlementResponse = EntitlementService.CheckEntitlement(userId, appId);

            if (entitlementResponse.IsValid)
            {
                StoreEntitlement(new StoredEntitlement
                {
                    UserId = userId,
                    AppId = appId,
                    IsValid = true,
                    LastChecked = DateTime.Now
                });
            }

            return entitlementResponse.IsValid;
        }

        private static void StoreEntitlement(StoredEntitlement entitlement)
        {
            //string json = JsonConvert.SerializeObject(entitlement);
            //File.WriteAllText(EntitlementFileName, json);
        }

        private static StoredEntitlement ReadStoredEntitlement()
        {
            if (File.Exists(EntitlementFileName))
            {
                //string json = File.ReadAllText(EntitlementFileName);
                //return JsonConvert.DeserializeObject<StoredEntitlement>(json);
            }
            return null;
        }
    }

    public class StoredEntitlement
    {
        public string UserId { get; set; }
        public string AppId { get; set; }
        public bool IsValid { get; set; }
        public DateTime LastChecked { get; set; }
    }
}