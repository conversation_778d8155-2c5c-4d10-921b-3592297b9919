<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="SPM_NET46_2017_2018.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <Setting Name="PointLayer" Type="System.String" Scope="User">
      <Value Profile="(Default)">spm_Point</Value>
    </Setting>
    <Setting Name="PointLayerColor" Type="System.Int16" Scope="User">
      <Value Profile="(Default)">2</Value>
    </Setting>
    <Setting Name="PointNumberLayer" Type="System.String" Scope="User">
      <Value Profile="(Default)">spm_PointNumber</Value>
    </Setting>
    <Setting Name="PointNumberLayerColor" Type="System.Int16" Scope="User">
      <Value Profile="(Default)">1</Value>
    </Setting>
    <Setting Name="TableLayer" Type="System.String" Scope="User">
      <Value Profile="(Default)">spm_table</Value>
    </Setting>
    <Setting Name="TableLayerColor" Type="System.Int16" Scope="User">
      <Value Profile="(Default)">3</Value>
    </Setting>
    <Setting Name="PointDisplayImage" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">35</Value>
    </Setting>
    <Setting Name="PointSize" Type="System.Double" Scope="User">
      <Value Profile="(Default)">0.25</Value>
    </Setting>
    <Setting Name="PointSizeMode" Type="System.String" Scope="User">
      <Value Profile="(Default)">Set Size Relative to Screen</Value>
    </Setting>
    <Setting Name="PointNumberTextHeight" Type="System.Double" Scope="User">
      <Value Profile="(Default)">0.18</Value>
    </Setting>
    <Setting Name="TableStyleName" Type="System.String" Scope="User">
      <Value Profile="(Default)">spm_TableStyle</Value>
    </Setting>
    <Setting Name="MaxRowPerTable" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">15</Value>
    </Setting>
    <Setting Name="ShowTableTitle" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="TableTitle" Type="System.String" Scope="User">
      <Value Profile="(Default)">Survey Points</Value>
    </Setting>
    <Setting Name="ShowPointNumber" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="ShowElevation" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="ShowDescription" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="RecordHistory" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="RecordHistoryLimit" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">5000</Value>
    </Setting>
    <Setting Name="Theme" Type="System.String" Scope="User">
      <Value Profile="(Default)">System</Value>
    </Setting>
  </Settings>
</SettingsFile>