﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Input;

namespace SPM_NET45_2015_2016.Services
{
    // Helper class to replace tuple
    public class DataGridExtractionResult
    {
        public List<string> Points { get; set; }
        public List<string> Headers { get; set; }

        public DataGridExtractionResult(List<string> points, List<string> headers)
        {
            Points = points;
            Headers = headers;
        }
    }

    public class UIService
    {
        public static DataGridExtractionResult ExtractPointsFromDataGrid(DataGrid pointsDataGrid)
        {
            var points = new List<string>();
            var headers = new List<string>();

            // Extract column headers
            foreach (var column in pointsDataGrid.Columns)
            {
                headers.Add(column.Header.ToString());
            }

            // Iterate through rows
            foreach (var item in pointsDataGrid.Items)
            {
                if (item != null)
                {
                    var rowValues = pointsDataGrid.Columns.Select(column =>
                    {
                        var cellContent = column.GetCellContent(item);
                        return cellContent != null ? (cellContent as TextBlock)?.Text ?? string.Empty : string.Empty;
                    }).ToArray();

                    // Join all cell values in the row into a single comma-separated string
                    points.Add(string.Join(", ", rowValues));
                }
            }

            return new DataGridExtractionResult(points, headers);
        }

        public static void SetupNumberValidationTextBox(TextBox textBox)
        {
            textBox.PreviewTextInput += NumberValidationTextBox;
            System.Windows.DataObject.AddPastingHandler(textBox, OnPasteHandler);
        }

        private static void NumberValidationTextBox(object sender, TextCompositionEventArgs e)
        {
            e.Handled = !IsTextAllowed(e.Text);
        }

        private static void OnPasteHandler(object sender, DataObjectPastingEventArgs e)
        {
            if (e.DataObject.GetDataPresent(typeof(string)))
            {
                string text = (string)e.DataObject.GetData(typeof(string));
                if (!IsTextAllowed(text))
                {
                    e.CancelCommand();
                }
            }
            else
            {
                e.CancelCommand();
            }
        }

        private static bool IsTextAllowed(string text)
        {
            return System.Text.RegularExpressions.Regex.IsMatch(text, @"^[-+]?[0-9]*\.?[0-9]+$");
        }


        private Dictionary<string, IList> _backupData = new Dictionary<string, IList>();

        public void ClearDataGrid(DataGrid dataGrid, string key)
        {
            if (dataGrid == null)
                throw new ArgumentNullException(nameof(dataGrid));

            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("Key cannot be null or empty", nameof(key));

            var itemsSource = dataGrid.ItemsSource as IList;
            if (itemsSource == null)
                throw new InvalidOperationException("DataGrid's ItemsSource is not an IList");

            // Backup current data
            _backupData[key] = new ArrayList(itemsSource);

            // Clear the DataGrid
            itemsSource.Clear();

            // Refresh the view
            CollectionViewSource.GetDefaultView(dataGrid.ItemsSource).Refresh();
        }

        public void UndoClearDataGrid(DataGrid dataGrid, string key)
        {
            if (dataGrid == null)
                throw new ArgumentNullException(nameof(dataGrid));

            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("Key cannot be null or empty", nameof(key));

            if (!_backupData.ContainsKey(key) || _backupData[key].Count == 0)
                return; // Nothing to undo

            var itemsSource = dataGrid.ItemsSource as IList;
            if (itemsSource == null)
                throw new InvalidOperationException("DataGrid's ItemsSource is not an IList");

            // Clear current items (if any) and restore the backed up data
            itemsSource.Clear();
            foreach (var item in _backupData[key])
            {
                itemsSource.Add(item);
            }

            // Refresh the view
            CollectionViewSource.GetDefaultView(dataGrid.ItemsSource).Refresh();

            // Remove the backup
            _backupData.Remove(key);
        }
    }
}