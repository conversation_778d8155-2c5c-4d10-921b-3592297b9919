﻿using SPM_NET48_2021_2024.Models;
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Windows;
using System.Windows.Threading;
using System.Xml.Serialization;

namespace SPM_NET48_2021_2024.Managers
{
    public class HistoryManager : INotifyPropertyChanged
    {
        private const int MaxRecords = 5000;
        private static readonly string HistoryFilePath =
            Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SurveyPointManager", "history.xml");

        private static HistoryManager _instance;
        public static HistoryManager Instance => _instance ?? (_instance = new HistoryManager());

        private ObservableCollection<HistoryRecord> _historyRecords = new ObservableCollection<HistoryRecord>();
        public ObservableCollection<HistoryRecord> HistoryRecords
        {
            get { return _historyRecords; }
            private set
            {
                _historyRecords = value;
                OnPropertyChanged(nameof(HistoryRecords));
                OnPropertyChanged(nameof(HistoryRecordCounter));
            }
        }

        // History record counter property.
        public int HistoryRecordCounter => HistoryRecords?.Count ?? 0;

        // Backup for undo operations.
        private ObservableCollection<HistoryRecord> _backupHistoryRecords = new ObservableCollection<HistoryRecord>();

        private HistoryManager()
        {
            LoadHistory();
        }

        /// <summary>
        /// Adds a new history record, ensuring that the maximum number of records is not exceeded.
        /// </summary>
        public void AddRecord(string fileName, string operationType, string fileType, int pointCount)
        {
            var newRecord = new HistoryRecord(fileName, operationType, fileType, pointCount);

            // Use the current dispatcher to update the collection.
            Dispatcher.CurrentDispatcher.Invoke(() =>
            {
                HistoryRecords.Insert(0, newRecord);

                if (HistoryRecords.Count > MaxRecords)
                {
                    HistoryRecords.RemoveAt(HistoryRecords.Count - 1);
                }

                SaveHistory();
                RefreshHistory();
            });
        }

        /// <summary>
        /// Backs up the current history collection for undo purposes.
        /// </summary>
        public void BackupCurrentHistory()
        {
            _backupHistoryRecords = new ObservableCollection<HistoryRecord>(HistoryRecords);
        }

        /// <summary>
        /// Restores the most recent backup.
        /// </summary>
        public void RestoreBackupHistory()
        {
            if (_backupHistoryRecords != null && _backupHistoryRecords.Count > 0)
            {
                Dispatcher.CurrentDispatcher.Invoke(() =>
                {
                    HistoryRecords.Clear();
                    foreach (var record in _backupHistoryRecords)
                    {
                        HistoryRecords.Add(record);
                    }
                    SaveHistory();
                    RefreshHistory();
                    _backupHistoryRecords.Clear();
                });
            }
        }

        /// <summary>
        /// Clears all history records (after backing up the current state).
        /// </summary>
        public void ClearHistory()
        {
            // Backup current records before clearing.
            _backupHistoryRecords = new ObservableCollection<HistoryRecord>(HistoryRecords);

            Dispatcher.CurrentDispatcher.Invoke(() =>
            {
                HistoryRecords.Clear();
                SaveHistory();
                RefreshHistory();
            });
        }

        private void SaveHistory()
        {
            try
            {
                Directory.CreateDirectory(Path.GetDirectoryName(HistoryFilePath));
                using (StreamWriter writer = new StreamWriter(HistoryFilePath))
                {
                    XmlSerializer serializer = new XmlSerializer(typeof(ObservableCollection<HistoryRecord>));
                    serializer.Serialize(writer, HistoryRecords);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to save history: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadHistory()
        {
            try
            {
                if (File.Exists(HistoryFilePath))
                {
                    using (StreamReader reader = new StreamReader(HistoryFilePath))
                    {
                        XmlSerializer serializer = new XmlSerializer(typeof(ObservableCollection<HistoryRecord>));
                        HistoryRecords = (ObservableCollection<HistoryRecord>)serializer.Deserialize(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to load history: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Raises the property changed event for the specified property.
        /// </summary>
        public void RefreshHistory()
        {
            OnPropertyChanged(nameof(HistoryRecords));
            OnPropertyChanged(nameof(HistoryRecordCounter));
        }

        public event PropertyChangedEventHandler PropertyChanged;
        private void OnPropertyChanged(string propertyName) =>
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

}
