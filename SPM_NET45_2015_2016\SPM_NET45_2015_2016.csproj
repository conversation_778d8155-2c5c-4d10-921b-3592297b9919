﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{E51FE1A2-CF3E-4BA5-8F9B-8755CA7E4E90}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>SPM_NET45_2015_2016</RootNamespace>
    <AssemblyName>SPM_NET45_2015_2016</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AcCoreMgd">
      <HintPath>..\..\..\..\..\..\Autodesk\ObjectARX 2016\inc\AcCoreMgd.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="AcDbMgd">
      <HintPath>..\..\..\..\..\..\Autodesk\ObjectARX 2016\inc\AcDbMgd.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="AcMgd">
      <HintPath>..\..\..\..\..\..\Autodesk\ObjectARX 2016\inc\AcMgd.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Management" />
    <Reference Include="System.Security" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xaml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Constants\PluginDefaults.cs" />
    <Compile Include="Helpers\AutoCADHelper.cs" />
    <Compile Include="Helpers\CSVHelper.cs" />
    <Compile Include="Helpers\DrawingHelper.cs" />
    <Compile Include="Helpers\EntitlementHelper.cs" />
    <Compile Include="Helpers\GSIHelper.cs" />
    <Compile Include="Helpers\KDTree.cs" />
    <Compile Include="Helpers\TXTHelper.cs" />
    <Compile Include="Managers\EntitlementManager.cs" />
    <Compile Include="Managers\HistoryManager.cs" />
    <Compile Include="Managers\LayerManager.cs" />
    <Compile Include="Managers\TableManager.cs" />
    <Compile Include="Models\CoordinateConversion.cs" />
    <Compile Include="Models\EntitlementResponse.cs" />
    <Compile Include="Models\HistoryRecord.cs" />
    <Compile Include="Models\IDXFileData.cs" />
    <Compile Include="Models\SDRModels.cs" />
    <Compile Include="Models\SurveyPoint.cs" />
    <Compile Include="PartialViews\Header.xaml.cs">
      <DependentUpon>Header.xaml</DependentUpon>
    </Compile>
    <Compile Include="PluginMain.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings1.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="Services\DrawingService.cs" />
    <Compile Include="Services\EntitlementService.cs" />
    <Compile Include="Services\ExportService.cs" />
    <Compile Include="Services\FileService.cs" />
    <Compile Include="Services\ImportService.cs" />
    <Compile Include="Services\SettingsService.cs" />
    <Compile Include="Services\UIService.cs" />
    <Compile Include="Utils\ExperimentalEvents.cs" />
    <Compile Include="Utils\LayerNameValidator.cs" />
    <Compile Include="Utils\SecurityUtils.cs" />
    <Compile Include="Utils\TableStyleNameValidator.cs" />
    <Compile Include="Views\EntitlementPromptControl.xaml.cs">
      <DependentUpon>EntitlementPromptControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\ExportPalette.xaml.cs">
      <DependentUpon>ExportPalette.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\HistoryPalette.xaml.cs">
      <DependentUpon>HistoryPalette.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\HomePalette.xaml.cs">
      <DependentUpon>HomePalette.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\ImportPalette.xaml.cs">
      <DependentUpon>ImportPalette.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\SettingsPalette.xaml.cs">
      <DependentUpon>SettingsPalette.xaml</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="Fonts\Poppins-Regular.ttf" />
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings1.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Page Include="PartialViews\Header.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="ResourceDictionary.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\EntitlementPromptControl.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\ExportPalette.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\HistoryPalette.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\HomePalette.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\ImportPalette.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\SettingsPalette.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Plugin Small Logo Illustrator 32x32.ico" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>