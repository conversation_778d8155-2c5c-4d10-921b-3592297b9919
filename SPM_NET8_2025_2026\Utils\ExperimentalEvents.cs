﻿using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.Runtime;
using System.Windows;
using AcadApplication = Autodesk.AutoCAD.ApplicationServices.Application;
using AcadDocument = Autodesk.AutoCAD.ApplicationServices.Document;

namespace SPM_NET8_2025_2026.Utils
{
    internal class ExperimentalEvents
    {
        [CommandMethod("MonitorCommandEvents")]
        public void MonitorCommandEvents_Method()
        {
            SubscribeToDoc(AcadApplication.DocumentManager.MdiActiveDocument);
        }

        public static void SubscribeToDoc(AcadDocument doc)
        {
            doc.CommandWillStart += new CommandEventHandler(doc_CommandWillStart);
            doc.CommandEnded += new CommandEventHandler(doc_CommandEnded);
            doc.CommandCancelled += new CommandEventHandler(doc_CommandCancelled);
            doc.CommandFailed += new CommandEventHandler(doc_CommandFailed);
            doc.UnknownCommand += new UnknownCommandEventHandler(doc_UnknownCommand);
        }

        static void doc_UnknownCommand(object sender, UnknownCommandEventArgs e)
        {
            (sender as AcadDocument).Editor.WriteMessage(string.Format("\nCommand {0} unknown.\n", e.GlobalCommandName));
        }

        static void doc_CommandFailed(object sender, CommandEventArgs e)
        {
            (sender as AcadDocument).Editor.WriteMessage(string.Format("\nCommand {0} failed.\n", e.GlobalCommandName));
        }

        static void doc_CommandCancelled(object sender, CommandEventArgs e)
        {
            (sender as AcadDocument).Editor.WriteMessage(string.Format("\nCommand {0} cancelled.\n", e.GlobalCommandName));
        }

        static void doc_CommandEnded(object sender, CommandEventArgs e)
        {
            (sender as AcadDocument).Editor.WriteMessage(string.Format("\nCommand {0} ended.\n", e.GlobalCommandName));

            if (e.GlobalCommandName == "PTYPE")
            {
                // Do something specific for the LINE command
                System.Windows.MessageBox.Show("The PTYPE command has ended.");
            }
        }

        static void doc_CommandWillStart(object sender, CommandEventArgs e)
        {
            (sender as AcadDocument).Editor.WriteMessage(string.Format("\nCommand {0} will start.\n", e.GlobalCommandName));
        }
    }
}
