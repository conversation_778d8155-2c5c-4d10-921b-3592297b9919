# Survey Points Manager (SPM) - .NET 8 Edition

## Overview

Survey Points Manager (SPM) is a comprehensive AutoCAD plugin designed for surveyors, engineers, and CAD professionals to efficiently import, manage, export, and visualize survey point data directly within AutoCAD. This .NET 8 edition provides enhanced performance and compatibility with AutoCAD 2025 and 2026.

## 🚀 Features

### Import Capabilities
- **Multiple File Formats**: CSV, TXT, KML, SDR, IDX, GSI (8-bit and 16-bit)
- **Smart Format Detection**: Automatic detection of file formats and coordinate systems
- **Flexible Column Mapping**: Customizable field mapping for different data structures
- **Coordinate System Support**: UTM zones, hemispheres, and various projection systems
- **Data Validation**: Real-time validation and error reporting during import

### Export Capabilities
- **Multiple Output Formats**: CSV, TXT, KML, SDR, IDX, GSI
- **Customizable Export Settings**: Headers, point numbering, coordinate order (XY/YX)
- **Batch Export**: Export large datasets efficiently
- **Format-Specific Options**: Tailored settings for each export format

### Point Management
- **Interactive Point Picking**: Manual and automatic point selection tools
- **Smart Point Numbering**: Intelligent sequential numbering with customizable starting points
- **Point Editing**: In-place editing of coordinates, elevations, and descriptions
- **Search and Filter**: Advanced search capabilities across all point attributes
- **Undo/Redo**: Full undo/redo support for all operations

### AutoCAD Integration
- **Drawing Tools**: Direct point insertion into AutoCAD drawings
- **Table Generation**: Create formatted tables in model space
- **Layer Management**: Automatic layer creation and management
- **OSNAP Integration**: Enhanced object snap functionality
- **Block and Text Processing**: Smart handling of existing drawing elements

### Advanced Features
- **History Management**: Complete operation history with search capabilities
- **Settings Management**: Persistent user preferences and configurations
- **Entitlement System**: License management and validation
- **Performance Optimization**: Efficient handling of large datasets

## 🔧 Technical Specifications

- **Framework**: .NET 8.0 (Windows)
- **UI Framework**: WPF with MVVM architecture
- **AutoCAD Compatibility**: AutoCAD 2025, AutoCAD 2026
- **Dependencies**: 
  - AutoCAD .NET API
  - Newtonsoft.Json
  - System.Windows.Forms (for enhanced compatibility)

## 📋 System Requirements

- **Operating System**: Windows 10/11 (64-bit)
- **AutoCAD**: AutoCAD 2025 or AutoCAD 2026
- **.NET Runtime**: .NET 8.0 Runtime (automatically installed with AutoCAD 2025/2026)
- **Memory**: Minimum 4GB RAM (8GB recommended for large datasets)
- **Storage**: 50MB free disk space

## 🛠️ Installation

### Method 1: Manual Installation
1. Download the latest release from the releases section
2. Extract the plugin files to a local directory
3. Open AutoCAD 2025/2026
4. Type `NETLOAD` in the command line
5. Browse and select `SPM_NET8_2025_2026.dll`
6. The plugin will load and display the SPM palette

### Method 2: AutoCAD Plugin Manager
1. Copy the plugin files to AutoCAD's plugin directory
2. Use AutoCAD's Plugin Manager to load the plugin
3. Enable auto-loading for future sessions

### Method 3: Startup Suite
1. Add the plugin to AutoCAD's Startup Suite
2. The plugin will automatically load with AutoCAD

## 🎯 Quick Start Guide

### First Launch
1. After loading the plugin, the SPM palette will appear
2. Navigate through the tabs: Home, Import, Export, Settings, History
3. Configure your preferences in the Settings tab
4. Start importing your first dataset

### Basic Workflow
1. **Import Data**: Use the Import tab to load survey points
2. **Review Points**: Check imported data in the points grid
3. **Edit if Needed**: Make corrections using the built-in editor
4. **Draw Points**: Insert points into your AutoCAD drawing
5. **Export Results**: Save processed data in your preferred format

## 📁 Project Structure

```
SPM_NET8_2025_2026/
├── Views/                  # WPF User Controls and Windows
├── ViewModels/            # MVVM ViewModels
├── Models/                # Data models and entities
├── Services/              # Business logic and data services
├── Managers/              # System managers (History, Settings, etc.)
├── Helpers/               # Utility classes and helpers
├── Utils/                 # Utility functions and extensions
├── PartialViews/          # Reusable UI components
├── Properties/            # Project properties and settings
└── Resources/             # Images, icons, and other resources
```

## 🔄 Migration from Previous Versions

This .NET 8 edition replaces the previous .NET Framework 4.6 version with:
- **Enhanced Performance**: Faster loading and processing
- **Modern Framework**: Latest .NET 8 features and optimizations
- **Improved Compatibility**: Better integration with AutoCAD 2025/2026
- **Maintained Functionality**: All features from previous versions preserved

## 🐛 Troubleshooting

### Common Issues

**Plugin Won't Load**
- Ensure .NET 8 runtime is installed
- Check AutoCAD version compatibility
- Verify all dependencies are present

**Import Errors**
- Check file format and encoding
- Verify coordinate system settings
- Review data validation messages

**Performance Issues**
- Close unnecessary applications
- Increase available memory
- Process large datasets in smaller batches

### Getting Help
- Check the User Guide for detailed instructions
- Review the troubleshooting section
- Contact support for technical assistance

## 📝 License

This software is proprietary. Please refer to the license agreement for usage terms and conditions.

## 🤝 Support

For technical support, feature requests, or bug reports:
- Email: [support email]
- Documentation: See USER_GUIDE.md
- Version: 2025.1.0 (.NET 8 Edition)

## 🔄 Version History

### v2025.1.0 (.NET 8 Edition)
- Migrated to .NET 8 framework
- Enhanced AutoCAD 2025/2026 compatibility
- Improved performance and stability
- Maintained all existing functionality
- Added Windows Forms compatibility layer

### Previous Versions
- v2017.1.0 (.NET Framework 4.6) - Legacy version for AutoCAD 2017-2024

---

**Note**: This is the official .NET 8 edition optimized for AutoCAD 2025 and 2026. For older AutoCAD versions, please use the legacy .NET Framework 4.6 edition.
