# Survey Points Manager - User Guide

## Table of Contents
1. [Getting Started](#getting-started)
2. [Interface Overview](#interface-overview)
3. [Importing Data](#importing-data)
4. [Managing Points](#managing-points)
5. [Exporting Data](#exporting-data)
6. [Drawing Tools](#drawing-tools)
7. [Settings Configuration](#settings-configuration)
8. [History Management](#history-management)
9. [Advanced Features](#advanced-features)
10. [Troubleshooting](#troubleshooting)

## Getting Started

### Installation and First Launch
1. Load the plugin using `NETLOAD` command in AutoCAD
2. The SPM palette will automatically appear on the right side
3. If the palette is not visible, type `SPM` in the command line to show it

### Interface Layout
The SPM interface consists of five main tabs:
- **Home**: Overview and quick actions
- **Import**: Data import tools and settings
- **Export**: Data export tools and options
- **Settings**: Configuration and preferences
- **History**: Operation history and management

## Interface Overview

### Home Tab
- **Points Summary**: Displays current point count and statistics
- **Quick Actions**: Common operations like Clear All, Undo/Redo
- **Recent Files**: Quick access to recently imported files
- **Status Information**: Current session status and notifications

### Navigation
- Use tabs at the top to switch between different functions
- Each tab contains relevant tools and settings
- Context-sensitive help is available throughout the interface

## Importing Data

### Supported File Formats

#### CSV Files
- **Standard CSV**: Comma-separated values with customizable delimiters
- **Format Detection**: Automatic detection of coordinate order and structure
- **Column Mapping**: Map columns to Point Number, Easting, Northing, Elevation, Description
- **Header Support**: Files with or without header rows

#### TXT Files
- **Space-Delimited**: Space or tab-separated coordinate files
- **Fixed-Width**: Fixed-width column formats
- **Custom Delimiters**: Support for various delimiter types

#### KML Files
- **Google Earth**: Standard KML format with coordinate extraction
- **UTM Conversion**: Automatic conversion from geographic to UTM coordinates
- **Zone Selection**: Manual UTM zone and hemisphere selection

#### Survey Data Formats
- **SDR**: Sokkia data recorder format
- **IDX**: Survey index files
- **GSI**: Leica GSI format (8-bit and 16-bit variants)

### Import Process

#### Step 1: Select File
1. Click "Browse" in the Import tab
2. Navigate to your data file
3. Select the file and click "Open"
4. The system will attempt automatic format detection

#### Step 2: Configure Format
1. **CSV/TXT Files**:
   - Select the detected format from the dropdown
   - Verify column mappings are correct
   - Adjust delimiter settings if needed

2. **KML Files**:
   - Select UTM zone from the dropdown
   - Choose hemisphere (North/South)
   - Verify coordinate system settings

3. **Survey Formats**:
   - Select the appropriate variant (for GSI files)
   - Review format-specific settings

#### Step 3: Preview and Import
1. Click "Import" to process the file
2. Review the imported data in the points grid
3. Check for any validation errors or warnings
4. Make corrections if necessary

### Import Settings

#### File Format Options
- **Include Headers**: Whether the file contains header rows
- **Point Numbering**: Include or exclude point numbers
- **Coordinate Order**: XY (Easting, Northing) or YX (Northing, Easting)

#### Validation Options
- **Duplicate Detection**: Identify and handle duplicate points
- **Range Validation**: Check coordinates are within expected ranges
- **Data Type Validation**: Ensure numeric fields contain valid numbers

## Managing Points

### Points Grid
The main points grid displays all imported survey points with the following columns:
- **Point Number**: Unique identifier for each point
- **Easting**: X-coordinate (typically in meters)
- **Northing**: Y-coordinate (typically in meters)
- **Elevation**: Z-coordinate or height
- **Description**: Text description or point code

### Editing Points

#### In-Place Editing
1. Double-click any cell in the points grid
2. Edit the value directly
3. Press Enter to confirm or Esc to cancel
4. Changes are automatically validated

#### Bulk Operations
- **Select Multiple Points**: Use Ctrl+Click or Shift+Click
- **Delete Points**: Select points and press Delete key
- **Copy/Paste**: Standard clipboard operations supported

### Search and Filter

#### Search Functionality
1. Use the search box at the top of the points grid
2. Search across all fields: Point Number, Coordinates, Description
3. Results update in real-time as you type
4. Clear search to show all points

#### Advanced Filtering
- **Coordinate Ranges**: Filter points within specific coordinate bounds
- **Description Patterns**: Filter by description text or codes
- **Point Number Ranges**: Show points within specific number ranges

### Point Validation

#### Automatic Validation
- **Coordinate Checks**: Verify coordinates are numeric and reasonable
- **Duplicate Detection**: Identify points with identical coordinates
- **Missing Data**: Highlight incomplete point records

#### Manual Validation
- **Visual Review**: Check points in the grid for obvious errors
- **Coordinate Verification**: Compare with known reference points
- **Description Consistency**: Ensure description codes are consistent

## Exporting Data

### Export Formats
All import formats are also available for export:
- CSV, TXT, KML, SDR, IDX, GSI

### Export Process

#### Step 1: Select Format
1. Go to the Export tab
2. Click "Open Export Dialog"
3. Select desired export format from dropdown
4. Format-specific options will appear

#### Step 2: Configure Options

**CSV/TXT Export**:
- **Include Headers**: Add column headers to output file
- **Include Point Numbers**: Export point numbers
- **Coordinate Order**: Choose XY or YX order
- **Delimiter**: Select separator character

**KML Export**:
- **UTM Zone**: Specify source UTM zone
- **Hemisphere**: North or South hemisphere
- **Coordinate System**: Target coordinate system

**Survey Format Export**:
- **GSI Variant**: Select 8-bit or 16-bit format
- **Format Options**: Specific to each survey format

#### Step 3: Export
1. Click "Export" button
2. Choose destination folder and filename
3. Confirm export settings
4. File will be saved to specified location

### Batch Export
- Export multiple formats simultaneously
- Save to different locations
- Maintain consistent settings across exports

## Drawing Tools

### Point Insertion

#### Manual Point Picking
1. Enable "Manual Picking" mode
2. Click points in the AutoCAD drawing
3. Points are automatically numbered and added to the grid
4. Use OSNAP for precise point placement

#### Automatic Point Drawing
1. Import or create points in the grid
2. Click "Draw Points" button
3. Points are inserted into the current AutoCAD drawing
4. Point numbers are added as text labels

### Smart Picking Features

#### Auto Pick
- Automatically detect and number points in the drawing
- Uses intelligent algorithms to identify survey points
- Configurable starting point and numbering sequence

#### Smart Pick
- Interactive point selection with real-time feedback
- Object filtering (blocks, text, points)
- Batch processing of selected objects

### Drawing Settings

#### Point Display
- **Point Style**: Configure AutoCAD point display style
- **Point Size**: Set point size relative to screen or absolute
- **Layer Assignment**: Specify target layer for points

#### Text Labels
- **Text Height**: Set height for point number labels
- **Text Style**: Choose text style for labels
- **Label Offset**: Position labels relative to points

### Table Generation
- Create formatted tables in AutoCAD model space
- Customizable table layout and formatting
- Automatic pagination for large datasets
- Include headers and formatting options

## Settings Configuration

### General Settings

#### Display Options
- **Units**: Set coordinate display units (meters, feet, etc.)
- **Precision**: Number of decimal places for coordinates
- **Grid Options**: Configure points grid appearance

#### File Handling
- **Default Paths**: Set default import/export directories
- **Auto-Save**: Enable automatic saving of work
- **Backup Options**: Configure backup file creation

### Import/Export Defaults

#### Format Preferences
- **Default Import Format**: Set preferred import format
- **Default Export Format**: Set preferred export format
- **Column Mappings**: Save custom column mapping templates

#### Coordinate Systems
- **Default UTM Zone**: Set default UTM zone for imports
- **Hemisphere**: Default hemisphere setting
- **Coordinate Order**: Preferred coordinate order (XY/YX)

### AutoCAD Integration

#### Drawing Settings
- **Point Layer**: Default layer for inserted points
- **Text Layer**: Default layer for point labels
- **Color Settings**: Configure point and text colors

#### OSNAP Configuration
- **Auto-Enable**: Automatically enable required OSNAP modes
- **OSNAP Types**: Configure which OSNAP types to use
- **Precision Settings**: Set snap precision and tolerance

### Performance Settings

#### Memory Management
- **Cache Size**: Configure data caching for large files
- **Processing Batch Size**: Set batch size for large operations
- **Auto-Cleanup**: Enable automatic memory cleanup

#### Display Optimization
- **Grid Virtualization**: Enable for large datasets
- **Update Frequency**: Control real-time update frequency
- **Background Processing**: Enable background operations

## History Management

### Operation History
The History tab maintains a complete record of all operations:
- **Import Operations**: Files imported with timestamps
- **Export Operations**: Files exported with settings
- **Point Modifications**: Changes made to point data
- **Drawing Operations**: Points inserted into drawings

### History Features

#### Search and Filter
- **Date Range**: Filter operations by date
- **Operation Type**: Filter by import, export, edit, etc.
- **File Names**: Search by filename or path
- **User Actions**: Track specific user operations

#### History Replay
- **Undo Operations**: Reverse previous operations
- **Redo Operations**: Replay undone operations
- **Batch Undo**: Undo multiple operations at once

### History Management

#### Cleanup Options
- **Auto-Cleanup**: Automatically remove old history entries
- **Manual Cleanup**: Selectively remove history items
- **Export History**: Save history to external file

#### Backup and Restore
- **History Backup**: Save complete operation history
- **Session Restore**: Restore previous work sessions
- **Crash Recovery**: Recover work after unexpected shutdowns

## Advanced Features

### Coordinate System Handling

#### UTM Projections
- **Zone Detection**: Automatic UTM zone detection
- **Hemisphere Handling**: North/South hemisphere support
- **Datum Transformations**: Support for various datums

#### Custom Coordinate Systems
- **Projection Parameters**: Define custom projections
- **Transformation Settings**: Configure coordinate transformations
- **Validation Rules**: Set coordinate validation parameters

### Data Validation and Quality Control

#### Validation Rules
- **Coordinate Bounds**: Define acceptable coordinate ranges
- **Elevation Limits**: Set minimum/maximum elevation values
- **Point Spacing**: Check for minimum point separation

#### Quality Reports
- **Validation Summary**: Overview of data quality issues
- **Error Details**: Detailed error descriptions and locations
- **Correction Suggestions**: Automated suggestions for fixes

### Automation and Scripting

#### Batch Processing
- **Multiple File Import**: Process multiple files simultaneously
- **Automated Workflows**: Set up automated processing chains
- **Scheduled Operations**: Configure scheduled data processing

#### Integration Options
- **Command Line Interface**: Automate operations via command line
- **API Access**: Integrate with other applications
- **Custom Scripts**: Support for custom automation scripts

## Troubleshooting

### Common Issues

#### Import Problems

**File Won't Import**
- Check file format and encoding
- Verify file is not corrupted or locked
- Ensure file contains valid coordinate data
- Check delimiter settings for CSV/TXT files

**Incorrect Coordinates**
- Verify coordinate order (XY vs YX)
- Check UTM zone and hemisphere settings
- Ensure coordinate system is correctly specified
- Validate source data format

**Missing Points**
- Check for empty rows in source file
- Verify all required columns are present
- Review validation error messages
- Check coordinate range filters

#### Export Problems

**Export Fails**
- Ensure destination folder is writable
- Check available disk space
- Verify export format settings
- Close file if it's open in another application

**Incorrect Output Format**
- Review export format settings
- Check coordinate order configuration
- Verify delimiter and header settings
- Ensure all required fields are included

#### Performance Issues

**Slow Import/Export**
- Process large files in smaller batches
- Close unnecessary applications
- Increase available system memory
- Use SSD storage for better performance

**Interface Lag**
- Reduce grid update frequency
- Enable data virtualization for large datasets
- Close other AutoCAD drawings
- Restart AutoCAD if memory usage is high

### Error Messages

#### Common Error Codes
- **ERR001**: File format not recognized
- **ERR002**: Invalid coordinate data
- **ERR003**: Missing required columns
- **ERR004**: Coordinate system error
- **ERR005**: Memory allocation error

#### Resolution Steps
1. Check error message details
2. Review input data format
3. Verify settings configuration
4. Try with smaller dataset
5. Contact support if issue persists

### Getting Help

#### Built-in Help
- Hover over controls for tooltips
- Check status bar for current operation info
- Review validation messages for guidance

#### External Resources
- User manual and documentation
- Video tutorials and examples
- Technical support contact information
- Community forums and discussions

## Keyboard Shortcuts

### General Navigation
- **Ctrl + Tab**: Switch between tabs
- **F1**: Show help
- **Esc**: Cancel current operation
- **Ctrl + Z**: Undo last operation
- **Ctrl + Y**: Redo last operation

### Points Grid
- **Ctrl + A**: Select all points
- **Ctrl + C**: Copy selected points
- **Ctrl + V**: Paste points
- **Delete**: Delete selected points
- **F2**: Edit selected cell
- **Ctrl + F**: Open search box

### Import/Export
- **Ctrl + O**: Open import dialog
- **Ctrl + S**: Quick export
- **Ctrl + Shift + S**: Export dialog
- **F5**: Refresh file list

## Best Practices

### Data Management
1. **Backup Original Files**: Always keep copies of original survey data
2. **Validate Before Processing**: Review imported data before drawing
3. **Use Consistent Naming**: Maintain consistent file naming conventions
4. **Document Settings**: Record coordinate systems and settings used

### Performance Optimization
1. **Process in Batches**: Handle large datasets in smaller chunks
2. **Close Unused Drawings**: Keep only necessary AutoCAD drawings open
3. **Regular Cleanup**: Clear history and temporary files periodically
4. **Monitor Memory**: Watch system memory usage during large operations

### Quality Control
1. **Visual Inspection**: Always visually inspect imported points
2. **Cross-Reference**: Compare with known control points
3. **Coordinate Validation**: Verify coordinate systems and transformations
4. **Documentation**: Maintain records of all processing steps

## Integration with Other Software

### CAD Software
- **AutoCAD Civil 3D**: Enhanced integration with Civil 3D point objects
- **MicroStation**: Export to MicroStation-compatible formats
- **Other CAD**: Standard file format support for broad compatibility

### Survey Software
- **Trimble Business Center**: Direct import/export compatibility
- **Leica Infinity**: GSI format support for seamless workflow
- **Topcon MAGNET**: Multiple format support for data exchange

### GIS Software
- **ArcGIS**: KML and coordinate system compatibility
- **QGIS**: Standard format support for GIS integration
- **Google Earth**: Direct KML export for visualization

## Customization Options

### User Interface
- **Theme Selection**: Choose from available interface themes
- **Layout Customization**: Adjust panel sizes and positions
- **Toolbar Configuration**: Customize available tools and buttons

### Workflow Automation
- **Template Creation**: Save frequently used settings as templates
- **Batch Scripts**: Create scripts for repetitive operations
- **Custom Formats**: Define custom import/export formats

### Advanced Configuration
- **Registry Settings**: Advanced configuration through Windows registry
- **Configuration Files**: XML-based configuration for complex setups
- **Plugin Extensions**: Support for custom plugin extensions

---

## Appendices

### Appendix A: Supported File Formats

#### Import Formats
| Format | Extension | Description | Notes |
|--------|-----------|-------------|-------|
| CSV | .csv | Comma-separated values | Customizable delimiters |
| TXT | .txt | Text files | Space/tab delimited |
| KML | .kml | Google Earth format | Geographic coordinates |
| SDR | .sdr | Sokkia data recorder | Survey instrument format |
| IDX | .idx | Survey index files | Point index format |
| GSI | .gsi | Leica GSI format | 8-bit and 16-bit variants |

#### Export Formats
All import formats are supported for export with format-specific options.

### Appendix B: Coordinate Systems

#### Supported UTM Zones
- **Northern Hemisphere**: Zones 1-60
- **Southern Hemisphere**: Zones 1-60
- **Automatic Detection**: Based on coordinate ranges

#### Common Datums
- **WGS84**: World Geodetic System 1984
- **NAD83**: North American Datum 1983
- **NAD27**: North American Datum 1927
- **Local Datums**: Support for local coordinate systems

### Appendix C: Error Codes Reference

| Code | Description | Solution |
|------|-------------|----------|
| ERR001 | File format not recognized | Check file extension and format |
| ERR002 | Invalid coordinate data | Verify numeric coordinate values |
| ERR003 | Missing required columns | Ensure all required fields present |
| ERR004 | Coordinate system error | Check UTM zone and hemisphere |
| ERR005 | Memory allocation error | Close applications, restart AutoCAD |
| ERR006 | File access denied | Check file permissions |
| ERR007 | Network connection error | Verify network connectivity |
| ERR008 | License validation failed | Check license status |

### Appendix D: Performance Guidelines

#### System Requirements by Dataset Size

| Dataset Size | RAM Required | Processing Time | Recommendations |
|--------------|--------------|-----------------|-----------------|
| < 1,000 points | 4GB | < 1 minute | Standard settings |
| 1,000 - 10,000 | 8GB | 1-5 minutes | Enable virtualization |
| 10,000 - 50,000 | 16GB | 5-15 minutes | Batch processing |
| > 50,000 points | 32GB+ | 15+ minutes | Professional workstation |

#### Optimization Settings
- **Grid Virtualization**: Enable for datasets > 5,000 points
- **Background Processing**: Use for large imports/exports
- **Memory Caching**: Adjust cache size based on available RAM
- **Update Frequency**: Reduce for better performance with large datasets

---

For additional support or questions not covered in this guide, please contact technical support or refer to the README.md file for more information.

**Survey Points Manager v2025.1.0 (.NET 8 Edition)**
*Compatible with AutoCAD 2025 and 2026*
