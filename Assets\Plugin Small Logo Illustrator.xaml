<!-- Created with Ai->XAML Export Plug-In Version 0.4 (PC/64) -->
<!-- By <PERSON> (http://blog.mikeswanson.com/)           -->

<Viewbox Width="48.837" Height="50.382"
  xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
<Canvas Width="48.837" Height="50.382">

  <Canvas>

  <!-- drawing/<Path> -->
  <Path Fill="#ff6f788c" Data="F1 M 11.458,0.008 C 13.460,0.009 15.462,0.025 17.463,0.000 C 18.128,-0.008 18.493,0.302 18.647,0.895 C 19.195,2.998 19.726,5.106 20.269,7.210 C 20.343,7.496 20.306,7.624 19.962,7.617 C 18.972,7.597 17.982,7.601 16.991,7.614 C 16.717,7.618 16.603,7.532 16.633,7.255 C 16.646,7.130 16.634,7.002 16.625,6.876 C 16.546,5.819 16.546,5.819 17.591,5.813 C 17.801,5.812 18.012,5.816 18.223,5.806 C 18.562,5.791 18.787,5.647 18.786,5.272 C 18.786,4.896 18.564,4.750 18.221,4.747 C 17.736,4.742 17.250,4.725 16.767,4.752 C 16.458,4.770 16.390,4.663 16.371,4.367 C 16.319,3.591 16.200,2.820 16.142,2.045 C 16.106,1.572 15.898,1.386 15.423,1.388 C 12.769,1.400 10.114,1.402 7.459,1.386 C 6.983,1.383 6.748,1.567 6.708,2.024 C 6.641,2.799 6.554,3.573 6.522,4.349 C 6.507,4.696 6.425,4.843 6.056,4.818 C 5.637,4.789 5.214,4.816 4.792,4.810 C 4.470,4.805 4.324,4.996 4.314,5.277 C 4.305,5.579 4.434,5.818 4.783,5.829 C 5.141,5.840 5.500,5.832 5.858,5.833 C 5.984,5.833 6.150,5.784 6.230,5.846 C 6.586,6.122 6.386,6.517 6.309,6.812 C 6.238,7.086 6.565,7.621 5.939,7.634 C 4.949,7.654 3.959,7.642 2.969,7.659 C 2.439,7.668 2.721,7.281 2.752,7.135 C 3.076,5.623 3.435,4.118 3.786,2.611 C 3.909,2.081 4.022,1.549 4.164,1.024 C 4.377,0.236 4.692,0.009 5.516,0.008 C 7.497,0.007 9.478,0.008 11.458,0.008 Z"/>

  <!-- drawing/<Compound Path> -->
  <Path Fill="#ff6f788c" Data="F1 M 15.770,26.227 C 15.756,25.727 15.468,25.478 15.059,25.478 C 14.618,25.478 14.319,25.735 14.337,26.232 C 14.353,26.681 14.606,26.945 15.037,26.942 C 15.493,26.939 15.751,26.661 15.770,26.227 Z M 15.772,28.430 C 15.761,27.868 15.462,27.631 15.058,27.630 C 14.618,27.630 14.311,27.895 14.328,28.382 C 14.343,28.823 14.613,29.089 15.039,29.088 C 15.499,29.088 15.746,28.798 15.772,28.430 Z M 10.085,28.724 C 10.757,28.724 11.430,28.713 12.102,28.729 C 12.445,28.737 12.603,28.623 12.597,28.261 C 12.582,27.463 12.580,26.664 12.596,25.866 C 12.603,25.516 12.456,25.357 12.121,25.357 C 10.755,25.354 9.389,25.352 8.024,25.356 C 7.707,25.357 7.514,25.486 7.520,25.848 C 7.532,26.646 7.534,27.445 7.519,28.243 C 7.512,28.646 7.712,28.738 8.068,28.729 C 8.740,28.712 9.413,28.724 10.085,28.724 Z M 11.576,24.169 C 13.219,24.169 14.861,24.182 16.503,24.160 C 16.959,24.154 17.131,24.269 17.122,24.760 C 17.093,26.444 17.096,28.129 17.121,29.814 C 17.130,30.393 16.966,30.637 16.337,30.632 C 13.115,30.605 9.894,30.608 6.672,30.625 C 6.187,30.627 5.996,30.495 6.003,29.988 C 6.025,28.219 6.024,26.450 6.003,24.681 C 5.998,24.271 6.110,24.155 6.523,24.161 C 8.207,24.185 9.892,24.171 11.576,24.171 C 11.576,24.170 11.576,24.170 11.576,24.169 Z"/>

  <!-- drawing/<Path> -->
  <Path Fill="#ff6f788c" Data="F1 M 18.522,18.984 C 18.522,22.145 18.525,25.305 18.514,28.466 C 18.514,28.646 18.693,28.979 18.275,28.970 C 17.858,28.961 18.059,28.630 18.057,28.456 C 18.042,27.023 18.058,25.590 18.053,24.157 C 18.050,23.390 17.936,23.287 17.168,23.282 C 16.251,23.275 16.251,23.275 16.251,22.362 C 16.253,18.064 16.264,13.765 16.243,9.467 C 16.241,8.960 16.383,8.804 16.868,8.862 C 17.446,8.930 18.223,8.613 18.554,8.994 C 18.839,9.321 18.642,10.076 18.643,10.641 C 18.646,13.422 18.645,16.203 18.645,18.984 C 18.604,18.984 18.563,18.984 18.522,18.984 Z"/>

  <!-- drawing/<Path> -->
  <Path Fill="#ff6f788c" Data="F1 M 4.496,18.926 C 4.496,15.765 4.507,12.605 4.484,9.444 C 4.480,8.966 4.607,8.804 5.079,8.862 C 5.615,8.928 6.333,8.636 6.645,8.990 C 6.910,9.291 6.734,9.989 6.735,10.511 C 6.740,14.578 6.731,18.645 6.753,22.712 C 6.756,23.185 6.639,23.370 6.164,23.285 C 6.021,23.259 5.868,23.293 5.722,23.279 C 5.202,23.229 5.007,23.478 5.012,23.979 C 5.024,25.475 5.024,26.971 5.014,28.467 C 5.013,28.657 5.172,28.969 4.760,28.973 C 4.334,28.978 4.508,28.651 4.508,28.471 C 4.496,25.289 4.499,22.108 4.499,18.926 C 4.498,18.926 4.497,18.926 4.496,18.926 Z"/>

  <!-- drawing/<Path> -->
  <Path Fill="#ff6f788c" Data="F1 M 3.759,18.948 C 3.759,22.043 3.764,25.137 3.749,28.232 C 3.748,28.471 3.934,28.886 3.611,28.936 C 3.185,29.003 2.690,29.128 2.294,28.743 C 1.990,28.447 1.962,28.089 1.963,27.701 C 1.966,23.068 1.963,18.436 1.962,13.804 C 1.961,12.562 1.958,11.320 1.963,10.078 C 1.967,9.203 2.316,8.891 3.187,8.857 C 3.634,8.839 3.742,8.971 3.739,9.410 C 3.719,12.590 3.728,15.769 3.728,18.948 C 3.739,18.948 3.749,18.948 3.759,18.948 Z"/>

  <!-- drawing/<Path> -->
  <Path Fill="#ff6f788c" Data="F1 M 7.782,12.448 C 7.782,10.847 7.773,9.246 7.785,7.645 C 7.792,6.698 8.421,6.091 9.375,6.088 C 10.828,6.083 12.282,6.084 13.735,6.088 C 14.662,6.090 15.225,6.604 15.261,7.557 C 15.319,9.105 15.330,10.654 15.214,12.298 C 14.393,10.692 13.074,9.921 11.426,9.902 C 9.681,9.881 8.583,10.973 7.782,12.448 Z"/>

  <!-- drawing/<Path> -->
  <Path Fill="#ff6f788c" Data="F1 M 15.280,15.756 C 15.287,16.936 15.309,18.116 15.296,19.296 C 15.280,20.706 14.742,21.220 13.327,21.223 C 12.063,21.226 10.799,21.231 9.535,21.223 C 8.366,21.216 7.786,20.639 7.781,19.486 C 7.776,18.118 7.780,16.751 7.780,15.385 C 8.048,15.340 8.045,15.580 8.099,15.702 C 9.115,17.967 11.729,18.553 13.532,17.399 C 14.139,17.010 14.719,16.483 15.067,15.788 C 15.095,15.732 15.157,15.370 15.280,15.758 C 15.280,15.758 15.280,15.756 15.280,15.756 Z"/>

  <!-- drawing/<Compound Path> -->
  <Path Fill="#ff6fc7f1" Data="F1 M 11.545,15.963 C 12.733,15.974 13.639,15.098 13.637,13.940 C 13.635,12.781 12.777,11.900 11.641,11.892 C 10.404,11.883 9.526,12.734 9.524,13.945 C 9.522,15.096 10.379,15.952 11.545,15.963 Z M 11.389,17.038 C 10.187,17.003 8.436,15.936 8.492,13.967 C 8.546,12.069 9.487,10.946 11.530,10.836 C 12.870,10.764 14.668,11.892 14.671,13.846 C 14.673,15.501 13.414,17.096 11.389,17.038 Z"/>

  <!-- drawing/<Path> -->
  <Path Fill="#ff6f788c" Data="F1 M 19.488,22.228 C 19.809,23.658 20.433,24.971 21.013,26.300 C 21.313,26.988 21.176,27.707 21.154,28.411 C 21.145,28.701 20.889,28.918 20.556,28.969 C 19.400,29.149 19.291,29.057 19.291,27.901 C 19.293,26.015 19.292,24.128 19.292,22.242 C 19.357,22.238 19.423,22.233 19.488,22.228 Z"/>

  <!-- drawing/<Path> -->
  <Path Fill="#ff6f788c" Data="F1 M 0.050,15.118 C 0.050,14.383 0.050,13.649 0.049,12.914 C 0.049,12.554 0.178,12.290 0.578,12.288 C 0.982,12.285 1.124,12.572 1.127,12.914 C 1.137,14.340 1.133,15.767 1.123,17.194 C 1.120,17.603 0.916,17.964 0.498,17.954 C 0.092,17.943 0.037,17.552 0.040,17.195 C 0.046,16.502 0.042,15.810 0.042,15.118 C 0.044,15.118 0.047,15.118 0.050,15.118 Z"/>

  <!-- drawing/<Path> -->
  <Path Fill="#ff6f788c" Data="F1 M 19.481,13.855 C 19.558,12.321 19.540,10.785 19.547,9.250 C 19.549,8.891 20.153,8.702 20.612,8.909 C 21.054,9.107 21.302,9.671 21.095,10.124 C 20.533,11.348 19.949,12.563 19.481,13.855 Z"/>

  <!-- drawing/<Path> -->
  <Path Fill="#ff7ac1e9" Data="F1 M 15.280,15.756 C 15.280,15.756 15.280,15.758 15.280,15.758 C 15.280,15.758 15.280,15.756 15.280,15.756 Z"/>

  <!-- drawing/<Path> -->
  <Path Fill="#ffebebeb" Data="F1 M 10.085,28.724 C 9.413,28.724 8.740,28.712 8.068,28.729 C 7.712,28.738 7.512,28.646 7.519,28.243 C 7.534,27.445 7.532,26.646 7.520,25.848 C 7.514,25.486 7.707,25.357 8.024,25.356 C 9.389,25.352 10.755,25.354 12.121,25.357 C 12.456,25.357 12.603,25.516 12.596,25.866 C 12.580,26.664 12.582,27.463 12.597,28.261 C 12.603,28.623 12.445,28.737 12.102,28.729 C 11.430,28.713 10.757,28.724 10.085,28.724 Z"/>

  <!-- drawing/<Path> -->
  <Path Fill="#ffebebeb" Data="F1 M 15.772,28.430 C 15.746,28.798 15.499,29.088 15.039,29.088 C 14.613,29.089 14.343,28.823 14.328,28.382 C 14.311,27.895 14.618,27.630 15.058,27.630 C 15.462,27.631 15.761,27.868 15.772,28.430 Z"/>

  <!-- drawing/<Path> -->
  <Path Fill="#ffebebeb" Data="F1 M 15.770,26.227 C 15.751,26.661 15.493,26.939 15.037,26.942 C 14.606,26.945 14.353,26.681 14.337,26.232 C 14.319,25.735 14.618,25.478 15.059,25.478 C 15.468,25.478 15.756,25.727 15.770,26.227 Z"/>

  <!-- drawing/<Path> -->
  <Path Fill="#ffebebeb" Data="F1 M 11.545,15.963 C 10.379,15.952 9.522,15.096 9.524,13.945 C 9.526,12.734 10.404,11.883 11.641,11.892 C 12.777,11.900 13.635,12.781 13.637,13.940 C 13.639,15.098 12.733,15.974 11.545,15.963 Z"/>

  <Canvas>

  <!-- drawing/<Group>/<Path> -->
  <Path Fill="#ff6f788c" Data="F1 M 40.417,23.745 C 40.411,24.179 39.883,24.702 39.451,24.702 C 39.270,24.702 39.150,24.610 39.046,24.471 C 38.152,23.278 37.254,22.087 36.359,20.895 C 36.298,20.814 36.217,20.744 36.324,20.627 C 36.444,20.498 36.525,20.576 36.619,20.647 C 37.810,21.544 39.001,22.441 40.194,23.336 C 40.334,23.441 40.423,23.564 40.417,23.745 Z"/>

  <Canvas>

  <!-- drawing/<Group>/<Group>/<Path> -->
  <Path Fill="#ff6f788c" Data="F1 M 33.033,16.869 C 32.907,16.622 33.131,16.577 33.261,16.499 C 34.240,15.918 35.580,16.146 36.393,17.026 C 37.191,17.890 37.341,19.285 36.652,20.187 C 35.799,21.305 34.656,21.751 33.250,21.037 C 32.070,20.438 31.594,19.006 32.124,17.751 C 32.205,17.560 32.213,17.224 32.586,17.316 C 33.158,17.444 33.158,17.444 33.033,16.869 Z"/>

  <!-- drawing/<Group>/<Group>/<Path> -->
  <Path Fill="#ff6f788c" Data="F1 M 29.285,12.815 C 29.543,12.735 29.724,12.831 29.880,13.043 C 30.826,14.324 31.777,15.600 32.727,16.878 C 32.755,16.916 32.787,16.950 32.822,16.981 C 32.891,17.040 32.955,17.103 32.847,17.197 C 32.762,17.270 32.707,17.209 32.654,17.146 C 32.623,17.108 32.585,17.075 32.546,17.045 C 31.283,16.089 30.021,15.131 28.755,14.179 C 28.545,14.021 28.449,13.839 28.528,13.579 C 28.683,13.572 28.655,13.739 28.739,13.799 C 28.989,13.996 29.202,13.874 29.383,13.698 C 29.572,13.512 29.704,13.292 29.502,13.025 C 29.441,12.943 29.277,12.968 29.285,12.815 Z"/>

  <!-- drawing/<Group>/<Group>/<Path> -->
  <Path Fill="#ff6f788c" Data="F1 M 29.316,12.831 C 29.385,12.924 29.535,12.835 29.592,12.954 C 29.551,13.019 29.484,13.039 29.415,13.052 C 29.052,13.115 28.841,13.327 28.777,13.690 C 28.765,13.758 28.745,13.826 28.680,13.867 C 28.561,13.810 28.649,13.660 28.556,13.591 C 28.660,13.188 28.914,12.935 29.316,12.831 Z"/>

  </Canvas>

  </Canvas>

  <Canvas>

  <Canvas>

  <!-- drawing/LWPOLYLINE/<Group>/<Path> -->
  <Path Fill="#ff7ac1e9" Data="F1 M 28.426,33.799 L 29.152,34.616 C 29.277,34.713 29.459,34.698 29.566,34.577 C 29.649,34.484 29.666,34.348 29.611,34.238 L 27.031,31.285 C 26.925,31.170 26.737,31.160 26.610,31.271 L 25.233,32.468 C 25.795,33.583 26.451,34.880 27.141,36.242 C 27.320,35.536 27.452,34.817 27.523,34.092 C 27.543,33.887 27.683,33.714 27.879,33.650 C 28.075,33.587 28.290,33.645 28.426,33.799 Z"/>

  <!-- drawing/LWPOLYLINE/<Group>/<Path> -->
  <Path Fill="#ff7ac1e9" Data="F1 M 34.712,37.585 C 32.893,37.650 31.151,38.473 29.929,39.926 C 29.733,40.157 29.551,40.399 29.380,40.649 C 29.635,41.150 29.887,41.643 30.132,42.122 C 30.568,41.142 31.160,40.229 31.898,39.430 C 32.701,38.597 33.660,37.973 34.712,37.585 Z"/>

  <!-- drawing/LWPOLYLINE/<Group>/<Compound Path> -->
  <Path Fill="#ff7ac1e9" Data="F1 M 34.306,28.618 C 29.042,28.637 24.323,23.887 24.489,18.505 C 24.667,12.717 29.453,8.580 34.638,8.740 C 40.134,8.908 44.440,13.389 44.311,18.978 C 44.192,24.136 40.015,28.815 34.306,28.618 Z M 48.416,14.915 C 46.862,8.555 41.009,4.143 34.333,4.157 C 30.596,4.154 27.314,5.379 24.623,7.975 C 20.991,11.477 19.585,15.770 20.373,20.764 C 20.835,23.692 22.238,26.269 23.448,28.917 C 23.471,28.968 23.979,29.980 24.751,31.513 L 25.929,30.490 C 26.481,30.007 27.305,30.052 27.802,30.593 L 30.425,33.594 C 30.443,33.614 30.459,33.636 30.473,33.659 C 30.790,34.162 30.735,34.823 30.339,35.267 C 29.846,35.819 28.996,35.868 28.444,35.375 C 28.429,35.362 28.415,35.347 28.401,35.332 L 28.400,35.331 C 28.266,36.099 28.074,36.857 27.827,37.596 C 28.168,38.267 28.511,38.944 28.852,39.613 C 28.945,39.493 29.039,39.373 29.137,39.258 C 31.138,36.879 34.344,35.959 37.303,36.918 C 37.326,36.925 37.348,36.934 37.370,36.945 C 37.537,37.026 37.661,37.175 37.709,37.355 C 37.754,37.519 37.732,37.693 37.646,37.842 C 37.561,37.991 37.423,38.098 37.257,38.143 C 37.217,38.154 37.177,38.160 37.136,38.161 C 35.429,38.209 33.837,38.912 32.651,40.141 C 31.795,41.068 31.156,42.168 30.762,43.352 C 31.149,44.105 31.509,44.803 31.828,45.418 C 31.906,45.457 31.980,45.502 32.050,45.557 C 32.330,45.777 32.506,46.092 32.548,46.446 C 32.562,46.558 32.556,46.670 32.541,46.779 C 32.904,47.464 33.149,47.910 33.228,48.018 C 33.488,48.376 33.652,48.807 34.178,48.839 C 34.728,48.872 34.935,48.464 35.145,48.070 C 37.413,43.829 39.693,39.593 41.943,35.342 C 43.810,31.815 45.749,28.325 47.407,24.690 C 48.844,21.539 49.232,18.257 48.416,14.915 Z"/>

  </Canvas>

  <!-- drawing/LWPOLYLINE/<Path> -->
  <Path StrokeThickness="0.5" Stroke="#ff6f788c" StrokeStartLineCap="Round" StrokeEndLineCap="Round" StrokeLineJoin="Round" Fill="#ff6f788c" Data="F1 M 30.035,33.935 L 27.421,30.944 C 27.117,30.614 26.606,30.586 26.269,30.881 L 23.291,33.469 C 22.989,33.802 23.015,34.316 23.348,34.617 C 23.633,34.875 24.060,34.898 24.371,34.673 L 25.322,33.861 C 25.338,36.465 24.273,38.959 22.382,40.748 C 21.019,41.982 19.226,42.630 17.389,42.553 C 17.326,42.547 17.270,42.595 17.264,42.658 C 17.259,42.713 17.293,42.763 17.346,42.779 C 20.029,43.859 23.100,43.204 25.109,41.122 C 26.116,40.087 26.874,38.836 27.325,37.464 C 27.688,36.388 27.928,35.273 28.039,34.143 L 28.789,34.988 C 29.128,35.291 29.649,35.262 29.952,34.922 C 30.197,34.648 30.230,34.246 30.035,33.935"/>

  </Canvas>

  <!-- drawing/<Path> -->
  <Path StrokeThickness="0.5" Stroke="#ff6f788c" StrokeStartLineCap="Round" StrokeEndLineCap="Round" StrokeLineJoin="Round" Fill="#ff6f788c" Data="F1 M 25.105,47.098 L 27.916,49.905 C 28.241,50.213 28.752,50.206 29.069,49.889 L 31.865,47.106 C 32.143,46.753 32.082,46.242 31.730,45.964 C 31.428,45.726 31.001,45.732 30.706,45.978 L 29.812,46.852 C 29.619,44.255 30.512,41.695 32.279,39.781 C 33.551,38.462 35.289,37.694 37.121,37.643 C 37.188,37.625 37.227,37.556 37.209,37.490 C 37.200,37.455 37.176,37.426 37.143,37.411 C 34.399,36.522 31.389,37.385 29.532,39.593 C 28.598,40.694 27.927,41.994 27.569,43.393 C 27.280,44.492 27.116,45.619 27.082,46.755 L 26.276,45.962 C 25.917,45.683 25.400,45.748 25.120,46.107 C 24.895,46.397 24.889,46.801 25.105,47.098"/>

  <Canvas>

  <!-- drawing/LWPOLYLINE/<Path> -->
  <Path StrokeThickness="0.5" Stroke="#ffffffff" StrokeStartLineCap="Round" StrokeEndLineCap="Round" StrokeLineJoin="Round" Fill="#ff6f788c" Data="F1 M 16.177,32.981 L 6.864,32.981 C 6.637,32.981 6.437,32.831 6.375,32.612 L 6.096,31.629 C 6.083,31.583 6.076,31.537 6.076,31.490 C 6.076,31.209 6.304,30.981 6.585,30.981 L 16.455,30.981 C 16.502,30.981 16.549,30.988 16.594,31.001 C 16.864,31.077 17.021,31.358 16.944,31.628 L 16.666,32.611 C 16.604,32.830 16.404,32.981 16.177,32.981 Z"/>

  </Canvas>

  <Canvas>

  <!-- drawing/LWPOLYLINE/<Path> -->
  <Path StrokeThickness="0.5" Stroke="#ff6f788c" StrokeStartLineCap="Round" StrokeEndLineCap="Round" StrokeLineJoin="Round" Fill="#ff6f788c" Data="F1 M 6.046,34.045 L 0.266,48.979 C 0.219,49.100 0.279,49.236 0.400,49.282 C 0.427,49.293 0.456,49.298 0.485,49.298 L 1.654,49.298 L 8.381,33.849 L 6.332,33.849 C 6.205,33.849 6.092,33.927 6.046,34.045 Z"/>

  </Canvas>

  <Canvas>

  <!-- drawing/LINE/<Path> -->
  <Path StrokeThickness="0.5" Stroke="#ff7bc1ea" StrokeStartLineCap="Round" StrokeEndLineCap="Round" StrokeLineJoin="Round" Data="F1 M 5.354,35.833 L 7.173,36.624"/>

  </Canvas>

  <Canvas>

  <!-- drawing/LINE/<Path> -->
  <Path StrokeThickness="0.5" Stroke="#ff7bc1ea" StrokeStartLineCap="Round" StrokeEndLineCap="Round" StrokeLineJoin="Round" Data="F1 M 5.039,36.648 L 6.824,37.425"/>

  </Canvas>

  <Canvas>

  <!-- drawing/LWPOLYLINE/<Path> -->
  <Path StrokeThickness="0.5" Stroke="#ff6f788c" StrokeStartLineCap="Round" StrokeEndLineCap="Round" StrokeLineJoin="Round" Fill="#ff6f788c" Data="F1 M 18.166,41.902 L 14.660,33.849 L 16.709,33.849 C 16.836,33.849 16.949,33.927 16.995,34.045 L 19.925,41.616 C 19.353,41.785 18.762,41.881 18.166,41.902 Z"/>

  </Canvas>

  <Canvas>

  <!-- drawing/LWPOLYLINE/<Path> -->
  <Path StrokeThickness="0.5" Stroke="#ff6f788c" StrokeStartLineCap="Round" StrokeEndLineCap="Round" StrokeLineJoin="Round" Fill="#ff6f788c" Data="F1 M 20.948,44.259 L 22.775,48.979 C 22.785,49.006 22.791,49.035 22.791,49.064 C 22.791,49.193 22.686,49.298 22.556,49.298 L 21.387,49.298 L 19.149,44.158 C 19.743,44.256 20.346,44.290 20.948,44.259 Z"/>

  </Canvas>

  <Canvas>

  <!-- drawing/LINE/<Path> -->
  <Path StrokeThickness="0.5" Stroke="#ff7bc1ea" StrokeStartLineCap="Round" StrokeEndLineCap="Round" StrokeLineJoin="Round" Data="F1 M 17.686,35.833 L 15.868,36.624"/>

  </Canvas>

  <Canvas>

  <!-- drawing/LINE/<Path> -->
  <Path StrokeThickness="0.5" Stroke="#ff7bc1ea" StrokeStartLineCap="Round" StrokeEndLineCap="Round" StrokeLineJoin="Round" Data="F1 M 18.002,36.648 L 16.217,37.425"/>

  </Canvas>

  <Canvas>

  <!-- drawing/LWPOLYLINE/<Path> -->
  <Path StrokeThickness="0.5" Stroke="#ff6f788c" StrokeStartLineCap="Round" StrokeEndLineCap="Round" StrokeLineJoin="Round" Fill="#ff6f788c" Data="F1 M 13.323,33.849 L 9.563,33.849 L 9.924,49.298 L 12.961,49.298 L 13.323,33.849"/>

  </Canvas>

  <Canvas>

  <!-- drawing/LINE/<Path> -->
  <Path StrokeThickness="0.5" Stroke="#ff7bc1ea" StrokeStartLineCap="Round" StrokeEndLineCap="Round" StrokeLineJoin="Round" Data="F1 M 13.258,36.624 L 9.627,36.624"/>

  </Canvas>

  <Canvas>

  <!-- drawing/LINE/<Path> -->
  <Path StrokeThickness="0.5" Stroke="#ff7bc1ea" StrokeStartLineCap="Round" StrokeEndLineCap="Round" StrokeLineJoin="Round" Fill="#ff0f2239" Data="F1 M 9.646,37.425 L 13.239,37.425"/>

  </Canvas>

  </Canvas>
</Canvas>
</Viewbox>