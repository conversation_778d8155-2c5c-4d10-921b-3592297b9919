﻿using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace SPM_NET45_2015_2016.Utils
{
    /// <summary>
    /// Utility class for encryption and decryption of sensitive data
    /// </summary>
    internal static class SecurityUtils
    {
        // This key should be unique to your application and stored securely
        // In a real-world scenario, consider more secure key management
        private static readonly byte[] EncryptionKey = new byte[]
        {
            0x43, 0x87, 0x23, 0x72, 0x45, 0x56, 0x68, 0x14,
            0x62, 0x84, 0x36, 0x92, 0x5A, 0x45, 0x78, 0x88,
            0x54, 0x36, 0x22, 0x78, 0x98, 0x45, 0x25, 0x65,
            0x33, 0x77, 0x12, 0x28, 0x56, 0x76, 0x89, 0x39
        };

        private static readonly byte[] EncryptionIV = new byte[]
        {
            0x34, 0x56, 0x78, 0x90, 0x12, 0x34, 0x56, 0x78,
            0x90, 0x12, 0x34, 0x56, 0x78, 0x90, 0x12, 0x34
        };

        /// <summary>
        /// Encrypts the given plaintext string and returns a base64 encoded result
        /// </summary>
        public static string Encrypt(string plainText)
        {
            if (string.IsNullOrEmpty(plainText))
            {
                return plainText;
            }

            try
            {
                using (Aes aes = Aes.Create())
                {
                    aes.Key = EncryptionKey;
                    aes.IV = EncryptionIV;

                    ICryptoTransform encryptor = aes.CreateEncryptor(aes.Key, aes.IV);

                    using (MemoryStream memoryStream = new MemoryStream())
                    {
                        using (CryptoStream cryptoStream = new CryptoStream(memoryStream, encryptor, CryptoStreamMode.Write))
                        {
                            using (StreamWriter writer = new StreamWriter(cryptoStream))
                            {
                                writer.Write(plainText);
                            }

                            return Convert.ToBase64String(memoryStream.ToArray());
                        }
                    }
                }
            }
            catch
            {
                // In case of any error, return null
                return null;
            }
        }

        /// <summary>
        /// Decrypts the given base64 encoded ciphertext string
        /// </summary>
        public static string Decrypt(string cipherText)
        {
            if (string.IsNullOrEmpty(cipherText))
            {
                return cipherText;
            }

            try
            {
                byte[] buffer = Convert.FromBase64String(cipherText);

                using (Aes aes = Aes.Create())
                {
                    aes.Key = EncryptionKey;
                    aes.IV = EncryptionIV;

                    ICryptoTransform decryptor = aes.CreateDecryptor(aes.Key, aes.IV);

                    using (MemoryStream memoryStream = new MemoryStream(buffer))
                    {
                        using (CryptoStream cryptoStream = new CryptoStream(memoryStream, decryptor, CryptoStreamMode.Read))
                        {
                            using (StreamReader reader = new StreamReader(cryptoStream))
                            {
                                return reader.ReadToEnd();
                            }
                        }
                    }
                }
            }
            catch
            {
                // In case of any error, return null
                return null;
            }
        }

        /// <summary>
        /// Computes a device-specific identifier to bind the license to this machine
        /// </summary>
        public static string GetMachineIdentifier()
        {
            try
            {
                // Get unique machine identifiers
                string processorId = GetProcessorId();
                string diskId = GetDiskId();
                string biosId = GetBiosId();

                // Combine them and create a hash
                string combinedId = $"{processorId}|{diskId}|{biosId}";

                using (SHA256 sha256 = SHA256.Create())
                {
                    byte[] hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(combinedId));
                    return BitConverter.ToString(hashBytes).Replace("-", "").Substring(0, 32);
                }
            }
            catch
            {
                // Fallback to a simple identifier if WMI fails
                return Environment.MachineName;
            }
        }

        private static string GetProcessorId()
        {
            try
            {
                // Use WMI to get processor ID
                System.Management.ManagementClass mc = new System.Management.ManagementClass("Win32_Processor");
                System.Management.ManagementObjectCollection moc = mc.GetInstances();

                foreach (System.Management.ManagementObject mo in moc)
                {
                    return mo.Properties["ProcessorId"].Value.ToString();
                }

                return "";
            }
            catch
            {
                return Environment.ProcessorCount.ToString();
            }
        }

        private static string GetDiskId()
        {
            try
            {
                // Use WMI to get disk serial number
                System.Management.ManagementObject disk = new System.Management.ManagementObject(
                    "Win32_LogicalDisk.DeviceID='C:'");
                disk.Get();
                return disk["VolumeSerialNumber"].ToString();
            }
            catch
            {
                return "";
            }
        }

        private static string GetBiosId()
        {
            try
            {
                // Use WMI to get BIOS serial number
                System.Management.ManagementClass mc = new System.Management.ManagementClass("Win32_BIOS");
                System.Management.ManagementObjectCollection moc = mc.GetInstances();

                foreach (System.Management.ManagementObject mo in moc)
                {
                    return mo.Properties["SerialNumber"].Value.ToString();
                }

                return "";
            }
            catch
            {
                return "";
            }
        }
    }

}
