﻿using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.Geometry;
using System.Collections.Generic;
using System.Linq;

namespace SPM_NET45_2015_2016.Helpers
{
    public class KDTreeNode
    {
        public DBPoint Point { get; set; }
        public KDTreeNode Left { get; set; }
        public KDTreeNode Right { get; set; }
        public int Depth { get; set; }

        public KDTreeNode(DBPoint point, int depth)
        {
            Point = point;
            Depth = depth;
        }
    }

    public class KDTree
    {
        private KDTreeNode _root;

        public KDTree(IEnumerable<DBPoint> points)
        {
            _root = BuildTree(points.ToList(), 0);
        }

        private KDTreeNode BuildTree(List<DBPoint> points, int depth)
        {
            if (points == null || points.Count == 0)
            {
                return null;
            }

            int axis = depth % 2; // 0: X, 1: Y
            points.Sort((a, b) => axis == 0 ?
                a.Position.X.CompareTo(b.Position.X) :
                a.Position.Y.CompareTo(b.Position.Y));

            int median = points.Count / 2;
            var node = new KDTreeNode(points[median], depth);
            node.Left = BuildTree(points.GetRange(0, median), depth + 1);
            node.Right = BuildTree(points.GetRange(median + 1, points.Count - median - 1), depth + 1);
            return node;
        }

        public DBPoint NearestNeighbor(Point3d target)
        {
            return Nearest(_root, target, _root.Point, 0);
        }

        private DBPoint Nearest(KDTreeNode node, Point3d target, DBPoint best, int depth)
        {
            if (node == null)
            {
                return best;
            }

            double dBest = DistanceSquared(best.Position, target);
            double dNode = DistanceSquared(node.Point.Position, target);
            DBPoint newBest = dNode < dBest ? node.Point : best;

            int axis = depth % 2;
            double diff = axis == 0 ? target.X - node.Point.Position.X : target.Y - node.Point.Position.Y;
            KDTreeNode first = diff < 0 ? node.Left : node.Right;
            KDTreeNode second = diff < 0 ? node.Right : node.Left;

            newBest = Nearest(first, target, newBest, depth + 1);
            if (diff * diff < DistanceSquared(newBest.Position, target))
            {
                newBest = Nearest(second, target, newBest, depth + 1);
            }
            return newBest;
        }

        private double DistanceSquared(Point3d p, Point3d q)
        {
            return ((p.X - q.X) * (p.X - q.X)) + ((p.Y - q.Y) * (p.Y - q.Y));
        }
    }
}
