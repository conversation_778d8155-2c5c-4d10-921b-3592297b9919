﻿using System.Windows;
using System.Windows.Controls;

namespace SPM_NET46_2017_2018.Views
{
    /// <summary>
    /// HomePalette UserControl handles the main navigation and functionality
    /// of the PointFlow CAD application's home screen.
    /// </summary>
    public partial class HomePalette : UserControl
    {
        /// <summary>
        /// Initializes a new instance of the HomePalette class.
        /// </summary>
        public HomePalette()
        {
            InitializeComponent();
        }

        /// <summary>
        /// Handles the Import button click event.
        /// Shows the import palette interface.
        /// </summary>
        /// <param name="sender">The source of the event.</param>
        /// <param name="e">Event data.</param>
        private void ImportBTN_Click(object sender, RoutedEventArgs e)
        {
            PluginMain.ShowImportPalette();
        }

        /// <summary>
        /// Handles the Export button click event.
        /// Shows the export palette interface.
        /// </summary>
        /// <param name="sender">The source of the event.</param>
        /// <param name="e">Event data.</param>
        private void ExportBTN_Click(object sender, RoutedEventArgs e)
        {
            PluginMain.ShowExportPalette();
        }

        /// <summary>
        /// Handles the Settings button click event.
        /// Shows the settings palette interface.
        /// </summary>
        /// <param name="sender">The source of the event.</param>
        /// <param name="e">Event data.</param>
        private void SettingsBTN_Click(object sender, RoutedEventArgs e)
        {
            PluginMain.ShowSettingsPalette();
        }

        /// <summary>
        /// Handles the History button click event.
        /// Shows the history palette interface.
        /// </summary>
        /// <param name="sender">The source of the event.</param>
        /// <param name="e">Event data.</param>
        private void HistoryBTN_Click(object sender, RoutedEventArgs e)
        {
            PluginMain.ShowHistoryPalette();
        }

        private void HelpBTN_Click(object sender, RoutedEventArgs e)
        {

        }
    }

}
