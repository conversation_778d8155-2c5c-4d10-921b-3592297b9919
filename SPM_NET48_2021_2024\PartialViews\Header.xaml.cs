﻿using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;

namespace SPM_NET48_2021_2024.PartialViews
{
    public partial class Header : UserControl
    {
        public static readonly DependencyProperty BreadcrumbTextProperty =
            DependencyProperty.Register(nameof(BreadcrumbText), typeof(string), typeof(Header), new PropertyMetadata(string.Empty));

        public Header()
        {
            InitializeComponent();
        }

        public string BreadcrumbText
        {
            get { return (string)GetValue(BreadcrumbTextProperty); }
            set { SetValue(BreadcrumbTextProperty, value); }
        }

        private void NavHomeBTN_Click(object sender, RoutedEventArgs e)
        {
            SPM_NET48_2021_2024.PluginMain.MainPaletteSet?.Activate(0); // Home
        }

        private void NavSettingsBTN_Click(object sender, RoutedEventArgs e)
        {
            SPM_NET48_2021_2024.PluginMain.MainPaletteSet?.Activate(4); // Settings
        }

        private void NavHistoryBTN_Click(object sender, RoutedEventArgs e)
        {
            SPM_NET48_2021_2024.PluginMain.MainPaletteSet?.Activate(3); // History
        }

        private void NavImportBTN_Click(object sender, RoutedEventArgs e)
        {
            SPM_NET48_2021_2024.PluginMain.MainPaletteSet?.Activate(1); // Import
        }

        private void NavExportBTN_Click(object sender, RoutedEventArgs e)
        {
            SPM_NET48_2021_2024.PluginMain.MainPaletteSet?.Activate(2); // Export
        }

        #region display help
        /*
         * (command "_BROWSER" (strcat (getenv "APPDATA") "/Autodesk/ApplicationPlugins/SampleApp.bundledocs/help.htm"))
         * */
        private void NavHelpBTN_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                string bundleDir = GetCurrentPluginBundleDirectory();
                if (bundleDir != null)
                {
                    string helpFilePath = FindHelpInBundle(bundleDir);
                    if (!string.IsNullOrEmpty(helpFilePath) && File.Exists(helpFilePath))
                    {
                        Autodesk.AutoCAD.ApplicationServices.Application.DocumentManager.MdiActiveDocument.SendStringToExecute(
                            $"(command \"_BROWSER\" \"{helpFilePath.Replace("\\", "/")}\")\n",
                            true, false, false);
                    }
                    else
                    {
                        MessageBox.Show("Help file not found in plugin bundle.", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                else
                {
                    MessageBox.Show("Could not locate the plugin bundle directory.", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening help: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string GetCurrentPluginBundleDirectory()
        {
            var assembly = System.Reflection.Assembly.GetExecutingAssembly();
            string assemblyLocation = assembly.Location;
            string assemblyDir = Path.GetDirectoryName(assemblyLocation);

            DirectoryInfo dir = new DirectoryInfo(assemblyDir);
            while (dir != null && !dir.Name.EndsWith(".bundle", StringComparison.OrdinalIgnoreCase))
            {
                dir = dir.Parent;
            }

            return dir?.FullName;
        }

        private string FindHelpInBundle(string bundleDir)
        {
            string contentsDir = Path.Combine(bundleDir, "Contents");
            if (!Directory.Exists(contentsDir))
            {
                return null;
            }

            string[] helpFiles = Directory.GetFiles(contentsDir, "help.*");
            foreach (string file in helpFiles)
            {
                string ext = Path.GetExtension(file).ToLowerInvariant();
                if (ext == ".htm" || ext == ".html")
                {
                    return file;
                }
            }

            string[] htmlFiles = Directory.GetFiles(contentsDir, "*.htm*");
            return htmlFiles.Length > 0 ? htmlFiles[0] : null;
        }
        #endregion

        private void Viewbox_MouseLeftButtonDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            SPM_NET48_2021_2024.PluginMain.MainPaletteSet?.Activate(0);
        }
    }

}
