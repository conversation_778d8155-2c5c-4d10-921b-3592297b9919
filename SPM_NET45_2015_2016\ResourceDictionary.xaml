﻿<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="clr-namespace:SPM_NET45_2015_2016">

    <!--#region Fonts-->
    <FontFamily x:Key="PoppinsFont">/Fonts/#Poppins</FontFamily>
    <!--#endregion-->


    <!--#region Home Logo-->
    <ControlTemplate x:Key="HomeLogoTemplate" TargetType="ContentControl">
        <Viewbox
            xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
            xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
            Width="250.000"
            Height="126.766">
            <Canvas Width="250.000" Height="126.766">

                <Canvas>

                    <!-- drawing/<Compound Path> -->
                    <Path Data="F1 M 233.453,123.582 C 235.676,123.582 237.898,123.588 240.121,123.581 C 244.169,123.568 246.895,120.840 246.902,116.793 C 246.908,112.976 246.871,109.159 246.930,105.344 C 246.943,104.504 246.633,104.191 245.910,103.950 C 241.695,102.545 237.486,101.123 233.292,99.657 C 232.510,99.384 232.294,99.554 232.062,100.327 C 229.814,107.806 227.536,115.276 225.204,122.729 C 224.919,123.639 225.272,123.590 225.904,123.586 C 228.420,123.572 230.936,123.580 233.453,123.582 Z M 189.676,123.568 C 189.682,123.516 189.688,123.464 189.694,123.413 C 184.224,121.371 178.758,119.319 173.283,117.291 C 163.584,113.697 153.867,110.155 144.193,106.496 C 142.667,105.919 141.535,106.040 140.184,106.905 C 138.726,107.836 137.150,108.625 135.629,109.458 C 134.121,110.284 132.563,111.069 131.082,111.979 C 128.790,113.387 126.284,114.442 123.917,115.735 C 121.478,117.068 119.194,118.729 116.662,119.836 C 114.232,120.898 112.096,122.437 109.712,123.568 C 136.367,123.568 163.022,123.568 189.676,123.568 Z M 237.721,97.474 C 238.015,97.579 238.307,97.689 238.597,97.807 C 240.940,98.755 243.474,99.178 245.770,100.309 C 246.000,100.422 247.070,100.824 246.899,99.666 C 246.897,98.888 246.894,98.109 246.891,97.331 C 246.893,96.810 246.894,96.288 246.896,95.766 L 246.930,95.719 L 246.896,95.669 C 246.893,92.803 246.889,89.936 246.886,87.070 C 246.885,86.788 246.885,86.505 246.885,86.223 C 246.885,85.245 246.886,84.266 246.887,83.288 C 246.887,82.924 246.886,82.560 246.886,82.196 C 246.888,77.882 246.890,73.568 246.892,69.254 L 246.893,69.255 C 246.892,66.583 246.892,63.911 246.891,61.240 L 246.927,61.191 L 246.892,61.140 C 246.889,60.373 246.886,59.607 246.883,58.840 C 246.883,58.477 246.883,58.114 246.883,57.751 C 246.885,54.864 246.888,51.978 246.891,49.092 L 246.892,49.093 C 246.892,48.553 246.893,48.013 246.893,47.472 C 246.893,47.472 246.894,47.436 246.894,47.436 C 246.892,44.686 246.889,41.936 246.887,39.186 C 246.888,38.658 246.889,38.130 246.891,37.602 C 246.890,36.453 246.889,35.304 246.888,34.155 C 246.888,33.952 246.887,33.748 246.887,33.545 C 246.888,32.948 246.890,32.351 246.891,31.753 L 246.927,31.705 L 246.892,31.654 C 246.890,28.620 246.887,25.586 246.885,22.552 C 246.885,22.185 246.885,21.817 246.885,21.450 C 246.885,20.481 246.886,19.512 246.886,18.543 C 246.886,18.413 246.886,18.283 246.886,18.153 C 246.888,15.697 246.890,13.241 246.891,10.784 L 246.893,10.786 C 246.891,10.357 246.889,9.927 246.887,9.498 C 246.887,9.419 246.888,9.341 246.889,9.263 C 246.361,5.281 243.890,3.090 239.919,3.090 C 163.604,3.089 87.288,3.088 10.973,3.099 C 9.883,3.100 8.831,3.195 7.716,3.500 C 5.525,4.100 4.320,5.475 3.536,7.394 C 2.987,8.737 3.067,10.187 3.067,11.611 C 3.066,35.258 3.066,58.905 3.066,82.552 C 3.066,93.683 3.114,104.814 3.041,115.944 C 3.012,120.320 5.600,123.638 10.580,123.623 C 40.316,123.537 70.053,123.541 99.789,123.645 C 102.779,123.655 105.046,122.524 107.338,120.958 C 108.716,120.016 110.254,119.267 111.760,118.612 C 113.082,118.037 114.264,117.222 115.481,116.541 C 116.915,115.738 118.384,114.892 119.839,114.152 C 121.090,113.515 122.438,112.850 123.556,112.122 C 125.722,110.713 128.153,109.854 130.291,108.403 C 132.099,107.175 134.335,106.575 136.137,105.341 C 137.838,104.175 139.758,103.528 141.500,102.501 C 141.784,102.334 142.022,102.067 142.506,102.266 C 147.613,104.372 152.834,106.175 158.004,108.115 C 162.691,109.873 167.377,111.645 172.057,113.431 C 179.581,116.303 187.165,119.022 194.670,121.944 C 197.678,123.115 200.670,123.724 203.917,123.625 C 209.246,123.463 214.584,123.551 219.917,123.608 C 220.962,123.619 221.450,123.333 221.771,122.310 C 223.465,116.914 225.192,111.528 226.920,106.142 M 8.216,126.664 C 3.630,125.987 0.012,121.923 0.010,117.301 C -0.004,81.263 -0.005,45.226 0.018,9.189 C 0.019,6.560 0.977,4.106 2.919,2.415 C 4.725,0.843 6.986,-0.026 9.571,0.001 C 18.012,0.087 26.455,0.036 34.897,0.037 C 102.895,0.038 170.893,0.048 238.892,0.009 C 242.163,0.007 245.127,0.426 247.478,2.949 C 249.093,4.683 249.974,6.734 249.975,9.062 C 250.000,45.268 250.021,81.473 249.961,117.678 C 249.952,122.711 245.905,126.728 240.282,126.739 C 228.816,126.762 217.350,126.749 205.884,126.749 C 143.262,126.748 80.640,126.747 18.018,126.746 C 18.018,126.746 18.018,126.745 18.018,126.745 C 14.752,126.694 11.484,126.874 8.219,126.653 C 8.219,126.653 8.216,126.664 8.216,126.664 Z" Fill="#c7e0ef" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 8.216,126.664 C 8.216,126.664 8.219,126.653 8.219,126.653 C 8.219,126.653 8.216,126.664 8.216,126.664 Z" Fill="#ff7ac1e9" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 18.018,126.745 C 18.018,126.745 18.018,126.746 18.018,126.746 C 18.018,126.746 18.018,126.745 18.018,126.745 Z" Fill="#ff7ac1e9" />

                    <!-- drawing/<Compound Path> -->
                    <Path Data="F1 M 51.288,37.741 C 52.221,35.166 53.385,32.746 54.504,30.305 C 54.917,29.404 54.423,28.280 53.542,27.885 C 52.626,27.473 51.422,27.849 51.419,28.565 C 51.406,31.625 51.442,34.685 51.288,37.741 Z M 12.564,40.258 C 12.559,40.258 12.554,40.258 12.548,40.258 C 12.548,41.637 12.557,43.018 12.545,44.397 C 12.538,45.109 12.649,45.889 13.458,45.909 C 14.291,45.930 14.697,45.210 14.703,44.396 C 14.724,41.552 14.730,38.709 14.711,35.866 C 14.706,35.185 14.422,34.613 13.617,34.618 C 12.819,34.624 12.563,35.150 12.564,35.867 C 12.566,37.330 12.564,38.794 12.564,40.258 Z M 51.302,54.428 C 51.172,54.438 51.041,54.447 50.910,54.456 C 50.910,58.215 50.912,61.974 50.910,65.732 C 50.908,68.036 51.127,68.219 53.430,67.862 C 54.094,67.759 54.604,67.326 54.622,66.749 C 54.665,65.347 54.938,63.914 54.340,62.543 C 53.186,59.893 51.942,57.277 51.302,54.428 Z M 35.163,44.084 C 39.198,44.201 41.706,41.022 41.702,37.724 C 41.697,33.830 38.113,31.582 35.442,31.725 C 31.371,31.943 29.495,34.182 29.389,37.965 C 29.278,41.889 32.766,44.015 35.163,44.084 Z M 166.804,48.021 C 166.873,44.809 166.690,44.626 163.890,44.724 C 163.596,44.734 163.302,44.753 163.011,44.792 C 161.394,45.007 161.226,45.125 161.309,46.771 C 161.525,51.086 161.912,55.393 161.566,59.721 C 161.412,61.643 161.395,63.575 161.278,65.500 C 161.234,66.231 161.489,66.571 162.236,66.641 C 163.367,66.747 164.487,66.809 165.626,66.707 C 166.526,66.626 166.846,66.241 166.841,65.346 C 166.830,63.504 166.647,61.668 166.611,59.834 C 166.532,55.766 166.418,51.690 166.804,48.021 Z M 27.973,34.937 C 29.570,31.998 31.758,29.822 35.236,29.863 C 38.519,29.902 41.148,31.437 42.785,34.639 C 43.015,31.363 42.993,28.275 42.877,25.191 C 42.805,23.291 41.683,22.268 39.837,22.263 C 36.941,22.255 34.044,22.253 31.148,22.263 C 29.247,22.270 27.994,23.478 27.980,25.366 C 27.956,28.556 27.973,31.747 27.973,34.937 Z M 19.957,47.892 C 19.937,47.892 19.916,47.892 19.895,47.892 C 19.895,41.556 19.877,35.220 19.916,28.884 C 19.922,28.008 19.708,27.746 18.816,27.781 C 17.082,27.850 16.384,28.470 16.377,30.214 C 16.368,32.689 16.374,35.165 16.375,37.640 C 16.377,46.871 16.383,56.102 16.377,65.333 C 16.376,66.108 16.432,66.821 17.037,67.410 C 17.827,68.178 18.813,67.928 19.661,67.796 C 20.305,67.696 19.935,66.868 19.937,66.392 C 19.967,60.225 19.957,54.058 19.957,47.892 Z M 21.424,47.847 C 21.427,47.847 21.429,47.847 21.432,47.847 C 21.432,54.187 21.426,60.528 21.448,66.869 C 21.449,67.227 21.102,67.879 21.951,67.870 C 22.773,67.860 22.456,67.240 22.458,66.860 C 22.476,63.879 22.477,60.897 22.453,57.916 C 22.444,56.919 22.833,56.423 23.869,56.522 C 24.160,56.550 24.464,56.482 24.749,56.533 C 25.697,56.703 25.928,56.335 25.923,55.391 C 25.880,47.287 25.896,39.182 25.887,31.078 C 25.885,30.037 26.235,28.645 25.707,28.046 C 25.086,27.340 23.654,27.923 22.587,27.792 C 21.646,27.676 21.394,27.999 21.401,28.951 C 21.446,35.250 21.424,41.548 21.424,47.847 Z M 49.377,47.963 C 49.458,47.963 49.539,47.963 49.621,47.963 C 49.621,42.421 49.624,36.879 49.618,31.337 C 49.616,30.210 50.009,28.707 49.441,28.055 C 48.780,27.295 47.232,27.927 46.081,27.790 C 45.113,27.676 44.831,27.987 44.836,28.997 C 44.877,37.563 44.854,46.129 44.852,54.695 C 44.851,56.513 44.852,56.513 46.677,56.527 C 48.209,56.539 48.437,56.743 48.442,58.272 C 48.452,61.128 48.421,63.983 48.450,66.838 C 48.454,67.186 48.053,67.846 48.884,67.863 C 49.718,67.881 49.360,67.217 49.361,66.859 C 49.383,60.560 49.377,54.262 49.377,47.963 Z M 197.927,44.710 C 197.927,44.706 197.927,44.702 197.927,44.698 C 195.618,44.698 193.307,44.754 191.000,44.675 C 189.778,44.634 189.326,45.203 189.291,46.285 C 189.260,47.292 189.250,48.301 189.280,49.307 C 189.306,50.156 189.728,50.612 190.645,50.543 C 191.439,50.482 192.238,50.477 193.036,50.463 C 195.172,50.426 195.213,50.503 195.328,52.656 C 195.513,56.127 195.260,59.588 194.987,63.036 C 194.718,66.430 194.121,66.858 198.607,66.690 C 200.627,66.615 200.777,66.459 200.584,64.447 C 200.210,60.556 199.898,56.665 200.064,52.753 C 200.169,50.261 200.183,50.262 202.677,50.358 C 206.252,50.497 206.387,50.354 206.107,46.713 C 206.088,46.463 206.010,46.216 206.001,45.967 C 205.967,44.961 205.494,44.643 204.473,44.684 C 202.294,44.770 200.110,44.710 197.927,44.710 Z M 217.476,18.264 C 217.476,17.844 217.472,17.425 217.477,17.005 C 217.494,15.392 217.326,15.213 215.679,15.190 C 214.798,15.177 213.914,15.239 213.037,15.181 C 211.810,15.099 211.375,15.669 211.438,16.830 C 211.624,20.208 212.835,23.233 214.658,26.022 C 215.049,26.621 215.169,27.426 215.984,27.773 C 217.297,28.332 217.796,29.419 217.655,30.815 C 217.524,32.108 217.349,33.397 217.263,34.692 C 217.142,36.535 217.346,36.756 219.177,37.053 C 219.424,37.093 219.678,37.107 219.929,37.106 C 223.041,37.091 223.599,36.806 223.054,33.818 C 222.534,30.971 223.063,28.727 225.282,26.494 C 227.780,23.979 228.910,20.556 228.841,16.865 C 228.817,15.636 228.364,15.075 227.112,15.174 C 226.487,15.224 225.855,15.170 225.226,15.177 C 223.242,15.198 223.237,15.200 223.274,17.227 C 223.307,19.030 223.325,20.835 222.635,22.545 C 222.232,23.541 221.670,24.480 220.455,24.508 C 219.247,24.536 218.662,23.657 218.182,22.663 C 217.504,21.259 217.407,19.778 217.476,18.264 Z M 35.535,58.296 C 35.535,58.297 35.535,58.298 35.535,58.300 C 32.178,58.300 28.820,58.327 25.464,58.280 C 24.641,58.268 24.417,58.499 24.428,59.317 C 24.471,62.841 24.472,66.367 24.428,69.891 C 24.415,70.902 24.795,71.166 25.762,71.161 C 32.182,71.128 38.602,71.122 45.022,71.176 C 46.276,71.186 46.603,70.700 46.585,69.544 C 46.535,66.188 46.530,62.830 46.588,59.474 C 46.604,58.495 46.263,58.266 45.354,58.278 C 42.081,58.322 38.808,58.296 35.535,58.296 Z M 219.477,95.591 C 219.477,95.593 219.477,95.594 219.477,95.596 C 220.190,95.596 220.904,95.597 221.617,95.596 C 222.330,95.595 223.044,95.589 223.757,95.590 C 226.393,95.592 226.346,95.593 226.326,92.990 C 226.298,89.293 225.676,89.822 223.024,90.461 C 221.586,90.808 220.072,91.142 218.577,90.571 C 217.429,90.132 217.311,89.203 217.544,88.200 C 217.778,87.193 218.498,86.935 219.475,87.136 C 220.003,87.245 220.557,87.255 221.101,87.265 C 223.468,87.307 223.740,87.005 223.526,84.622 C 223.518,84.539 223.520,84.454 223.507,84.371 C 223.379,83.552 222.963,83.085 222.070,83.188 C 221.115,83.298 220.162,83.440 219.204,83.491 C 218.107,83.549 217.559,82.976 217.482,81.813 C 217.393,80.467 217.870,79.799 219.023,79.706 C 220.746,79.568 222.455,79.672 224.129,80.117 C 224.811,80.299 225.383,80.266 225.731,79.572 C 226.739,77.567 225.133,74.889 222.884,74.868 C 220.200,74.843 217.514,74.859 214.830,74.881 C 213.199,74.895 212.895,75.199 212.768,76.841 C 212.742,77.174 212.734,77.514 212.768,77.846 C 213.051,80.609 212.987,83.381 212.945,86.148 C 212.907,88.705 212.748,91.260 212.646,93.815 C 212.586,95.314 212.846,95.589 214.316,95.590 C 216.036,95.592 217.757,95.591 219.477,95.591 Z M 183.546,74.577 C 180.585,74.571 179.826,75.226 179.192,78.248 C 178.448,81.792 177.690,85.333 176.910,88.869 C 176.516,90.656 176.044,92.426 175.640,94.211 C 175.517,94.756 175.530,95.302 176.223,95.538 C 178.631,96.358 180.443,95.457 181.180,93.077 C 181.278,92.762 181.436,92.458 181.612,92.177 C 181.977,91.592 182.455,91.283 183.215,91.280 C 185.577,91.271 186.243,91.626 186.968,93.932 C 187.364,95.190 188.053,95.727 189.323,95.630 C 189.698,95.601 190.078,95.624 190.454,95.608 C 192.144,95.539 192.378,95.284 192.002,93.629 C 190.765,88.191 189.506,82.757 188.230,77.328 C 187.846,75.697 186.952,74.928 185.302,74.705 C 184.721,74.626 184.131,74.616 183.546,74.577 Z M 148.872,74.672 C 145.405,74.671 144.902,75.070 144.108,78.464 C 143.823,79.679 143.557,80.898 143.271,82.113 C 142.347,86.042 141.428,89.973 140.483,93.898 C 140.286,94.715 140.405,95.321 141.293,95.558 C 143.646,96.186 144.834,95.615 145.757,93.435 C 146.421,91.867 147.454,91.342 149.140,91.543 C 150.632,91.720 151.392,92.614 151.742,93.942 C 152.120,95.377 153.102,95.825 154.454,95.749 C 154.872,95.725 155.287,95.661 155.704,95.635 C 156.808,95.567 157.119,95.052 156.861,93.960 C 155.600,88.616 154.212,83.303 153.127,77.916 C 152.559,75.095 151.894,74.672 148.872,74.672 Z M 35.300,10.147 C 31.353,10.147 27.406,10.145 23.458,10.147 C 21.816,10.148 21.189,10.602 20.763,12.171 C 20.480,13.217 20.256,14.279 20.010,15.334 C 19.312,18.337 18.596,21.336 17.949,24.349 C 17.887,24.640 17.326,25.411 18.381,25.394 C 20.354,25.361 22.328,25.384 24.301,25.343 C 25.549,25.317 24.896,24.251 25.038,23.706 C 25.191,23.118 25.591,22.331 24.881,21.781 C 24.722,21.658 24.391,21.756 24.139,21.755 C 23.425,21.753 22.711,21.769 21.998,21.747 C 21.302,21.726 21.044,21.249 21.063,20.647 C 21.081,20.087 21.372,19.707 22.016,19.716 C 22.855,19.729 23.698,19.674 24.534,19.732 C 25.268,19.782 25.433,19.489 25.462,18.799 C 25.527,17.251 25.699,15.708 25.834,14.164 C 25.914,13.254 26.382,12.887 27.329,12.893 C 32.620,12.925 37.911,12.921 43.202,12.897 C 44.148,12.893 44.562,13.263 44.633,14.206 C 44.748,15.751 44.986,17.287 45.089,18.833 C 45.129,19.424 45.263,19.636 45.880,19.601 C 46.842,19.547 47.810,19.581 48.776,19.590 C 49.460,19.597 49.902,19.888 49.903,20.637 C 49.904,21.383 49.456,21.672 48.780,21.702 C 48.361,21.720 47.940,21.714 47.521,21.716 C 45.438,21.727 45.439,21.727 45.596,23.834 C 45.615,24.084 45.638,24.340 45.611,24.588 C 45.552,25.141 45.779,25.312 46.326,25.304 C 48.300,25.278 50.274,25.270 52.247,25.310 C 52.931,25.324 53.005,25.069 52.858,24.500 C 51.775,20.307 50.718,16.106 49.626,11.915 C 49.318,10.733 48.592,10.115 47.267,10.131 C 43.278,10.181 39.289,10.149 35.300,10.147 Z M 183.831,37.069 C 184.293,37.069 184.755,37.073 185.217,37.068 C 187.094,37.049 187.505,36.749 187.961,35.006 C 188.383,33.395 188.788,31.779 189.187,30.161 C 190.293,25.673 191.879,21.326 193.085,16.868 C 193.409,15.668 193.096,15.249 191.860,15.209 C 190.686,15.171 189.501,15.287 188.335,15.176 C 187.172,15.065 186.882,15.691 186.759,16.621 C 186.425,19.149 186.118,21.682 185.726,24.202 C 185.432,26.094 185.088,27.983 184.515,29.819 C 184.377,30.264 184.265,30.807 183.716,30.788 C 183.225,30.770 183.163,30.181 183.050,29.822 C 182.716,28.750 182.434,27.658 182.208,26.558 C 181.524,23.211 180.848,19.862 180.229,16.502 C 180.039,15.470 179.529,15.095 178.521,15.154 C 177.516,15.213 176.506,15.170 175.498,15.194 C 174.235,15.224 173.827,15.746 174.238,16.909 C 176.150,22.327 177.530,27.898 179.018,33.439 C 180.009,37.125 180.026,37.121 183.831,37.069 Z M 124.269,44.771 C 123.177,44.771 122.085,44.761 120.993,44.773 C 119.385,44.791 119.149,45.017 119.194,46.587 C 119.266,49.063 119.367,51.537 119.455,54.012 C 119.586,57.752 119.321,61.482 119.149,65.213 C 119.095,66.406 119.633,66.808 120.660,66.841 C 121.583,66.870 122.508,66.860 123.432,66.838 C 124.327,66.816 124.825,66.422 124.770,65.434 C 124.693,64.052 124.706,62.664 124.632,61.282 C 124.580,60.288 124.764,59.633 125.993,59.784 C 126.646,59.864 127.323,59.737 127.987,59.756 C 131.933,59.866 134.797,57.831 135.707,54.007 C 136.715,49.769 135.382,46.137 130.518,45.151 C 128.434,44.729 126.366,44.660 124.269,44.771 Z M 125.514,37.342 C 127.937,37.410 129.756,36.951 131.443,35.898 C 135.401,33.429 135.464,27.229 131.126,25.197 C 129.809,24.580 128.505,23.932 127.207,23.273 C 126.538,22.933 125.856,22.585 125.389,21.967 C 125.012,21.468 124.929,20.948 125.231,20.334 C 125.511,19.762 125.938,19.755 126.463,19.721 C 127.735,19.638 128.676,20.087 129.289,21.222 C 129.649,21.891 130.169,22.283 130.996,22.271 C 132.443,22.250 133.348,21.574 133.802,20.067 C 134.286,18.458 133.883,17.017 132.529,16.258 C 130.046,14.866 127.380,14.479 124.545,14.913 C 121.921,15.315 120.217,16.753 119.512,19.263 C 118.860,21.585 119.996,24.522 122.836,25.831 C 124.639,26.663 126.596,27.227 128.181,28.505 C 128.805,29.009 129.236,29.638 129.009,30.462 C 128.789,31.259 128.128,31.673 127.361,31.831 C 125.657,32.180 124.208,31.763 123.163,30.286 C 122.778,29.741 122.457,29.130 121.985,28.674 C 121.610,28.311 121.140,27.781 120.485,28.172 C 118.192,29.541 118.205,34.603 120.573,35.791 C 122.249,36.632 123.929,37.666 125.514,37.342 Z M 205.968,88.278 C 205.077,90.997 201.621,91.931 199.371,90.207 C 196.396,87.927 196.249,82.797 199.087,80.333 C 200.820,78.828 203.660,79.124 204.840,80.933 C 205.700,82.252 205.797,82.280 207.139,81.399 C 207.592,81.102 208.015,80.754 208.437,80.412 C 208.994,79.961 209.107,79.357 208.901,78.710 C 208.593,77.740 208.009,76.768 207.248,76.284 C 203.737,74.051 200.125,73.657 196.601,76.152 C 193.011,78.693 191.848,82.443 192.368,86.636 C 192.750,89.718 193.790,92.581 196.724,94.348 C 200.705,96.747 204.353,96.129 207.711,94.068 C 209.910,92.718 211.400,87.459 210.572,84.955 C 210.357,84.304 210.074,83.726 209.200,83.739 C 206.975,83.769 204.750,83.757 202.525,83.749 C 202.055,83.748 201.712,83.854 201.613,84.385 C 201.444,85.295 201.298,86.205 201.444,87.133 C 201.558,87.864 201.810,88.368 202.728,88.296 C 203.770,88.216 204.823,88.278 205.968,88.278 Z M 169.266,85.058 C 168.631,84.532 168.339,83.835 167.998,83.182 C 166.782,80.861 165.547,78.556 164.132,76.344 C 163.476,75.319 162.648,74.849 161.472,74.761 C 158.917,74.572 158.632,74.860 158.720,77.388 C 158.896,82.465 159.043,87.542 158.634,92.617 C 158.392,95.622 158.091,95.646 161.604,95.728 C 163.478,95.772 163.577,95.559 163.545,93.699 C 163.535,93.114 163.445,92.530 163.417,91.944 C 163.312,89.765 163.203,87.585 163.131,85.405 C 163.118,85.014 163.110,84.408 163.510,84.319 C 163.998,84.210 163.999,84.858 164.231,85.169 C 166.255,87.886 167.041,91.209 168.653,94.129 C 169.191,95.104 169.779,95.749 170.955,95.671 C 171.498,95.635 172.044,95.631 172.588,95.654 C 173.781,95.704 174.121,95.011 174.054,93.985 C 173.694,88.493 173.642,83.000 173.868,77.499 C 173.979,74.815 173.554,74.502 170.841,74.752 C 170.675,74.767 170.510,74.816 170.344,74.824 C 169.423,74.866 169.004,75.267 169.091,76.258 C 169.273,78.330 169.385,80.409 169.500,82.486 C 169.547,83.328 169.862,84.207 169.266,85.058 Z M 202.690,36.873 C 202.690,36.886 202.690,36.898 202.690,36.911 C 204.159,36.911 205.628,36.919 207.098,36.909 C 210.890,36.882 210.469,37.128 210.403,33.487 C 210.358,31.027 210.054,30.841 207.629,31.324 C 207.384,31.373 207.142,31.433 206.897,31.482 C 205.412,31.781 203.909,31.963 202.400,31.808 C 201.038,31.669 200.457,30.799 200.600,29.307 C 200.710,28.171 201.139,27.922 202.653,28.032 C 203.405,28.086 204.163,28.054 204.918,28.079 C 206.933,28.147 207.163,27.932 207.129,25.928 C 207.123,25.595 207.066,25.262 207.032,24.929 C 206.930,23.956 206.434,23.541 205.427,23.694 C 204.394,23.851 203.351,23.973 202.308,24.024 C 200.969,24.090 200.427,23.533 200.372,22.174 C 200.321,20.905 200.917,20.264 202.253,20.225 C 204.221,20.167 206.180,20.130 208.115,20.686 C 209.438,21.066 209.840,20.727 210.040,19.345 C 210.179,18.386 210.028,17.432 209.977,16.473 C 209.919,15.374 209.436,15.119 208.486,15.129 C 204.666,15.167 200.846,15.181 197.026,15.195 C 194.954,15.203 194.769,15.457 194.912,17.529 C 195.173,21.297 195.400,25.070 195.246,28.850 C 195.162,30.904 195.011,32.955 194.913,35.009 C 194.837,36.586 195.122,36.875 196.644,36.874 C 198.660,36.872 200.675,36.873 202.690,36.873 Z M 136.336,23.815 C 136.336,25.326 136.364,26.837 136.329,28.347 C 136.267,31.069 137.252,33.347 139.277,35.127 C 142.100,37.607 145.332,38.004 148.744,36.597 C 152.095,35.215 153.646,32.534 153.711,28.983 C 153.769,25.794 153.727,22.604 153.752,19.414 C 153.760,18.409 153.875,17.405 153.875,16.400 C 153.874,15.563 153.479,15.110 152.545,15.162 C 151.373,15.228 150.194,15.239 149.022,15.187 C 147.826,15.133 147.574,15.775 147.700,16.766 C 147.910,18.426 148.241,20.079 148.324,21.745 C 148.442,24.124 148.871,26.509 148.458,28.895 C 148.145,30.701 147.076,31.619 145.310,31.698 C 143.598,31.774 142.025,30.675 141.677,29.055 C 141.530,28.368 141.558,27.636 141.560,26.925 C 141.569,23.653 141.756,20.394 142.102,17.137 C 142.298,15.296 142.105,15.199 140.292,15.195 C 139.452,15.194 138.613,15.194 137.773,15.202 C 136.309,15.216 136.030,15.455 136.169,16.896 C 136.390,19.200 136.300,21.509 136.336,23.815 Z M 234.991,55.127 C 234.966,54.792 234.945,54.457 234.917,54.123 C 234.375,47.623 226.858,43.988 221.463,47.617 C 217.843,50.051 216.432,54.620 218.302,58.700 C 220.394,63.264 222.715,67.722 224.937,72.226 C 225.162,72.684 225.511,73.038 225.853,73.410 C 226.448,74.057 226.719,73.876 227.076,73.159 C 229.274,68.740 231.509,64.341 233.740,59.939 C 234.508,58.424 235.040,56.853 234.991,55.127 Z M 162.549,15.247 C 162.548,15.231 162.548,15.215 162.548,15.199 C 161.163,15.199 159.778,15.198 158.393,15.200 C 156.567,15.203 156.123,15.662 156.334,17.438 C 156.797,21.316 157.115,25.194 156.779,29.106 C 156.604,31.152 156.580,33.211 156.421,35.260 C 156.332,36.416 156.793,37.024 157.931,37.061 C 158.936,37.094 159.946,37.097 160.948,37.029 C 161.949,36.960 162.425,36.399 162.329,35.348 C 162.188,33.805 162.135,32.254 161.979,30.713 C 161.873,29.652 162.315,29.284 163.333,29.289 C 164.947,29.297 165.800,29.937 166.310,31.762 C 166.690,33.124 167.000,34.506 167.334,35.880 C 167.490,36.524 167.869,36.802 168.541,36.941 C 169.855,37.213 171.133,37.044 172.416,36.847 C 173.173,36.731 173.518,36.205 173.354,35.481 C 172.976,33.801 172.749,32.041 171.740,30.595 C 170.702,29.107 170.507,28.056 171.902,26.416 C 175.247,22.484 173.644,16.031 167.830,15.282 C 166.097,15.059 164.310,15.247 162.549,15.247 Z M 137.341,55.570 C 137.295,62.011 142.136,67.007 148.444,66.996 C 154.681,66.985 159.559,62.254 159.429,55.582 C 159.307,49.296 155.060,44.455 148.678,44.289 C 142.259,44.122 137.387,49.032 137.341,55.570 Z M 124.041,84.559 C 124.588,84.871 124.673,85.295 124.829,85.676 C 125.756,87.941 126.617,90.234 127.743,92.416 C 128.304,93.505 128.598,93.581 129.389,92.659 C 129.552,92.470 129.692,92.259 129.825,92.046 C 131.032,90.108 131.633,87.904 132.523,85.827 C 132.705,85.403 132.835,84.719 133.239,84.758 C 133.799,84.813 133.752,85.565 133.783,86.003 C 133.935,88.220 134.128,90.434 134.092,92.663 C 134.042,95.739 134.168,95.821 137.261,95.635 C 139.447,95.504 139.623,95.218 139.288,92.977 C 138.923,90.538 138.653,88.084 138.345,85.636 C 137.986,82.769 137.682,79.897 137.537,77.007 C 137.482,75.914 137.316,74.822 135.796,74.702 C 133.578,74.526 132.623,74.938 132.061,76.501 C 131.723,77.439 131.401,78.383 131.053,79.318 C 130.619,80.487 130.186,81.657 129.706,82.808 C 129.551,83.181 129.292,83.593 128.813,83.586 C 128.329,83.579 128.369,83.074 128.177,82.777 C 126.961,80.891 126.311,78.764 125.638,76.665 C 125.234,75.404 124.589,74.743 123.259,74.780 C 122.799,74.792 122.337,74.720 121.877,74.733 C 120.036,74.783 119.754,75.106 119.709,76.996 C 119.580,82.439 118.927,87.829 117.997,93.188 C 117.673,95.055 118.001,95.533 119.898,95.626 C 120.566,95.659 121.238,95.615 121.909,95.601 C 122.829,95.581 123.247,95.134 123.264,94.187 C 123.303,91.964 123.408,89.742 123.482,87.519 C 123.515,86.518 123.559,85.520 124.041,84.559 Z M 182.141,55.693 C 181.589,55.406 181.393,55.042 181.167,54.695 C 179.320,51.851 177.766,48.828 175.821,46.040 C 175.408,45.447 174.948,45.103 174.348,44.907 C 173.125,44.510 171.863,44.640 170.616,44.802 C 169.797,44.908 169.691,45.549 169.712,46.209 C 169.738,47.048 169.833,47.884 169.851,48.723 C 169.966,54.093 170.209,59.463 169.637,64.827 C 169.472,66.383 169.656,66.541 171.217,66.704 C 171.965,66.782 172.727,66.809 173.477,66.771 C 175.217,66.682 175.561,66.230 175.356,64.466 C 175.032,61.677 174.882,58.878 174.888,56.071 C 174.889,55.617 174.784,55.002 175.271,54.848 C 175.786,54.685 176.007,55.288 176.242,55.662 C 177.967,58.399 179.485,61.250 180.904,64.156 C 182.164,66.738 182.664,67.019 185.465,66.823 C 187.241,66.699 187.586,66.363 187.464,64.573 C 187.100,59.209 186.947,53.851 187.205,48.471 C 187.398,44.450 187.158,44.350 183.109,44.703 C 183.067,44.706 183.025,44.706 182.983,44.709 C 182.084,44.778 181.611,45.176 181.708,46.163 C 181.868,47.791 181.960,49.425 182.093,51.056 C 182.215,52.548 182.384,54.038 182.141,55.693 Z M 80.886,18.415 C 73.439,18.410 66.898,20.851 61.535,26.023 C 54.297,33.003 51.495,41.558 53.066,51.509 C 53.987,57.345 56.781,62.480 59.193,67.758 C 59.393,68.196 77.554,104.270 78.683,105.823 C 79.202,106.536 79.529,107.395 80.577,107.458 C 81.672,107.524 82.084,106.711 82.503,105.927 C 87.024,97.474 91.567,89.033 96.051,80.561 C 99.772,73.533 103.635,66.578 106.939,59.333 C 109.803,53.054 110.577,46.515 108.950,39.855 C 105.853,27.179 94.188,18.387 80.886,18.415 Z M 42.916,41.533 C 42.670,40.761 42.548,41.483 42.492,41.594 C 41.798,42.978 40.642,44.028 39.432,44.804 C 35.840,47.105 30.630,45.935 28.606,41.421 C 28.498,41.180 28.504,40.701 27.969,40.790 C 27.969,43.512 27.961,46.238 27.972,48.964 C 27.981,51.261 29.137,52.411 31.467,52.425 C 33.986,52.440 36.505,52.431 39.025,52.425 C 41.845,52.418 42.916,51.394 42.948,48.585 C 42.974,46.234 42.929,43.882 42.915,41.531 L 42.916,41.533 Z M 227.761,103.520 C 227.664,103.822 227.567,104.124 227.470,104.425 C 225.559,110.383 223.645,116.341 221.771,122.310 C 221.450,123.333 220.962,123.619 219.917,123.608 C 214.584,123.551 209.246,123.463 203.917,123.625 C 200.670,123.724 197.678,123.115 194.670,121.944 C 187.165,119.022 179.581,116.303 172.057,113.431 C 167.377,111.645 162.691,109.873 158.004,108.115 C 152.834,106.175 147.613,104.372 142.506,102.266 C 142.022,102.067 141.784,102.334 141.500,102.501 C 139.758,103.528 137.838,104.175 136.137,105.341 C 134.335,106.575 132.099,107.175 130.291,108.403 C 128.153,109.854 125.722,110.713 123.556,112.122 C 122.438,112.850 121.090,113.515 119.839,114.152 C 118.384,114.892 116.915,115.738 115.481,116.541 C 114.264,117.222 113.082,118.037 111.760,118.612 C 110.254,119.267 108.716,120.016 107.338,120.958 C 105.046,122.524 102.779,123.655 99.789,123.645 C 70.053,123.541 40.316,123.537 10.580,123.623 C 5.600,123.638 3.012,120.320 3.041,115.944 C 3.114,104.814 3.066,93.683 3.066,82.552 C 3.066,58.905 3.066,35.258 3.067,11.611 C 3.067,10.187 2.987,8.737 3.536,7.394 C 4.320,5.475 5.525,4.100 7.716,3.500 C 8.831,3.195 9.883,3.100 10.973,3.099 C 87.288,3.088 163.604,3.089 239.919,3.090 C 243.890,3.090 246.361,5.281 246.889,9.259 C 246.871,9.278 246.835,9.303 246.838,9.322 C 246.847,9.382 246.869,9.439 246.887,9.498 C 246.889,9.927 246.891,10.357 246.891,10.784 C 246.890,13.241 246.888,15.697 246.886,18.149 C 246.886,18.277 246.886,18.410 246.886,18.543 C 246.886,19.512 246.885,20.481 246.885,21.444 C 246.885,21.809 246.885,22.180 246.885,22.552 C 246.887,25.586 246.890,28.620 246.892,31.653 C 246.875,31.652 246.858,31.651 246.841,31.650 C 246.858,31.685 246.874,31.719 246.891,31.753 C 246.890,32.351 246.888,32.948 246.887,33.539 C 246.887,33.741 246.888,33.948 246.888,34.155 C 246.889,35.304 246.890,36.453 246.891,37.595 C 246.889,38.121 246.888,38.653 246.887,39.186 C 246.889,41.936 246.892,44.686 246.894,47.436 C 246.894,47.436 246.846,47.451 246.846,47.451 L 246.893,47.472 C 246.893,48.013 246.892,48.553 246.891,49.092 C 246.888,51.978 246.885,54.864 246.883,57.744 C 246.883,58.106 246.883,58.473 246.883,58.840 C 246.886,59.607 246.889,60.373 246.892,61.139 C 246.875,61.138 246.858,61.137 246.841,61.137 C 246.857,61.171 246.874,61.205 246.891,61.240 C 246.892,63.911 246.892,66.583 246.892,69.254 C 246.890,73.568 246.888,77.882 246.886,82.190 C 246.886,82.552 246.887,82.920 246.887,83.288 C 246.886,84.266 246.885,85.245 246.885,86.217 C 246.885,86.497 246.885,86.784 246.886,87.070 C 246.889,89.936 246.893,92.803 246.896,95.669 C 246.880,95.667 246.863,95.666 246.847,95.666 C 246.863,95.699 246.879,95.733 246.896,95.766 C 246.894,96.288 246.893,96.810 246.891,97.324 C 246.894,98.101 246.897,98.883 246.899,99.666 C 247.070,100.824 246.000,100.422 245.770,100.309 C 243.474,99.178 240.940,98.755 238.597,97.807 C 238.335,97.700 238.071,97.600 237.805,97.504 L 227.761,103.520 Z" Fill="#ffebebeb" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 189.676,123.568 C 163.022,123.568 136.367,123.568 109.712,123.568 C 112.096,122.437 114.232,120.898 116.662,119.836 C 119.194,118.729 121.478,117.068 123.917,115.735 C 126.284,114.442 128.790,113.387 131.082,111.979 C 132.563,111.069 134.121,110.284 135.629,109.458 C 137.150,108.625 138.726,107.836 140.184,106.905 C 141.535,106.040 142.667,105.919 144.193,106.496 C 153.867,110.155 163.584,113.697 173.283,117.291 C 178.758,119.319 184.224,121.371 189.694,123.413 C 189.688,123.464 189.682,123.516 189.676,123.568 Z" Fill="#ffebebeb" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 233.453,123.581 C 230.936,123.580 228.420,123.572 225.904,123.586 C 225.272,123.590 224.919,123.639 225.204,122.729 C 227.536,115.276 229.814,107.806 232.062,100.327 C 232.294,99.554 232.510,99.384 233.292,99.657 C 233.717,99.806 234.142,99.954 234.567,100.101 C 238.339,101.411 242.122,102.688 245.910,103.950 C 246.633,104.191 246.943,104.504 246.930,105.344 C 246.871,109.159 246.908,112.976 246.902,116.793 C 246.895,120.840 244.169,123.568 240.121,123.581 C 237.898,123.588 235.676,123.582 233.453,123.581 Z" Fill="#ffebebeb" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 246.899,99.666 C 246.897,98.883 246.894,98.101 246.891,97.324 C 246.894,98.109 246.897,98.888 246.899,99.666 Z" Fill="#ff7ac1e9" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 246.887,39.186 C 246.888,38.653 246.889,38.121 246.891,37.595 C 246.889,38.130 246.888,38.658 246.887,39.186 Z" Fill="#ff7ac1e9" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 246.885,22.552 C 246.885,22.180 246.885,21.809 246.885,21.444 C 246.885,21.817 246.885,22.185 246.885,22.552 Z" Fill="#ff7ac1e9" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 246.887,83.288 C 246.887,82.920 246.886,82.552 246.886,82.190 C 246.886,82.560 246.887,82.924 246.887,83.288 Z" Fill="#ff7ac1e9" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 246.883,58.840 C 246.883,58.473 246.883,58.106 246.883,57.744 C 246.883,58.114 246.883,58.477 246.883,58.840 Z" Fill="#ff7ac1e9" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 246.886,87.070 C 246.885,86.784 246.885,86.497 246.885,86.217 C 246.885,86.505 246.885,86.788 246.886,87.070 Z" Fill="#ff7ac1e9" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 246.888,34.155 C 246.888,33.948 246.887,33.741 246.887,33.539 C 246.887,33.748 246.888,33.952 246.888,34.155 Z" Fill="#ff7ac1e9" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 246.886,18.543 C 246.886,18.410 246.886,18.277 246.886,18.149 C 246.886,18.283 246.886,18.413 246.886,18.543 Z" Fill="#ff7ac1e9" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 246.887,9.498 C 246.869,9.439 246.847,9.382 246.838,9.322 C 246.835,9.303 246.871,9.278 246.889,9.259 C 246.888,9.341 246.887,9.419 246.887,9.498 Z" Fill="#ff7ac1e9" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 246.891,61.240 C 246.874,61.205 246.857,61.171 246.841,61.137 C 246.858,61.137 246.875,61.138 246.892,61.139 C 246.892,61.140 246.927,61.191 246.927,61.191 L 246.891,61.240 Z" Fill="#ff7ac1e9" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 246.896,95.766 C 246.879,95.733 246.863,95.699 246.847,95.666 C 246.863,95.666 246.880,95.667 246.896,95.669 C 246.896,95.669 246.930,95.719 246.930,95.719 L 246.896,95.766 Z" Fill="#ff7ac1e9" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 246.891,31.753 C 246.874,31.719 246.858,31.685 246.841,31.650 C 246.858,31.651 246.875,31.652 246.892,31.653 C 246.892,31.654 246.927,31.705 246.927,31.705 L 246.891,31.753 Z" Fill="#ff7ac1e9" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 246.893,47.472 L 246.846,47.451 C 246.846,47.451 246.894,47.436 246.894,47.436 C 246.894,47.436 246.893,47.472 246.893,47.472 Z" Fill="#ff7ac1e9" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 246.891,10.784 C 246.891,10.784 246.893,10.786 246.893,10.786 C 246.893,10.786 246.891,10.784 246.891,10.784 Z" Fill="#ff7ac1e9" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 246.891,49.092 C 246.891,49.092 246.892,49.093 246.892,49.093 C 246.892,49.093 246.891,49.092 246.891,49.092 Z" Fill="#ff7ac1e9" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 246.892,69.254 L 246.893,69.255 C 246.893,69.255 246.892,69.254 246.892,69.254 Z" Fill="#ff7ac1e9" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 182.141,55.693 C 182.384,54.038 182.215,52.548 182.093,51.056 C 181.960,49.425 181.868,47.791 181.708,46.163 C 181.611,45.176 182.084,44.778 182.983,44.709 C 183.025,44.706 183.067,44.706 183.109,44.703 C 187.158,44.350 187.398,44.450 187.205,48.471 C 186.947,53.851 187.100,59.209 187.464,64.573 C 187.586,66.363 187.241,66.699 185.465,66.823 C 182.664,67.019 182.164,66.738 180.904,64.156 C 179.485,61.250 177.967,58.399 176.242,55.662 C 176.007,55.288 175.786,54.685 175.271,54.848 C 174.784,55.002 174.889,55.617 174.888,56.071 C 174.882,58.878 175.032,61.677 175.356,64.466 C 175.561,66.230 175.217,66.682 173.477,66.771 C 172.727,66.809 171.965,66.782 171.217,66.704 C 169.656,66.541 169.472,66.383 169.637,64.827 C 170.209,59.463 169.966,54.093 169.851,48.723 C 169.833,47.884 169.738,47.048 169.712,46.209 C 169.691,45.549 169.797,44.908 170.616,44.802 C 171.863,44.640 173.125,44.510 174.348,44.907 C 174.948,45.103 175.408,45.447 175.821,46.040 C 177.766,48.828 179.320,51.851 181.167,54.695 C 181.393,55.042 181.589,55.406 182.141,55.693 Z" Fill="#ff6e778b" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 124.041,84.559 C 123.559,85.520 123.515,86.518 123.482,87.519 C 123.408,89.742 123.303,91.964 123.264,94.187 C 123.247,95.134 122.829,95.581 121.909,95.601 C 121.238,95.615 120.566,95.659 119.898,95.626 C 118.001,95.533 117.673,95.055 117.997,93.188 C 118.927,87.829 119.580,82.439 119.709,76.996 C 119.754,75.106 120.036,74.783 121.877,74.733 C 122.337,74.720 122.799,74.792 123.259,74.780 C 124.589,74.743 125.234,75.404 125.638,76.665 C 126.311,78.764 126.961,80.891 128.177,82.777 C 128.369,83.074 128.329,83.579 128.813,83.586 C 129.292,83.593 129.551,83.181 129.706,82.808 C 130.186,81.657 130.619,80.487 131.053,79.318 C 131.401,78.383 131.723,77.439 132.061,76.501 C 132.623,74.938 133.578,74.526 135.796,74.702 C 137.316,74.822 137.482,75.914 137.537,77.007 C 137.682,79.897 137.986,82.769 138.345,85.636 C 138.653,88.084 138.923,90.538 139.288,92.977 C 139.623,95.218 139.447,95.504 137.261,95.635 C 134.168,95.821 134.042,95.739 134.092,92.663 C 134.128,90.434 133.935,88.220 133.783,86.003 C 133.752,85.565 133.799,84.813 133.239,84.758 C 132.835,84.719 132.705,85.403 132.523,85.827 C 131.633,87.904 131.032,90.108 129.825,92.046 C 129.692,92.259 129.552,92.470 129.389,92.659 C 128.598,93.581 128.304,93.505 127.743,92.416 C 126.617,90.234 125.756,87.941 124.829,85.676 C 124.673,85.295 124.588,84.871 124.041,84.559 Z" Fill="#ff6e778b" />

                    <!-- drawing/<Compound Path> -->
                    <Path Data="F1 M 154.241,55.796 C 154.249,52.211 151.711,49.574 148.396,49.432 C 145.492,49.307 142.423,52.440 142.483,55.659 C 142.549,59.158 145.023,61.823 148.386,61.823 C 151.726,61.823 154.233,59.242 154.241,55.796 Z M 137.341,55.570 C 137.387,49.032 142.259,44.122 148.678,44.289 C 155.060,44.455 159.307,49.296 159.429,55.582 C 159.559,62.254 154.681,66.985 148.444,66.996 C 142.136,67.007 137.295,62.011 137.341,55.570 Z" Fill="#ff6e778b" />

                    <!-- drawing/<Compound Path> -->
                    <Path Data="F1 M 164.336,25.486 C 165.347,25.509 166.310,25.273 167.144,24.742 C 167.957,24.224 168.422,23.388 168.300,22.411 C 168.178,21.431 167.917,20.647 166.634,20.318 C 165.189,19.947 163.910,20.394 162.561,20.497 C 162.395,20.510 162.185,20.718 162.100,20.887 C 161.521,22.043 161.606,23.231 162.035,24.404 C 162.438,25.505 163.422,25.442 164.336,25.486 Z M 162.549,15.247 C 164.310,15.247 166.097,15.059 167.830,15.282 C 173.644,16.031 175.247,22.484 171.902,26.416 C 170.507,28.056 170.702,29.107 171.740,30.595 C 172.749,32.041 172.976,33.801 173.354,35.481 C 173.518,36.205 173.173,36.731 172.416,36.847 C 171.133,37.044 169.855,37.213 168.541,36.941 C 167.869,36.802 167.490,36.524 167.334,35.880 C 167.000,34.506 166.690,33.124 166.310,31.762 C 165.800,29.937 164.947,29.297 163.333,29.289 C 162.315,29.284 161.873,29.652 161.979,30.713 C 162.135,32.254 162.188,33.805 162.329,35.348 C 162.425,36.399 161.949,36.960 160.948,37.029 C 159.946,37.097 158.936,37.094 157.931,37.061 C 156.793,37.024 156.332,36.416 156.421,35.260 C 156.580,33.211 156.604,31.152 156.779,29.106 C 157.115,25.194 156.797,21.316 156.334,17.438 C 156.123,15.662 156.567,15.203 158.393,15.200 C 159.778,15.198 161.163,15.199 162.548,15.199 C 162.548,15.215 162.548,15.231 162.549,15.247 Z" Fill="#ff6e778b" />

                    <!-- drawing/<Compound Path> -->
                    <Path Data="F1 M 230.326,55.376 C 230.322,53.053 228.436,51.059 226.262,51.080 C 224.029,51.101 222.138,52.967 222.123,55.165 C 222.105,57.644 223.941,59.716 226.173,59.736 C 228.414,59.756 230.330,57.744 230.326,55.376 Z M 234.991,55.127 C 235.040,56.853 234.508,58.424 233.740,59.939 C 231.509,64.341 229.274,68.740 227.076,73.159 C 226.719,73.876 226.448,74.057 225.853,73.410 C 225.511,73.038 225.162,72.684 224.937,72.226 C 222.715,67.722 220.394,63.264 218.302,58.700 C 216.432,54.620 217.843,50.051 221.463,47.617 C 226.858,43.988 234.375,47.623 234.917,54.123 C 234.945,54.457 234.966,54.792 234.991,55.127 Z" Fill="#ff7ac1e9" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 136.336,23.815 C 136.300,21.509 136.390,19.200 136.169,16.896 C 136.030,15.455 136.309,15.216 137.773,15.202 C 138.613,15.194 139.452,15.194 140.292,15.195 C 142.105,15.199 142.298,15.296 142.102,17.137 C 141.756,20.394 141.569,23.653 141.560,26.925 C 141.558,27.636 141.530,28.368 141.677,29.055 C 142.025,30.675 143.598,31.774 145.310,31.698 C 147.076,31.619 148.145,30.701 148.458,28.895 C 148.871,26.509 148.442,24.124 148.324,21.745 C 148.241,20.079 147.910,18.426 147.700,16.766 C 147.574,15.775 147.826,15.133 149.022,15.187 C 150.194,15.239 151.373,15.228 152.545,15.162 C 153.479,15.110 153.874,15.563 153.875,16.400 C 153.875,17.405 153.760,18.409 153.752,19.414 C 153.727,22.604 153.769,25.794 153.711,28.983 C 153.646,32.534 152.095,35.215 148.744,36.597 C 145.332,38.004 142.100,37.607 139.277,35.127 C 137.252,33.347 136.267,31.069 136.329,28.347 C 136.364,26.837 136.336,25.326 136.336,23.815 Z" Fill="#ff6e778b" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 202.690,36.873 C 200.675,36.873 198.660,36.872 196.644,36.874 C 195.122,36.875 194.837,36.586 194.913,35.009 C 195.011,32.955 195.162,30.904 195.246,28.850 C 195.400,25.070 195.173,21.297 194.912,17.529 C 194.769,15.457 194.954,15.203 197.026,15.195 C 200.846,15.181 204.666,15.167 208.486,15.129 C 209.436,15.119 209.919,15.374 209.977,16.473 C 210.028,17.432 210.179,18.386 210.040,19.345 C 209.840,20.727 209.438,21.066 208.115,20.686 C 206.180,20.130 204.221,20.167 202.253,20.225 C 200.917,20.264 200.321,20.905 200.372,22.174 C 200.427,23.533 200.969,24.090 202.308,24.024 C 203.351,23.973 204.394,23.851 205.427,23.694 C 206.434,23.541 206.930,23.956 207.032,24.929 C 207.066,25.262 207.123,25.595 207.129,25.928 C 207.163,27.932 206.933,28.147 204.918,28.079 C 204.163,28.054 203.405,28.086 202.653,28.032 C 201.139,27.922 200.710,28.171 200.600,29.307 C 200.457,30.799 201.038,31.669 202.400,31.808 C 203.909,31.963 205.412,31.781 206.897,31.482 C 207.142,31.433 207.384,31.373 207.629,31.324 C 210.054,30.841 210.358,31.027 210.403,33.487 C 210.469,37.128 210.890,36.882 207.098,36.909 C 205.628,36.919 204.159,36.911 202.690,36.911 C 202.690,36.898 202.690,36.886 202.690,36.873 Z" Fill="#ff6e778b" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 169.266,85.058 C 169.862,84.207 169.547,83.328 169.500,82.486 C 169.385,80.409 169.273,78.330 169.091,76.258 C 169.004,75.267 169.423,74.866 170.344,74.824 C 170.510,74.816 170.675,74.767 170.841,74.752 C 173.554,74.502 173.979,74.815 173.868,77.499 C 173.642,83.000 173.694,88.493 174.054,93.985 C 174.121,95.011 173.781,95.704 172.588,95.654 C 172.044,95.631 171.498,95.635 170.955,95.671 C 169.779,95.749 169.191,95.104 168.653,94.129 C 167.041,91.209 166.255,87.886 164.231,85.169 C 163.999,84.858 163.998,84.210 163.510,84.319 C 163.110,84.408 163.118,85.014 163.131,85.405 C 163.203,87.585 163.312,89.765 163.417,91.944 C 163.445,92.530 163.535,93.114 163.545,93.699 C 163.577,95.559 163.478,95.772 161.604,95.728 C 158.091,95.646 158.392,95.622 158.634,92.617 C 159.043,87.542 158.896,82.465 158.720,77.388 C 158.632,74.860 158.917,74.572 161.472,74.761 C 162.648,74.849 163.476,75.319 164.132,76.344 C 165.547,78.556 166.782,80.861 167.998,83.182 C 168.339,83.835 168.631,84.532 169.266,85.058 Z" Fill="#ff6e778b" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 205.968,88.278 C 204.823,88.278 203.770,88.216 202.728,88.296 C 201.810,88.368 201.558,87.864 201.444,87.133 C 201.298,86.205 201.444,85.295 201.613,84.385 C 201.712,83.854 202.055,83.748 202.525,83.749 C 204.750,83.757 206.975,83.769 209.200,83.739 C 210.074,83.726 210.357,84.304 210.572,84.955 C 211.400,87.459 209.910,92.718 207.711,94.068 C 204.353,96.129 200.705,96.747 196.724,94.348 C 193.790,92.581 192.750,89.718 192.368,86.636 C 191.848,82.443 193.011,78.693 196.601,76.152 C 200.125,73.657 203.737,74.051 207.248,76.284 C 208.009,76.768 208.593,77.740 208.901,78.710 C 209.107,79.357 208.994,79.961 208.437,80.412 C 208.015,80.754 207.592,81.102 207.139,81.399 C 205.797,82.280 205.700,82.252 204.840,80.933 C 203.660,79.124 200.820,78.828 199.087,80.333 C 196.249,82.797 196.396,87.927 199.371,90.207 C 201.621,91.931 205.077,90.997 205.968,88.278 Z" Fill="#ff6e778b" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 125.514,37.342 C 123.929,37.666 122.249,36.632 120.573,35.791 C 118.205,34.603 118.192,29.541 120.485,28.172 C 121.140,27.781 121.610,28.311 121.985,28.674 C 122.457,29.130 122.778,29.741 123.163,30.286 C 124.208,31.763 125.657,32.180 127.361,31.831 C 128.128,31.673 128.789,31.259 129.009,30.462 C 129.236,29.638 128.805,29.009 128.181,28.505 C 126.596,27.227 124.639,26.663 122.836,25.831 C 119.996,24.522 118.860,21.585 119.512,19.263 C 120.217,16.753 121.921,15.315 124.545,14.913 C 127.380,14.479 130.046,14.866 132.529,16.258 C 133.883,17.017 134.286,18.458 133.802,20.067 C 133.348,21.574 132.443,22.250 130.996,22.271 C 130.169,22.283 129.649,21.891 129.289,21.222 C 128.676,20.087 127.735,19.638 126.463,19.721 C 125.938,19.755 125.511,19.762 125.231,20.334 C 124.929,20.948 125.012,21.468 125.389,21.967 C 125.856,22.585 126.538,22.933 127.207,23.273 C 128.505,23.932 129.809,24.580 131.126,25.197 C 135.464,27.229 135.401,33.429 131.443,35.898 C 129.756,36.951 127.937,37.410 125.514,37.342 Z" Fill="#ff6e778b" />

                    <!-- drawing/<Compound Path> -->
                    <Path Data="F1 M 127.427,49.454 C 125.086,49.455 124.087,50.327 124.093,52.364 C 124.101,54.827 124.964,55.738 127.261,55.706 C 129.676,55.673 130.948,54.573 130.918,52.543 C 130.888,50.541 129.660,49.454 127.427,49.454 Z M 124.269,44.771 C 126.366,44.660 128.434,44.729 130.518,45.151 C 135.382,46.137 136.715,49.769 135.707,54.007 C 134.797,57.831 131.933,59.866 127.987,59.756 C 127.323,59.737 126.646,59.864 125.993,59.784 C 124.764,59.633 124.580,60.288 124.632,61.282 C 124.706,62.664 124.693,64.052 124.770,65.434 C 124.825,66.422 124.327,66.816 123.432,66.838 C 122.508,66.860 121.583,66.870 120.660,66.841 C 119.633,66.808 119.095,66.406 119.149,65.213 C 119.321,61.482 119.586,57.752 119.455,54.012 C 119.367,51.537 119.266,49.063 119.194,46.587 C 119.149,45.017 119.385,44.791 120.993,44.773 C 122.085,44.761 123.177,44.771 124.269,44.771 Z" Fill="#ff6e778b" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 183.831,37.069 C 180.026,37.121 180.009,37.125 179.018,33.439 C 177.530,27.898 176.150,22.327 174.238,16.909 C 173.827,15.746 174.235,15.224 175.498,15.194 C 176.506,15.170 177.516,15.213 178.521,15.154 C 179.529,15.095 180.039,15.470 180.229,16.502 C 180.848,19.862 181.524,23.211 182.208,26.558 C 182.434,27.658 182.716,28.750 183.050,29.822 C 183.163,30.181 183.225,30.770 183.716,30.788 C 184.265,30.807 184.377,30.264 184.515,29.819 C 185.088,27.983 185.432,26.094 185.726,24.202 C 186.118,21.682 186.425,19.149 186.759,16.621 C 186.882,15.691 187.172,15.065 188.335,15.176 C 189.501,15.287 190.686,15.171 191.860,15.209 C 193.096,15.249 193.409,15.668 193.085,16.868 C 191.879,21.326 190.293,25.673 189.187,30.161 C 188.788,31.779 188.383,33.395 187.961,35.006 C 187.505,36.749 187.094,37.049 185.217,37.068 C 184.755,37.073 184.293,37.069 183.831,37.069 Z" Fill="#ff6e778b" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 35.300,10.148 C 39.289,10.149 43.278,10.181 47.267,10.131 C 48.592,10.115 49.318,10.733 49.626,11.915 C 50.718,16.106 51.775,20.307 52.858,24.500 C 53.005,25.069 52.931,25.324 52.247,25.310 C 50.274,25.270 48.300,25.278 46.326,25.304 C 45.779,25.312 45.552,25.141 45.611,24.588 C 45.638,24.340 45.615,24.084 45.596,23.834 C 45.439,21.727 45.438,21.727 47.521,21.716 C 47.940,21.714 48.361,21.720 48.780,21.702 C 49.456,21.672 49.904,21.383 49.903,20.637 C 49.902,19.888 49.460,19.597 48.776,19.590 C 47.810,19.581 46.842,19.547 45.880,19.601 C 45.263,19.636 45.129,19.424 45.089,18.833 C 44.986,17.287 44.748,15.751 44.633,14.206 C 44.562,13.263 44.148,12.893 43.202,12.897 C 37.911,12.921 32.620,12.925 27.329,12.893 C 26.382,12.887 25.914,13.254 25.834,14.164 C 25.699,15.708 25.527,17.251 25.462,18.799 C 25.433,19.489 25.268,19.782 24.534,19.732 C 23.698,19.674 22.855,19.729 22.016,19.716 C 21.372,19.707 21.081,20.087 21.063,20.647 C 21.044,21.249 21.302,21.726 21.998,21.747 C 22.711,21.769 23.425,21.753 24.139,21.755 C 24.391,21.756 24.722,21.658 24.881,21.781 C 25.591,22.331 25.191,23.118 25.038,23.706 C 24.896,24.251 25.549,25.317 24.301,25.343 C 22.328,25.384 20.354,25.361 18.381,25.394 C 17.326,25.411 17.887,24.640 17.949,24.349 C 18.596,21.336 19.312,18.337 20.010,15.334 C 20.256,14.279 20.480,13.217 20.763,12.171 C 21.189,10.602 21.816,10.148 23.458,10.147 C 27.406,10.145 31.353,10.147 35.300,10.148 Z" Fill="#ff6e778b" />

                    <!-- drawing/<Compound Path> -->
                    <Path Data="F1 M 148.637,79.418 C 147.837,81.669 147.186,83.627 146.934,85.684 C 146.801,86.773 147.402,87.452 148.466,87.518 C 149.508,87.582 150.083,87.062 150.185,86.007 C 150.405,83.722 149.333,81.773 148.637,79.418 Z M 148.872,74.672 C 151.894,74.672 152.559,75.095 153.127,77.916 C 154.212,83.303 155.600,88.616 156.861,93.960 C 157.119,95.052 156.808,95.567 155.704,95.635 C 155.287,95.661 154.872,95.725 154.454,95.749 C 153.102,95.825 152.120,95.377 151.742,93.942 C 151.392,92.614 150.632,91.720 149.140,91.543 C 147.454,91.342 146.421,91.867 145.757,93.435 C 144.834,95.615 143.646,96.186 141.293,95.558 C 140.405,95.321 140.286,94.715 140.483,93.898 C 141.428,89.973 142.347,86.042 143.271,82.113 C 143.557,80.898 143.823,79.679 144.108,78.464 C 144.902,75.070 145.405,74.671 148.872,74.672 Z" Fill="#ff6e778b" />

                    <!-- drawing/<Compound Path> -->
                    <Path Data="F1 M 184.094,79.606 C 183.153,81.482 182.427,83.287 181.986,85.199 C 181.812,85.948 182.141,86.511 182.748,86.917 C 183.436,87.378 184.169,87.424 184.871,86.974 C 185.426,86.619 185.790,86.129 185.690,85.401 C 185.421,83.456 185.072,81.534 184.094,79.606 Z M 183.546,74.576 C 184.131,74.616 184.721,74.626 185.302,74.705 C 186.952,74.928 187.846,75.697 188.230,77.328 C 189.506,82.757 190.765,88.191 192.002,93.629 C 192.378,95.284 192.144,95.539 190.454,95.608 C 190.078,95.624 189.698,95.601 189.323,95.630 C 188.053,95.727 187.364,95.190 186.968,93.932 C 186.243,91.626 185.577,91.271 183.215,91.280 C 182.455,91.283 181.977,91.592 181.612,92.177 C 181.436,92.458 181.278,92.762 181.180,93.077 C 180.443,95.457 178.631,96.358 176.223,95.538 C 175.530,95.302 175.517,94.756 175.640,94.211 C 176.044,92.426 176.516,90.656 176.910,88.869 C 177.690,85.333 178.448,81.792 179.192,78.248 C 179.826,75.226 180.585,74.571 183.546,74.576 Z" Fill="#ff6e778b" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 219.477,95.591 C 217.757,95.591 216.036,95.592 214.316,95.590 C 212.846,95.589 212.586,95.314 212.646,93.815 C 212.748,91.260 212.907,88.705 212.945,86.148 C 212.987,83.381 213.051,80.609 212.768,77.846 C 212.734,77.514 212.742,77.174 212.768,76.841 C 212.895,75.199 213.199,74.895 214.830,74.881 C 217.514,74.859 220.200,74.843 222.884,74.868 C 225.133,74.889 226.739,77.567 225.731,79.572 C 225.383,80.266 224.811,80.299 224.129,80.117 C 222.455,79.672 220.746,79.568 219.023,79.706 C 217.870,79.799 217.393,80.467 217.482,81.813 C 217.559,82.976 218.107,83.549 219.204,83.491 C 220.162,83.440 221.115,83.298 222.070,83.188 C 222.963,83.085 223.379,83.552 223.507,84.371 C 223.520,84.454 223.518,84.539 223.526,84.622 C 223.740,87.005 223.468,87.307 221.101,87.265 C 220.557,87.255 220.003,87.245 219.475,87.136 C 218.498,86.935 217.778,87.193 217.544,88.200 C 217.311,89.203 217.429,90.132 218.577,90.571 C 220.072,91.142 221.586,90.808 223.024,90.461 C 225.676,89.822 226.298,89.293 226.326,92.990 C 226.346,95.593 226.393,95.592 223.757,95.590 C 223.044,95.589 222.330,95.595 221.617,95.596 C 220.904,95.597 220.190,95.596 219.477,95.596 C 219.477,95.594 219.477,95.593 219.477,95.591 Z" Fill="#ff6e778b" />

                    <!-- drawing/<Compound Path> -->
                    <Path Data="F1 M 43.892,62.397 C 43.864,61.401 43.291,60.904 42.475,60.904 C 41.597,60.904 41.000,61.417 41.036,62.408 C 41.069,63.301 41.572,63.828 42.431,63.822 C 43.339,63.816 43.855,63.263 43.892,62.397 Z M 43.896,66.787 C 43.875,65.666 43.279,65.194 42.473,65.194 C 41.597,65.193 40.986,65.720 41.019,66.690 C 41.050,67.571 41.586,68.099 42.436,68.099 C 43.351,68.099 43.844,67.521 43.896,66.787 Z M 32.563,67.373 C 33.903,67.373 35.243,67.351 36.582,67.383 C 37.266,67.399 37.582,67.173 37.569,66.451 C 37.540,64.860 37.535,63.268 37.567,61.678 C 37.582,60.979 37.289,60.663 36.620,60.662 C 33.898,60.658 31.177,60.654 28.456,60.661 C 27.825,60.663 27.440,60.920 27.451,61.641 C 27.475,63.232 27.480,64.824 27.450,66.414 C 27.435,67.218 27.834,67.401 28.544,67.383 C 29.883,67.349 31.223,67.373 32.563,67.373 Z M 35.535,58.296 C 38.808,58.296 42.081,58.322 45.354,58.278 C 46.263,58.266 46.604,58.495 46.588,59.474 C 46.530,62.830 46.535,66.188 46.585,69.544 C 46.603,70.700 46.276,71.186 45.022,71.176 C 38.602,71.122 32.182,71.128 25.762,71.161 C 24.795,71.166 24.415,70.902 24.428,69.891 C 24.472,66.367 24.471,62.841 24.428,59.317 C 24.417,58.499 24.641,58.268 25.464,58.280 C 28.820,58.327 32.178,58.300 35.535,58.300 C 35.535,58.298 35.535,58.297 35.535,58.296 Z" Fill="#ff6e778b" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 217.476,18.264 C 217.407,19.778 217.504,21.259 218.182,22.663 C 218.662,23.657 219.247,24.536 220.455,24.508 C 221.670,24.480 222.232,23.541 222.635,22.545 C 223.325,20.835 223.307,19.030 223.274,17.227 C 223.237,15.200 223.242,15.198 225.226,15.177 C 225.855,15.170 226.487,15.224 227.112,15.174 C 228.364,15.075 228.817,15.636 228.841,16.865 C 228.910,20.556 227.780,23.979 225.282,26.494 C 223.063,28.727 222.534,30.971 223.054,33.818 C 223.599,36.806 223.041,37.091 219.929,37.106 C 219.678,37.107 219.424,37.093 219.177,37.053 C 217.346,36.756 217.142,36.535 217.263,34.692 C 217.349,33.397 217.524,32.108 217.655,30.815 C 217.796,29.419 217.297,28.332 215.984,27.773 C 215.169,27.426 215.049,26.621 214.658,26.022 C 212.835,23.233 211.624,20.208 211.438,16.830 C 211.375,15.669 211.810,15.099 213.037,15.181 C 213.914,15.239 214.798,15.177 215.679,15.190 C 217.326,15.213 217.494,15.392 217.477,17.005 C 217.472,17.425 217.476,17.844 217.476,18.264 Z" Fill="#ff6e778b" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 197.927,44.710 C 200.110,44.710 202.294,44.770 204.473,44.684 C 205.494,44.643 205.967,44.961 206.001,45.967 C 206.010,46.216 206.088,46.463 206.107,46.713 C 206.387,50.354 206.252,50.497 202.677,50.358 C 200.183,50.262 200.169,50.261 200.064,52.753 C 199.898,56.665 200.210,60.556 200.584,64.447 C 200.777,66.459 200.627,66.615 198.607,66.690 C 194.121,66.858 194.718,66.430 194.987,63.036 C 195.260,59.588 195.513,56.127 195.328,52.656 C 195.213,50.503 195.172,50.426 193.036,50.463 C 192.238,50.477 191.439,50.482 190.645,50.543 C 189.728,50.612 189.306,50.156 189.280,49.307 C 189.250,48.301 189.260,47.292 189.291,46.285 C 189.326,45.203 189.778,44.634 191.000,44.675 C 193.307,44.754 195.618,44.698 197.927,44.698 C 197.927,44.702 197.927,44.706 197.927,44.710 Z" Fill="#ff6e778b" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 49.377,47.963 C 49.377,54.262 49.383,60.560 49.361,66.859 C 49.360,67.217 49.718,67.881 48.884,67.863 C 48.053,67.846 48.454,67.186 48.450,66.838 C 48.421,63.983 48.452,61.128 48.442,58.272 C 48.437,56.743 48.209,56.539 46.677,56.527 C 44.852,56.513 44.851,56.513 44.852,54.695 C 44.854,46.129 44.877,37.563 44.836,28.997 C 44.831,27.987 45.113,27.676 46.081,27.790 C 47.232,27.927 48.780,27.295 49.441,28.055 C 50.009,28.707 49.616,30.210 49.618,31.337 C 49.624,36.879 49.621,42.421 49.621,47.963 C 49.539,47.963 49.458,47.963 49.377,47.963 Z" Fill="#ff6e778b" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 21.424,47.847 C 21.424,41.548 21.446,35.250 21.401,28.951 C 21.394,27.999 21.646,27.676 22.587,27.792 C 23.654,27.923 25.086,27.340 25.707,28.046 C 26.235,28.645 25.885,30.037 25.887,31.078 C 25.896,39.182 25.880,47.287 25.923,55.391 C 25.928,56.335 25.697,56.703 24.749,56.533 C 24.464,56.482 24.160,56.550 23.869,56.522 C 22.833,56.423 22.444,56.919 22.453,57.916 C 22.477,60.897 22.476,63.879 22.458,66.860 C 22.456,67.240 22.773,67.860 21.951,67.870 C 21.102,67.879 21.449,67.227 21.448,66.869 C 21.426,60.528 21.432,54.187 21.432,47.847 C 21.429,47.847 21.427,47.847 21.424,47.847 Z" Fill="#ff6e778b" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 19.957,47.892 C 19.957,54.058 19.967,60.225 19.937,66.392 C 19.935,66.868 20.305,67.696 19.661,67.796 C 18.813,67.928 17.827,68.178 17.037,67.410 C 16.432,66.821 16.376,66.108 16.377,65.333 C 16.383,56.102 16.377,46.871 16.375,37.640 C 16.374,35.165 16.368,32.689 16.377,30.214 C 16.384,28.470 17.082,27.850 18.816,27.781 C 19.708,27.746 19.922,28.008 19.916,28.884 C 19.877,35.220 19.895,41.556 19.895,47.892 C 19.916,47.892 19.937,47.892 19.957,47.892 Z" Fill="#ff6e778b" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 27.973,34.937 C 27.973,31.747 27.956,28.556 27.980,25.366 C 27.994,23.478 29.247,22.270 31.148,22.263 C 34.044,22.253 36.941,22.255 39.837,22.263 C 41.683,22.268 42.805,23.291 42.877,25.191 C 42.993,28.275 43.015,31.363 42.785,34.639 C 41.148,31.437 38.519,29.902 35.236,29.863 C 31.758,29.822 29.570,31.998 27.973,34.937 Z" Fill="#ff6e778b" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 42.915,41.531 C 42.929,43.882 42.974,46.234 42.948,48.585 C 42.916,51.394 41.845,52.418 39.025,52.425 C 36.505,52.431 33.986,52.440 31.467,52.425 C 29.137,52.411 27.981,51.261 27.972,48.964 C 27.961,46.238 27.969,43.512 27.969,40.790 C 28.504,40.701 28.498,41.180 28.606,41.421 C 30.630,45.935 35.840,47.105 39.432,44.804 C 40.642,44.028 41.798,42.978 42.492,41.594 C 42.548,41.483 42.670,40.761 42.916,41.533 C 42.916,41.533 42.915,41.531 42.915,41.531 Z" Fill="#ff6e778b" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 166.804,48.021 C 166.418,51.690 166.532,55.766 166.611,59.834 C 166.647,61.668 166.830,63.504 166.841,65.346 C 166.846,66.241 166.526,66.626 165.626,66.707 C 164.487,66.809 163.367,66.747 162.236,66.641 C 161.489,66.571 161.234,66.231 161.278,65.500 C 161.395,63.575 161.412,61.643 161.566,59.721 C 161.912,55.393 161.525,51.086 161.309,46.771 C 161.226,45.125 161.394,45.007 163.011,44.792 C 163.302,44.753 163.596,44.734 163.890,44.724 C 166.690,44.626 166.873,44.809 166.804,48.021 Z" Fill="#ff6e778b" />

                    <!-- drawing/<Compound Path> -->
                    <Path Data="F1 M 35.473,41.942 C 37.840,41.964 39.645,40.218 39.642,37.910 C 39.639,35.600 37.928,33.846 35.663,33.829 C 33.199,33.811 31.450,35.508 31.445,37.921 C 31.441,40.214 33.150,41.920 35.473,41.942 Z M 35.163,44.084 C 32.766,44.015 29.278,41.889 29.389,37.965 C 29.495,34.182 31.371,31.943 35.442,31.725 C 38.113,31.582 41.697,33.830 41.702,37.724 C 41.706,41.022 39.198,44.201 35.163,44.084 Z" Fill="#ff7ac1e9" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 51.302,54.428 C 51.942,57.277 53.186,59.893 54.340,62.543 C 54.938,63.914 54.665,65.347 54.622,66.749 C 54.604,67.326 54.094,67.759 53.430,67.862 C 51.127,68.219 50.908,68.036 50.910,65.732 C 50.912,61.974 50.910,58.215 50.910,54.456 C 51.041,54.447 51.172,54.438 51.302,54.428 Z" Fill="#ff6e778b" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 12.564,40.258 C 12.564,38.794 12.566,37.330 12.564,35.867 C 12.563,35.150 12.819,34.624 13.617,34.618 C 14.422,34.613 14.706,35.185 14.711,35.866 C 14.730,38.709 14.724,41.552 14.703,44.396 C 14.697,45.210 14.291,45.930 13.458,45.909 C 12.649,45.889 12.538,45.109 12.545,44.397 C 12.557,43.018 12.548,41.637 12.548,40.258 C 12.554,40.258 12.559,40.258 12.564,40.258 Z" Fill="#ff6e778b" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 51.288,37.741 C 51.442,34.685 51.406,31.625 51.419,28.565 C 51.422,27.849 52.626,27.473 53.542,27.885 C 54.423,28.280 54.917,29.404 54.504,30.305 C 53.385,32.746 52.221,35.166 51.288,37.741 Z" Fill="#ff6e778b" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 42.915,41.531 C 42.915,41.531 42.916,41.533 42.916,41.533 C 42.916,41.533 42.915,41.531 42.915,41.531 Z" Fill="#ff7ac1e9" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 154.241,55.796 C 154.233,59.242 151.726,61.823 148.386,61.823 C 145.023,61.823 142.549,59.158 142.483,55.659 C 142.423,52.440 145.492,49.307 148.396,49.432 C 151.711,49.574 154.249,52.211 154.241,55.796 Z" Fill="#ffebebeb" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 164.336,25.486 C 163.422,25.442 162.438,25.505 162.035,24.404 C 161.606,23.231 161.521,22.043 162.100,20.887 C 162.185,20.718 162.395,20.510 162.561,20.497 C 163.910,20.394 165.189,19.947 166.634,20.318 C 167.917,20.647 168.178,21.431 168.300,22.411 C 168.422,23.388 167.957,24.224 167.144,24.742 C 166.310,25.273 165.347,25.509 164.336,25.486 Z" Fill="#ffebebeb" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 230.326,55.376 C 230.330,57.744 228.414,59.756 226.173,59.736 C 223.941,59.716 222.105,57.644 222.123,55.165 C 222.138,52.967 224.029,51.101 226.262,51.080 C 228.436,51.059 230.322,53.053 230.326,55.376 Z" Fill="#ffebebeb" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 127.427,49.454 C 129.660,49.454 130.888,50.541 130.918,52.543 C 130.948,54.573 129.676,55.673 127.261,55.706 C 124.964,55.738 124.101,54.827 124.093,52.364 C 124.087,50.327 125.086,49.455 127.427,49.454 Z" Fill="#ffebebeb" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 148.637,79.418 C 149.333,81.773 150.405,83.722 150.185,86.007 C 150.083,87.062 149.508,87.582 148.466,87.518 C 147.402,87.452 146.801,86.773 146.934,85.684 C 147.186,83.627 147.837,81.669 148.637,79.418 Z" Fill="#ffebebeb" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 184.094,79.606 C 185.072,81.534 185.421,83.456 185.690,85.401 C 185.790,86.129 185.426,86.619 184.871,86.974 C 184.169,87.424 183.436,87.378 182.748,86.917 C 182.141,86.511 181.812,85.948 181.986,85.199 C 182.427,83.287 183.153,81.482 184.094,79.606 Z" Fill="#ffebebeb" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 32.563,67.373 C 31.223,67.373 29.883,67.349 28.544,67.383 C 27.834,67.401 27.435,67.218 27.450,66.414 C 27.480,64.824 27.475,63.232 27.451,61.641 C 27.440,60.920 27.825,60.663 28.456,60.661 C 31.177,60.654 33.898,60.658 36.620,60.662 C 37.289,60.663 37.582,60.979 37.567,61.678 C 37.535,63.268 37.540,64.860 37.569,66.451 C 37.582,67.173 37.266,67.399 36.582,67.383 C 35.243,67.351 33.903,67.373 32.563,67.373 Z" Fill="#ffebebeb" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 43.896,66.787 C 43.844,67.521 43.351,68.099 42.436,68.099 C 41.586,68.099 41.050,67.571 41.019,66.690 C 40.986,65.720 41.597,65.193 42.473,65.194 C 43.279,65.194 43.875,65.666 43.896,66.787 Z" Fill="#ffebebeb" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 43.892,62.397 C 43.855,63.263 43.339,63.816 42.431,63.822 C 41.572,63.828 41.069,63.301 41.036,62.408 C 41.000,61.417 41.597,60.904 42.475,60.904 C 43.291,60.904 43.864,61.401 43.892,62.397 Z" Fill="#ffebebeb" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 35.473,41.942 C 33.150,41.920 31.441,40.214 31.445,37.921 C 31.450,35.508 33.199,33.811 35.663,33.829 C 37.928,33.846 39.639,35.600 39.642,37.910 C 39.645,40.218 37.840,41.964 35.473,41.942 Z" Fill="#ffebebeb" />

                    <!-- drawing/<Compound Path> -->
                    <Path Data="F1 M 236.231,84.285 C 237.194,84.308 238.112,84.083 238.906,83.577 C 239.681,83.083 240.124,82.287 240.008,81.356 C 239.891,80.422 239.643,79.675 238.421,79.362 C 237.043,79.009 235.825,79.434 234.540,79.533 C 234.382,79.545 234.182,79.743 234.101,79.904 C 233.549,81.006 233.631,82.137 234.039,83.255 C 234.423,84.304 235.360,84.244 236.231,84.285 Z M 234.528,74.531 C 236.207,74.531 237.909,74.351 239.560,74.564 C 245.099,75.277 246.627,81.425 243.439,85.171 C 242.110,86.734 242.296,87.736 243.285,89.153 C 244.246,90.531 244.463,92.207 244.823,93.808 C 244.978,94.498 244.650,94.999 243.929,95.110 C 242.707,95.297 241.490,95.458 240.238,95.199 C 239.597,95.066 239.236,94.802 239.087,94.189 C 238.769,92.879 238.474,91.562 238.111,90.265 C 237.626,88.526 236.813,87.916 235.275,87.909 C 234.306,87.904 233.884,88.254 233.986,89.265 C 234.134,90.733 234.184,92.211 234.319,93.681 C 234.411,94.683 233.957,95.218 233.003,95.283 C 232.049,95.348 231.086,95.345 230.129,95.313 C 229.045,95.278 228.605,94.699 228.691,93.597 C 228.842,91.646 228.865,89.684 229.032,87.735 C 229.351,84.008 229.048,80.313 228.608,76.618 C 228.406,74.926 228.829,74.489 230.569,74.486 C 231.889,74.484 233.208,74.485 234.528,74.485 C 234.528,74.501 234.528,74.516 234.528,74.531 Z" Fill="#ff6e778b" />

                    <Canvas>

                        <!-- drawing/<Group>/<Path> -->
                        <Path Data="F1 M 93.009,57.450 C 92.998,58.316 91.945,59.357 91.085,59.358 C 90.724,59.358 90.485,59.175 90.277,58.897 C 88.495,56.519 86.707,54.147 84.922,51.772 C 84.800,51.610 84.639,51.470 84.854,51.237 C 85.091,50.979 85.254,51.136 85.442,51.277 C 87.815,53.064 90.187,54.853 92.564,56.635 C 92.844,56.845 93.021,57.091 93.009,57.450 Z" Fill="#ff6e778b" />

                        <Canvas>

                            <!-- drawing/<Group>/<Group>/<Path> -->
                            <Path Data="F1 M 78.294,43.747 C 78.043,43.256 78.489,43.165 78.748,43.011 C 80.701,41.852 83.369,42.307 84.990,44.061 C 86.580,45.783 86.880,48.563 85.507,50.360 C 83.806,52.588 81.529,53.477 78.727,52.054 C 76.375,50.860 75.427,48.007 76.483,45.506 C 76.644,45.125 76.659,44.456 77.404,44.638 C 78.544,44.893 78.544,44.893 78.294,43.747 Z" Fill="#ff6e778b" />

                            <!-- drawing/<Group>/<Group>/<Path> -->
                            <Path Data="F1 M 70.826,35.668 C 71.339,35.509 71.700,35.702 72.011,36.123 C 73.895,38.676 75.792,41.219 77.684,43.765 C 77.740,43.841 77.804,43.910 77.875,43.971 C 78.011,44.090 78.140,44.215 77.925,44.401 C 77.755,44.548 77.644,44.426 77.540,44.300 C 77.477,44.224 77.403,44.158 77.324,44.098 C 74.807,42.193 72.292,40.284 69.769,38.387 C 69.351,38.073 69.159,37.710 69.317,37.192 C 69.625,37.177 69.571,37.510 69.737,37.631 C 70.236,38.023 70.661,37.779 71.020,37.428 C 71.398,37.058 71.661,36.619 71.257,36.087 C 71.136,35.924 70.809,35.974 70.826,35.668 Z" Fill="#ff6e778b" />

                            <!-- drawing/<Group>/<Group>/<Path> -->
                            <Path Data="F1 M 70.886,35.700 C 71.024,35.886 71.323,35.710 71.438,35.947 C 71.356,36.076 71.221,36.116 71.085,36.140 C 70.362,36.268 69.940,36.690 69.813,37.413 C 69.789,37.549 69.749,37.684 69.619,37.766 C 69.382,37.652 69.559,37.352 69.372,37.215 C 69.580,36.413 70.085,35.909 70.886,35.700 Z" Fill="#ff6e778b" />

                        </Canvas>

                    </Canvas>

                    <Canvas>

                        <Canvas>

                            <!-- drawing/LWPOLYLINE/<Group>/<Path> -->
                            <Path Data="F1 M 69.115,77.487 L 70.560,79.115 C 70.810,79.307 71.171,79.277 71.385,79.037 C 71.551,78.851 71.585,78.581 71.476,78.361 L 66.333,72.476 C 66.123,72.248 65.747,72.227 65.495,72.449 L 62.750,74.834 C 63.870,77.055 65.177,79.641 66.552,82.355 C 66.910,80.947 67.172,79.516 67.315,78.071 C 67.355,77.662 67.633,77.316 68.023,77.190 C 68.415,77.064 68.842,77.180 69.115,77.487 Z" Fill="#ff7ac1e9" />

                            <!-- drawing/LWPOLYLINE/<Group>/<Path> -->
                            <Path Data="F1 M 81.640,85.031 C 78.015,85.161 74.544,86.801 72.108,89.697 C 71.718,90.157 71.355,90.640 71.014,91.137 C 71.523,92.136 72.025,93.119 72.513,94.073 C 73.382,92.120 74.561,90.301 76.032,88.708 C 77.632,87.048 79.545,85.804 81.640,85.031 Z" Fill="#ff7ac1e9" />

                            <!-- drawing/LWPOLYLINE/<Group>/<Compound Path> -->
                            <Path Data="F1 M 80.831,67.162 C 70.341,67.200 60.937,57.733 61.268,47.009 C 61.623,35.473 71.160,27.230 81.493,27.548 C 92.446,27.884 101.027,36.813 100.770,47.951 C 100.532,58.230 92.209,67.553 80.831,67.162 Z M 108.950,39.855 C 105.853,27.179 94.188,18.387 80.886,18.415 C 73.439,18.410 66.898,20.851 61.535,26.023 C 54.297,33.003 51.495,41.558 53.066,51.509 C 53.987,57.345 56.781,62.480 59.193,67.758 C 59.239,67.859 60.251,69.876 61.790,72.931 L 64.138,70.892 C 65.238,69.931 66.879,70.019 67.870,71.097 L 73.097,77.077 C 73.133,77.118 73.165,77.162 73.193,77.207 C 73.825,78.210 73.715,79.528 72.925,80.412 C 71.943,81.513 70.250,81.609 69.149,80.627 C 69.119,80.600 69.091,80.572 69.064,80.542 L 69.062,80.539 C 68.795,82.070 68.413,83.581 67.920,85.053 C 68.599,86.391 69.284,87.739 69.963,89.074 C 70.148,88.834 70.334,88.596 70.530,88.365 C 74.517,83.624 80.907,81.792 86.804,83.702 C 86.850,83.716 86.894,83.735 86.937,83.755 C 87.270,83.917 87.517,84.215 87.613,84.573 C 87.703,84.900 87.659,85.246 87.488,85.543 C 87.318,85.841 87.042,86.054 86.711,86.143 C 86.633,86.164 86.552,86.177 86.471,86.179 C 83.070,86.274 79.896,87.676 77.534,90.125 C 75.828,91.973 74.554,94.165 73.769,96.523 C 74.539,98.024 75.257,99.415 75.894,100.641 C 76.048,100.719 76.197,100.808 76.336,100.918 C 76.893,101.356 77.245,101.985 77.329,102.689 C 77.355,102.914 77.344,103.135 77.315,103.354 C 78.038,104.719 78.526,105.606 78.683,105.823 C 79.202,106.536 79.529,107.395 80.577,107.458 C 81.672,107.524 82.084,106.711 82.503,105.927 C 87.024,97.474 91.567,89.033 96.051,80.561 C 99.772,73.533 103.635,66.578 106.939,59.333 C 109.803,53.054 110.577,46.515 108.950,39.855 Z" Fill="#ff7ac1e9" />

                        </Canvas>

                        <!-- drawing/LWPOLYLINE/<Path> -->
                        <Path
                            Data="F1 M 72.320,77.757 L 67.110,71.796 C 66.506,71.139 65.487,71.084 64.815,71.672 L 58.880,76.828 C 58.279,77.492 58.331,78.517 58.995,79.117 C 59.563,79.631 60.413,79.677 61.033,79.228 L 62.927,77.609 C 62.960,82.799 60.838,87.769 57.068,91.335 C 54.353,93.793 50.779,95.085 47.120,94.932 C 46.993,94.921 46.881,95.015 46.870,95.142 C 46.860,95.250 46.928,95.351 47.033,95.383 C 52.381,97.535 58.500,96.229 62.504,92.081 C 64.510,90.017 66.020,87.524 66.920,84.790 C 67.644,82.645 68.121,80.425 68.342,78.172 L 69.837,79.857 C 70.513,80.460 71.551,80.401 72.155,79.725 C 72.642,79.179 72.709,78.376 72.320,77.757"
                            Fill="#ff6e778b"
                            Stroke="#ff6e778b"
                            StrokeEndLineCap="Round"
                            StrokeLineJoin="Round"
                            StrokeStartLineCap="Round"
                            StrokeThickness="0.8" />

                    </Canvas>

                    <!-- drawing/<Path> -->
                    <Path
                        Data="F1 M 62.495,103.989 L 68.096,109.583 C 68.744,110.198 69.764,110.184 70.395,109.552 L 75.967,104.005 C 76.521,103.301 76.400,102.282 75.697,101.728 C 75.095,101.255 74.245,101.266 73.656,101.756 L 71.876,103.500 C 71.491,98.324 73.271,93.222 76.791,89.408 C 79.326,86.779 82.791,85.249 86.442,85.146 C 86.574,85.110 86.652,84.974 86.616,84.842 C 86.598,84.773 86.550,84.715 86.486,84.684 C 81.016,82.913 75.018,84.633 71.317,89.032 C 69.456,91.228 68.118,93.818 67.405,96.606 C 66.829,98.795 66.504,101.043 66.435,103.305 L 64.830,101.726 C 64.114,101.169 63.082,101.299 62.526,102.014 C 62.077,102.592 62.064,103.397 62.495,103.989"
                        Fill="#ff6e778b"
                        Stroke="#ff6e778b"
                        StrokeEndLineCap="Round"
                        StrokeLineJoin="Round"
                        StrokeStartLineCap="Round"
                        StrokeThickness="0.8" />

                    <Canvas>

                        <!-- drawing/LWPOLYLINE/<Path> -->
                        <Path
                            Data="F1 M 43.369,75.571 L 27.478,75.571 C 27.090,75.571 26.749,75.313 26.643,74.940 L 26.167,73.262 C 26.145,73.185 26.134,73.106 26.134,73.025 C 26.134,72.546 26.522,72.158 27.001,72.158 L 43.844,72.158 C 43.924,72.158 44.004,72.169 44.080,72.191 C 44.541,72.321 44.809,72.801 44.679,73.262 L 44.204,74.940 C 44.098,75.313 43.757,75.571 43.369,75.571 Z"
                            Fill="#ff6e778b"
                            Stroke="#ff6e778b"
                            StrokeEndLineCap="Round"
                            StrokeLineJoin="Round"
                            StrokeStartLineCap="Round"
                            StrokeThickness="1.1" />

                    </Canvas>

                    <Canvas>

                        <!-- drawing/LWPOLYLINE/<Path> -->
                        <Path
                            Data="F1 M 24.514,77.977 L 12.995,107.737 C 12.902,107.978 13.022,108.249 13.263,108.342 C 13.316,108.363 13.374,108.374 13.431,108.374 L 15.761,108.374 L 29.167,77.587 L 25.083,77.587 C 24.831,77.587 24.605,77.742 24.514,77.977 Z"
                            Fill="#ff6e778b"
                            Stroke="#ff6e778b"
                            StrokeEndLineCap="Round"
                            StrokeLineJoin="Round"
                            StrokeStartLineCap="Round"
                            StrokeThickness="1.1" />

                    </Canvas>

                    <Canvas>

                        <!-- drawing/LINE/<Path> -->
                        <Path
                            Data="F1 M 23.135,81.539 L 26.759,83.117"
                            Fill="#ff0f2239"
                            Stroke="#ff7ac1e9"
                            StrokeEndLineCap="Round"
                            StrokeLineJoin="Round"
                            StrokeStartLineCap="Round"
                            StrokeThickness="1.1" />

                    </Canvas>

                    <Canvas>

                        <!-- drawing/LINE/<Path> -->
                        <Path
                            Data="F1 M 22.507,83.163 L 26.064,84.713"
                            Fill="#ff0f2239"
                            Stroke="#ff7ac1e9"
                            StrokeEndLineCap="Round"
                            StrokeLineJoin="Round"
                            StrokeStartLineCap="Round"
                            StrokeThickness="1.1" />

                    </Canvas>

                    <Canvas>

                        <!-- drawing/LWPOLYLINE/<Path> -->
                        <Path
                            Data="F1 M 48.668,93.635 L 41.680,77.587 L 45.764,77.587 C 46.016,77.587 46.242,77.742 46.333,77.977 L 52.173,93.065 C 51.033,93.401 49.855,93.592 48.668,93.635 Z"
                            Fill="#ff6e778b"
                            Stroke="#ff6e778b"
                            StrokeEndLineCap="Round"
                            StrokeLineJoin="Round"
                            StrokeStartLineCap="Round"
                            StrokeThickness="1.1" />

                    </Canvas>

                    <Canvas>

                        <!-- drawing/LWPOLYLINE/<Path> -->
                        <Path
                            Data="F1 M 54.211,98.332 L 57.852,107.737 C 57.872,107.791 57.883,107.848 57.883,107.906 C 57.883,108.164 57.674,108.374 57.416,108.374 L 55.086,108.374 L 50.625,98.131 C 51.810,98.325 53.012,98.392 54.211,98.332 Z"
                            Fill="#ff6e778b"
                            Stroke="#ff6e778b"
                            StrokeEndLineCap="Round"
                            StrokeLineJoin="Round"
                            StrokeStartLineCap="Round"
                            StrokeThickness="1.1" />

                    </Canvas>

                    <Canvas>

                        <!-- drawing/LINE/<Path> -->
                        <Path
                            Data="F1 M 47.711,81.539 L 44.088,83.117"
                            Stroke="#ff7ac1e9"
                            StrokeEndLineCap="Round"
                            StrokeLineJoin="Round"
                            StrokeStartLineCap="Round"
                            StrokeThickness="1.1" />

                    </Canvas>

                    <Canvas>

                        <!-- drawing/LINE/<Path> -->
                        <Path
                            Data="F1 M 48.340,83.163 L 44.782,84.713"
                            Fill="#ff6e778b"
                            Stroke="#ff7ac1e9"
                            StrokeEndLineCap="Round"
                            StrokeLineJoin="Round"
                            StrokeStartLineCap="Round"
                            StrokeThickness="1.1" />

                    </Canvas>

                    <Canvas>

                        <!-- drawing/LWPOLYLINE/<Path> -->
                        <Path
                            Data="F1 M 39.015,77.587 L 31.522,77.587 L 32.243,108.374 L 38.295,108.374 L 39.015,77.585"
                            Fill="#ff6e778b"
                            Stroke="#ff6e778b"
                            StrokeEndLineCap="Round"
                            StrokeLineJoin="Round"
                            StrokeStartLineCap="Round"
                            StrokeThickness="1.1" />

                    </Canvas>

                    <Canvas>

                        <!-- drawing/LINE/<Path> -->
                        <Path
                            Data="F1 M 38.886,83.117 L 31.651,83.117"
                            Fill="#ff6e778b"
                            Stroke="#ff7ac1e9"
                            StrokeEndLineCap="Round"
                            StrokeLineJoin="Round"
                            StrokeStartLineCap="Round"
                            StrokeThickness="1.1" />

                    </Canvas>

                    <Canvas>

                        <!-- drawing/LINE/<Path> -->
                        <Path
                            Data="F1 M 31.689,84.713 L 38.849,84.713"
                            Fill="#ff0f2239"
                            Stroke="#ff7ac1e9"
                            StrokeEndLineCap="Round"
                            StrokeLineJoin="Round"
                            StrokeStartLineCap="Round"
                            StrokeThickness="1.1" />

                    </Canvas>

                </Canvas>
            </Canvas>
        </Viewbox>
    </ControlTemplate>
    <!--#endregion-->


    <!--#region Pages Logo-->
    <ControlTemplate x:Key="PageLogoTemplate" TargetType="ContentControl">
        <Viewbox
            xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
            xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
            Width="48.837"
            Height="50.382">
            <Canvas Width="48.837" Height="50.382">
                <Canvas>

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 11.458,0.008 C 13.460,0.009 15.462,0.025 17.463,0.000 C 18.128,-0.008 18.493,0.302 18.647,0.895 C 19.195,2.998 19.726,5.106 20.269,7.210 C 20.343,7.496 20.306,7.624 19.962,7.617 C 18.972,7.597 17.982,7.601 16.991,7.614 C 16.717,7.618 16.603,7.532 16.633,7.255 C 16.646,7.130 16.634,7.002 16.625,6.876 C 16.546,5.819 16.546,5.819 17.591,5.813 C 17.801,5.812 18.012,5.816 18.223,5.806 C 18.562,5.791 18.787,5.647 18.786,5.272 C 18.786,4.896 18.564,4.750 18.221,4.747 C 17.736,4.742 17.250,4.725 16.767,4.752 C 16.458,4.770 16.390,4.663 16.371,4.367 C 16.319,3.591 16.200,2.820 16.142,2.045 C 16.106,1.572 15.898,1.386 15.423,1.388 C 12.769,1.400 10.114,1.402 7.459,1.386 C 6.983,1.383 6.748,1.567 6.708,2.024 C 6.641,2.799 6.554,3.573 6.522,4.349 C 6.507,4.696 6.425,4.843 6.056,4.818 C 5.637,4.789 5.214,4.816 4.792,4.810 C 4.470,4.805 4.324,4.996 4.314,5.277 C 4.305,5.579 4.434,5.818 4.783,5.829 C 5.141,5.840 5.500,5.832 5.858,5.833 C 5.984,5.833 6.150,5.784 6.230,5.846 C 6.586,6.122 6.386,6.517 6.309,6.812 C 6.238,7.086 6.565,7.621 5.939,7.634 C 4.949,7.654 3.959,7.642 2.969,7.659 C 2.439,7.668 2.721,7.281 2.752,7.135 C 3.076,5.623 3.435,4.118 3.786,2.611 C 3.909,2.081 4.022,1.549 4.164,1.024 C 4.377,0.236 4.692,0.009 5.516,0.008 C 7.497,0.007 9.478,0.008 11.458,0.008 Z" Fill="#ff6f788c" />

                    <!-- drawing/<Compound Path> -->
                    <Path Data="F1 M 15.770,26.227 C 15.756,25.727 15.468,25.478 15.059,25.478 C 14.618,25.478 14.319,25.735 14.337,26.232 C 14.353,26.681 14.606,26.945 15.037,26.942 C 15.493,26.939 15.751,26.661 15.770,26.227 Z M 15.772,28.430 C 15.761,27.868 15.462,27.631 15.058,27.630 C 14.618,27.630 14.311,27.895 14.328,28.382 C 14.343,28.823 14.613,29.089 15.039,29.088 C 15.499,29.088 15.746,28.798 15.772,28.430 Z M 10.085,28.724 C 10.757,28.724 11.430,28.713 12.102,28.729 C 12.445,28.737 12.603,28.623 12.597,28.261 C 12.582,27.463 12.580,26.664 12.596,25.866 C 12.603,25.516 12.456,25.357 12.121,25.357 C 10.755,25.354 9.389,25.352 8.024,25.356 C 7.707,25.357 7.514,25.486 7.520,25.848 C 7.532,26.646 7.534,27.445 7.519,28.243 C 7.512,28.646 7.712,28.738 8.068,28.729 C 8.740,28.712 9.413,28.724 10.085,28.724 Z M 11.576,24.169 C 13.219,24.169 14.861,24.182 16.503,24.160 C 16.959,24.154 17.131,24.269 17.122,24.760 C 17.093,26.444 17.096,28.129 17.121,29.814 C 17.130,30.393 16.966,30.637 16.337,30.632 C 13.115,30.605 9.894,30.608 6.672,30.625 C 6.187,30.627 5.996,30.495 6.003,29.988 C 6.025,28.219 6.024,26.450 6.003,24.681 C 5.998,24.271 6.110,24.155 6.523,24.161 C 8.207,24.185 9.892,24.171 11.576,24.171 C 11.576,24.170 11.576,24.170 11.576,24.169 Z" Fill="#ff6f788c" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 18.522,18.984 C 18.522,22.145 18.525,25.305 18.514,28.466 C 18.514,28.646 18.693,28.979 18.275,28.970 C 17.858,28.961 18.059,28.630 18.057,28.456 C 18.042,27.023 18.058,25.590 18.053,24.157 C 18.050,23.390 17.936,23.287 17.168,23.282 C 16.251,23.275 16.251,23.275 16.251,22.362 C 16.253,18.064 16.264,13.765 16.243,9.467 C 16.241,8.960 16.383,8.804 16.868,8.862 C 17.446,8.930 18.223,8.613 18.554,8.994 C 18.839,9.321 18.642,10.076 18.643,10.641 C 18.646,13.422 18.645,16.203 18.645,18.984 C 18.604,18.984 18.563,18.984 18.522,18.984 Z" Fill="#ff6f788c" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 4.496,18.926 C 4.496,15.765 4.507,12.605 4.484,9.444 C 4.480,8.966 4.607,8.804 5.079,8.862 C 5.615,8.928 6.333,8.636 6.645,8.990 C 6.910,9.291 6.734,9.989 6.735,10.511 C 6.740,14.578 6.731,18.645 6.753,22.712 C 6.756,23.185 6.639,23.370 6.164,23.285 C 6.021,23.259 5.868,23.293 5.722,23.279 C 5.202,23.229 5.007,23.478 5.012,23.979 C 5.024,25.475 5.024,26.971 5.014,28.467 C 5.013,28.657 5.172,28.969 4.760,28.973 C 4.334,28.978 4.508,28.651 4.508,28.471 C 4.496,25.289 4.499,22.108 4.499,18.926 C 4.498,18.926 4.497,18.926 4.496,18.926 Z" Fill="#ff6f788c" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 3.759,18.948 C 3.759,22.043 3.764,25.137 3.749,28.232 C 3.748,28.471 3.934,28.886 3.611,28.936 C 3.185,29.003 2.690,29.128 2.294,28.743 C 1.990,28.447 1.962,28.089 1.963,27.701 C 1.966,23.068 1.963,18.436 1.962,13.804 C 1.961,12.562 1.958,11.320 1.963,10.078 C 1.967,9.203 2.316,8.891 3.187,8.857 C 3.634,8.839 3.742,8.971 3.739,9.410 C 3.719,12.590 3.728,15.769 3.728,18.948 C 3.739,18.948 3.749,18.948 3.759,18.948 Z" Fill="#ff6f788c" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 7.782,12.448 C 7.782,10.847 7.773,9.246 7.785,7.645 C 7.792,6.698 8.421,6.091 9.375,6.088 C 10.828,6.083 12.282,6.084 13.735,6.088 C 14.662,6.090 15.225,6.604 15.261,7.557 C 15.319,9.105 15.330,10.654 15.214,12.298 C 14.393,10.692 13.074,9.921 11.426,9.902 C 9.681,9.881 8.583,10.973 7.782,12.448 Z" Fill="#ff6f788c" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 15.280,15.756 C 15.287,16.936 15.309,18.116 15.296,19.296 C 15.280,20.706 14.742,21.220 13.327,21.223 C 12.063,21.226 10.799,21.231 9.535,21.223 C 8.366,21.216 7.786,20.639 7.781,19.486 C 7.776,18.118 7.780,16.751 7.780,15.385 C 8.048,15.340 8.045,15.580 8.099,15.702 C 9.115,17.967 11.729,18.553 13.532,17.399 C 14.139,17.010 14.719,16.483 15.067,15.788 C 15.095,15.732 15.157,15.370 15.280,15.758 C 15.280,15.758 15.280,15.756 15.280,15.756 Z" Fill="#ff6f788c" />

                    <!-- drawing/<Compound Path> -->
                    <Path Data="F1 M 11.545,15.963 C 12.733,15.974 13.639,15.098 13.637,13.940 C 13.635,12.781 12.777,11.900 11.641,11.892 C 10.404,11.883 9.526,12.734 9.524,13.945 C 9.522,15.096 10.379,15.952 11.545,15.963 Z M 11.389,17.038 C 10.187,17.003 8.436,15.936 8.492,13.967 C 8.546,12.069 9.487,10.946 11.530,10.836 C 12.870,10.764 14.668,11.892 14.671,13.846 C 14.673,15.501 13.414,17.096 11.389,17.038 Z" Fill="#ff6fc7f1" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 19.488,22.228 C 19.809,23.658 20.433,24.971 21.013,26.300 C 21.313,26.988 21.176,27.707 21.154,28.411 C 21.145,28.701 20.889,28.918 20.556,28.969 C 19.400,29.149 19.291,29.057 19.291,27.901 C 19.293,26.015 19.292,24.128 19.292,22.242 C 19.357,22.238 19.423,22.233 19.488,22.228 Z" Fill="#ff6f788c" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 0.050,15.118 C 0.050,14.383 0.050,13.649 0.049,12.914 C 0.049,12.554 0.178,12.290 0.578,12.288 C 0.982,12.285 1.124,12.572 1.127,12.914 C 1.137,14.340 1.133,15.767 1.123,17.194 C 1.120,17.603 0.916,17.964 0.498,17.954 C 0.092,17.943 0.037,17.552 0.040,17.195 C 0.046,16.502 0.042,15.810 0.042,15.118 C 0.044,15.118 0.047,15.118 0.050,15.118 Z" Fill="#ff6f788c" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 19.481,13.855 C 19.558,12.321 19.540,10.785 19.547,9.250 C 19.549,8.891 20.153,8.702 20.612,8.909 C 21.054,9.107 21.302,9.671 21.095,10.124 C 20.533,11.348 19.949,12.563 19.481,13.855 Z" Fill="#ff6f788c" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 15.280,15.756 C 15.280,15.756 15.280,15.758 15.280,15.758 C 15.280,15.758 15.280,15.756 15.280,15.756 Z" Fill="#ff7ac1e9" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 10.085,28.724 C 9.413,28.724 8.740,28.712 8.068,28.729 C 7.712,28.738 7.512,28.646 7.519,28.243 C 7.534,27.445 7.532,26.646 7.520,25.848 C 7.514,25.486 7.707,25.357 8.024,25.356 C 9.389,25.352 10.755,25.354 12.121,25.357 C 12.456,25.357 12.603,25.516 12.596,25.866 C 12.580,26.664 12.582,27.463 12.597,28.261 C 12.603,28.623 12.445,28.737 12.102,28.729 C 11.430,28.713 10.757,28.724 10.085,28.724 Z" Fill="#ffebebeb" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 15.772,28.430 C 15.746,28.798 15.499,29.088 15.039,29.088 C 14.613,29.089 14.343,28.823 14.328,28.382 C 14.311,27.895 14.618,27.630 15.058,27.630 C 15.462,27.631 15.761,27.868 15.772,28.430 Z" Fill="#ffebebeb" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 15.770,26.227 C 15.751,26.661 15.493,26.939 15.037,26.942 C 14.606,26.945 14.353,26.681 14.337,26.232 C 14.319,25.735 14.618,25.478 15.059,25.478 C 15.468,25.478 15.756,25.727 15.770,26.227 Z" Fill="#ffebebeb" />

                    <!-- drawing/<Path> -->
                    <Path Data="F1 M 11.545,15.963 C 10.379,15.952 9.522,15.096 9.524,13.945 C 9.526,12.734 10.404,11.883 11.641,11.892 C 12.777,11.900 13.635,12.781 13.637,13.940 C 13.639,15.098 12.733,15.974 11.545,15.963 Z" Fill="#ffebebeb" />

                    <Canvas>

                        <!-- drawing/<Group>/<Path> -->
                        <Path Data="F1 M 40.417,23.745 C 40.411,24.179 39.883,24.702 39.451,24.702 C 39.270,24.702 39.150,24.610 39.046,24.471 C 38.152,23.278 37.254,22.087 36.359,20.895 C 36.298,20.814 36.217,20.744 36.324,20.627 C 36.444,20.498 36.525,20.576 36.619,20.647 C 37.810,21.544 39.001,22.441 40.194,23.336 C 40.334,23.441 40.423,23.564 40.417,23.745 Z" Fill="#ff6f788c" />

                        <Canvas>

                            <!-- drawing/<Group>/<Group>/<Path> -->
                            <Path Data="F1 M 33.033,16.869 C 32.907,16.622 33.131,16.577 33.261,16.499 C 34.240,15.918 35.580,16.146 36.393,17.026 C 37.191,17.890 37.341,19.285 36.652,20.187 C 35.799,21.305 34.656,21.751 33.250,21.037 C 32.070,20.438 31.594,19.006 32.124,17.751 C 32.205,17.560 32.213,17.224 32.586,17.316 C 33.158,17.444 33.158,17.444 33.033,16.869 Z" Fill="#ff6f788c" />

                            <!-- drawing/<Group>/<Group>/<Path> -->
                            <Path Data="F1 M 29.285,12.815 C 29.543,12.735 29.724,12.831 29.880,13.043 C 30.826,14.324 31.777,15.600 32.727,16.878 C 32.755,16.916 32.787,16.950 32.822,16.981 C 32.891,17.040 32.955,17.103 32.847,17.197 C 32.762,17.270 32.707,17.209 32.654,17.146 C 32.623,17.108 32.585,17.075 32.546,17.045 C 31.283,16.089 30.021,15.131 28.755,14.179 C 28.545,14.021 28.449,13.839 28.528,13.579 C 28.683,13.572 28.655,13.739 28.739,13.799 C 28.989,13.996 29.202,13.874 29.383,13.698 C 29.572,13.512 29.704,13.292 29.502,13.025 C 29.441,12.943 29.277,12.968 29.285,12.815 Z" Fill="#ff6f788c" />

                            <!-- drawing/<Group>/<Group>/<Path> -->
                            <Path Data="F1 M 29.316,12.831 C 29.385,12.924 29.535,12.835 29.592,12.954 C 29.551,13.019 29.484,13.039 29.415,13.052 C 29.052,13.115 28.841,13.327 28.777,13.690 C 28.765,13.758 28.745,13.826 28.680,13.867 C 28.561,13.810 28.649,13.660 28.556,13.591 C 28.660,13.188 28.914,12.935 29.316,12.831 Z" Fill="#ff6f788c" />

                        </Canvas>

                    </Canvas>

                    <Canvas>

                        <Canvas>

                            <!-- drawing/LWPOLYLINE/<Group>/<Path> -->
                            <Path Data="F1 M 28.426,33.799 L 29.152,34.616 C 29.277,34.713 29.459,34.698 29.566,34.577 C 29.649,34.484 29.666,34.348 29.611,34.238 L 27.031,31.285 C 26.925,31.170 26.737,31.160 26.610,31.271 L 25.233,32.468 C 25.795,33.583 26.451,34.880 27.141,36.242 C 27.320,35.536 27.452,34.817 27.523,34.092 C 27.543,33.887 27.683,33.714 27.879,33.650 C 28.075,33.587 28.290,33.645 28.426,33.799 Z" Fill="#ff7ac1e9" />

                            <!-- drawing/LWPOLYLINE/<Group>/<Path> -->
                            <Path Data="F1 M 34.712,37.585 C 32.893,37.650 31.151,38.473 29.929,39.926 C 29.733,40.157 29.551,40.399 29.380,40.649 C 29.635,41.150 29.887,41.643 30.132,42.122 C 30.568,41.142 31.160,40.229 31.898,39.430 C 32.701,38.597 33.660,37.973 34.712,37.585 Z" Fill="#ff7ac1e9" />

                            <!-- drawing/LWPOLYLINE/<Group>/<Compound Path> -->
                            <Path Data="F1 M 34.306,28.618 C 29.042,28.637 24.323,23.887 24.489,18.505 C 24.667,12.717 29.453,8.580 34.638,8.740 C 40.134,8.908 44.440,13.389 44.311,18.978 C 44.192,24.136 40.015,28.815 34.306,28.618 Z M 48.416,14.915 C 46.862,8.555 41.009,4.143 34.333,4.157 C 30.596,4.154 27.314,5.379 24.623,7.975 C 20.991,11.477 19.585,15.770 20.373,20.764 C 20.835,23.692 22.238,26.269 23.448,28.917 C 23.471,28.968 23.979,29.980 24.751,31.513 L 25.929,30.490 C 26.481,30.007 27.305,30.052 27.802,30.593 L 30.425,33.594 C 30.443,33.614 30.459,33.636 30.473,33.659 C 30.790,34.162 30.735,34.823 30.339,35.267 C 29.846,35.819 28.996,35.868 28.444,35.375 C 28.429,35.362 28.415,35.347 28.401,35.332 L 28.400,35.331 C 28.266,36.099 28.074,36.857 27.827,37.596 C 28.168,38.267 28.511,38.944 28.852,39.613 C 28.945,39.493 29.039,39.373 29.137,39.258 C 31.138,36.879 34.344,35.959 37.303,36.918 C 37.326,36.925 37.348,36.934 37.370,36.945 C 37.537,37.026 37.661,37.175 37.709,37.355 C 37.754,37.519 37.732,37.693 37.646,37.842 C 37.561,37.991 37.423,38.098 37.257,38.143 C 37.217,38.154 37.177,38.160 37.136,38.161 C 35.429,38.209 33.837,38.912 32.651,40.141 C 31.795,41.068 31.156,42.168 30.762,43.352 C 31.149,44.105 31.509,44.803 31.828,45.418 C 31.906,45.457 31.980,45.502 32.050,45.557 C 32.330,45.777 32.506,46.092 32.548,46.446 C 32.562,46.558 32.556,46.670 32.541,46.779 C 32.904,47.464 33.149,47.910 33.228,48.018 C 33.488,48.376 33.652,48.807 34.178,48.839 C 34.728,48.872 34.935,48.464 35.145,48.070 C 37.413,43.829 39.693,39.593 41.943,35.342 C 43.810,31.815 45.749,28.325 47.407,24.690 C 48.844,21.539 49.232,18.257 48.416,14.915 Z" Fill="#ff7ac1e9" />

                        </Canvas>

                        <!-- drawing/LWPOLYLINE/<Path> -->
                        <Path
                            Data="F1 M 30.035,33.935 L 27.421,30.944 C 27.117,30.614 26.606,30.586 26.269,30.881 L 23.291,33.469 C 22.989,33.802 23.015,34.316 23.348,34.617 C 23.633,34.875 24.060,34.898 24.371,34.673 L 25.322,33.861 C 25.338,36.465 24.273,38.959 22.382,40.748 C 21.019,41.982 19.226,42.630 17.389,42.553 C 17.326,42.547 17.270,42.595 17.264,42.658 C 17.259,42.713 17.293,42.763 17.346,42.779 C 20.029,43.859 23.100,43.204 25.109,41.122 C 26.116,40.087 26.874,38.836 27.325,37.464 C 27.688,36.388 27.928,35.273 28.039,34.143 L 28.789,34.988 C 29.128,35.291 29.649,35.262 29.952,34.922 C 30.197,34.648 30.230,34.246 30.035,33.935"
                            Fill="#ff6f788c"
                            Stroke="#ff6f788c"
                            StrokeEndLineCap="Round"
                            StrokeLineJoin="Round"
                            StrokeStartLineCap="Round"
                            StrokeThickness="0.5" />

                    </Canvas>

                    <!-- drawing/<Path> -->
                    <Path
                        Data="F1 M 25.105,47.098 L 27.916,49.905 C 28.241,50.213 28.752,50.206 29.069,49.889 L 31.865,47.106 C 32.143,46.753 32.082,46.242 31.730,45.964 C 31.428,45.726 31.001,45.732 30.706,45.978 L 29.812,46.852 C 29.619,44.255 30.512,41.695 32.279,39.781 C 33.551,38.462 35.289,37.694 37.121,37.643 C 37.188,37.625 37.227,37.556 37.209,37.490 C 37.200,37.455 37.176,37.426 37.143,37.411 C 34.399,36.522 31.389,37.385 29.532,39.593 C 28.598,40.694 27.927,41.994 27.569,43.393 C 27.280,44.492 27.116,45.619 27.082,46.755 L 26.276,45.962 C 25.917,45.683 25.400,45.748 25.120,46.107 C 24.895,46.397 24.889,46.801 25.105,47.098"
                        Fill="#ff6f788c"
                        Stroke="#ff6f788c"
                        StrokeEndLineCap="Round"
                        StrokeLineJoin="Round"
                        StrokeStartLineCap="Round"
                        StrokeThickness="0.5" />

                    <Canvas>

                        <!-- drawing/LWPOLYLINE/<Path> -->
                        <Path
                            Data="F1 M 16.177,32.981 L 6.864,32.981 C 6.637,32.981 6.437,32.831 6.375,32.612 L 6.096,31.629 C 6.083,31.583 6.076,31.537 6.076,31.490 C 6.076,31.209 6.304,30.981 6.585,30.981 L 16.455,30.981 C 16.502,30.981 16.549,30.988 16.594,31.001 C 16.864,31.077 17.021,31.358 16.944,31.628 L 16.666,32.611 C 16.604,32.830 16.404,32.981 16.177,32.981 Z"
                            Fill="#ff6f788c"
                            Stroke="#ffffffff"
                            StrokeEndLineCap="Round"
                            StrokeLineJoin="Round"
                            StrokeStartLineCap="Round"
                            StrokeThickness="0.5" />

                    </Canvas>

                    <Canvas>

                        <!-- drawing/LWPOLYLINE/<Path> -->
                        <Path
                            Data="F1 M 6.046,34.045 L 0.266,48.979 C 0.219,49.100 0.279,49.236 0.400,49.282 C 0.427,49.293 0.456,49.298 0.485,49.298 L 1.654,49.298 L 8.381,33.849 L 6.332,33.849 C 6.205,33.849 6.092,33.927 6.046,34.045 Z"
                            Fill="#ff6f788c"
                            Stroke="#ff6f788c"
                            StrokeEndLineCap="Round"
                            StrokeLineJoin="Round"
                            StrokeStartLineCap="Round"
                            StrokeThickness="0.5" />

                    </Canvas>

                    <Canvas>

                        <!-- drawing/LINE/<Path> -->
                        <Path
                            Data="F1 M 5.354,35.833 L 7.173,36.624"
                            Stroke="#ff7bc1ea"
                            StrokeEndLineCap="Round"
                            StrokeLineJoin="Round"
                            StrokeStartLineCap="Round"
                            StrokeThickness="0.5" />

                    </Canvas>

                    <Canvas>

                        <!-- drawing/LINE/<Path> -->
                        <Path
                            Data="F1 M 5.039,36.648 L 6.824,37.425"
                            Stroke="#ff7bc1ea"
                            StrokeEndLineCap="Round"
                            StrokeLineJoin="Round"
                            StrokeStartLineCap="Round"
                            StrokeThickness="0.5" />

                    </Canvas>

                    <Canvas>

                        <!-- drawing/LWPOLYLINE/<Path> -->
                        <Path
                            Data="F1 M 18.166,41.902 L 14.660,33.849 L 16.709,33.849 C 16.836,33.849 16.949,33.927 16.995,34.045 L 19.925,41.616 C 19.353,41.785 18.762,41.881 18.166,41.902 Z"
                            Fill="#ff6f788c"
                            Stroke="#ff6f788c"
                            StrokeEndLineCap="Round"
                            StrokeLineJoin="Round"
                            StrokeStartLineCap="Round"
                            StrokeThickness="0.5" />

                    </Canvas>

                    <Canvas>

                        <!-- drawing/LWPOLYLINE/<Path> -->
                        <Path
                            Data="F1 M 20.948,44.259 L 22.775,48.979 C 22.785,49.006 22.791,49.035 22.791,49.064 C 22.791,49.193 22.686,49.298 22.556,49.298 L 21.387,49.298 L 19.149,44.158 C 19.743,44.256 20.346,44.290 20.948,44.259 Z"
                            Fill="#ff6f788c"
                            Stroke="#ff6f788c"
                            StrokeEndLineCap="Round"
                            StrokeLineJoin="Round"
                            StrokeStartLineCap="Round"
                            StrokeThickness="0.5" />

                    </Canvas>

                    <Canvas>

                        <!-- drawing/LINE/<Path> -->
                        <Path
                            Data="F1 M 17.686,35.833 L 15.868,36.624"
                            Stroke="#ff7bc1ea"
                            StrokeEndLineCap="Round"
                            StrokeLineJoin="Round"
                            StrokeStartLineCap="Round"
                            StrokeThickness="0.5" />

                    </Canvas>

                    <Canvas>

                        <!-- drawing/LINE/<Path> -->
                        <Path
                            Data="F1 M 18.002,36.648 L 16.217,37.425"
                            Stroke="#ff7bc1ea"
                            StrokeEndLineCap="Round"
                            StrokeLineJoin="Round"
                            StrokeStartLineCap="Round"
                            StrokeThickness="0.5" />

                    </Canvas>

                    <Canvas>

                        <!-- drawing/LWPOLYLINE/<Path> -->
                        <Path
                            Data="F1 M 13.323,33.849 L 9.563,33.849 L 9.924,49.298 L 12.961,49.298 L 13.323,33.849"
                            Fill="#ff6f788c"
                            Stroke="#ff6f788c"
                            StrokeEndLineCap="Round"
                            StrokeLineJoin="Round"
                            StrokeStartLineCap="Round"
                            StrokeThickness="0.5" />

                    </Canvas>

                    <Canvas>

                        <!-- drawing/LINE/<Path> -->
                        <Path
                            Data="F1 M 13.258,36.624 L 9.627,36.624"
                            Stroke="#ff7bc1ea"
                            StrokeEndLineCap="Round"
                            StrokeLineJoin="Round"
                            StrokeStartLineCap="Round"
                            StrokeThickness="0.5" />

                    </Canvas>

                    <Canvas>

                        <!-- drawing/LINE/<Path> -->
                        <Path
                            Data="F1 M 9.646,37.425 L 13.239,37.425"
                            Fill="#ff0f2239"
                            Stroke="#ff7bc1ea"
                            StrokeEndLineCap="Round"
                            StrokeLineJoin="Round"
                            StrokeStartLineCap="Round"
                            StrokeThickness="0.5" />

                    </Canvas>

                </Canvas>
            </Canvas>
        </Viewbox>
    </ControlTemplate>
    <!--#endregion-->


    <!--#region Home Background-->
    <Viewbox
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        x:Key="HomeBackground"
        Width="400"
        Height="600"
        Margin="0"
        Stretch="None">
        <Canvas Width="400" Height="600">
            <Path Data="M400 0H0v600h400z" Fill="#fff" />
            <Path Data="M395.017 132.034a5.017 5.017 0 1 0 0-10.034 5.017 5.017 0 0 0 0 10.034" Fill="#C7E0EF" />
            <Path Data="M395.017 207.034a5.017 5.017 0 1 0 0-10.034 5.017 5.017 0 0 0 0 10.034m0 48a5.017 5.017 0 1 0 0-10.034 5.017 5.017 0 0 0 0 10.034" Fill="#9AD0EF" />
            <Ellipse
                Canvas.Left="326"
                Canvas.Top="10"
                Width="50.171"
                Height="50.171"
                Fill="{DynamicResource radialGradientB}" />
            <Ellipse
                Canvas.Left="242"
                Canvas.Top="539"
                Width="50.171"
                Height="50.171"
                Fill="{DynamicResource radialGradientC}" />
            <Path Data="M401.581.27H.209v602.057h401.372z" Fill="#ECECEC" />

            <Path
                Data="m0 425 39-1M0 486l27-1M0 568l38 8M0 529l27-44M0 567l105-7M4 600l34-24m-25 24 92-40M38.922 423.687 26.84 484.956m12.081-61.269 71.194 4.535m-71.194-4.535 67.21 53.994m-67.21-53.994 69.829-71.184M26.84 484.956l79.291-7.275m-79.291 7.275 10.857 91.473m.001 0 67.229-16.918M38 576l-7 24m77.75-247.497 61.48 5.108m-61.48-5.108 1.365 75.719m0 0-3.984 49.459m0 0-1.204 81.83m0 0 75.348 10.305M105 560l13 40m-13.073-40.489L26.84 484.956M105 560l70 40m11.646-347.097 61.159 29.26m-77.575 75.448 24.373 53.071m-24.373-53.071-60.115 70.611m60.115-70.611 94.011-27.133m-69.637 80.204 4.756 78.88m-4.756-78.88-84.489 17.54m84.489-17.54 62.644 65.785m-62.645-65.785-85.853-58.179m85.854 58.179 69.638-80.204M199.36 489.562l57.887-13.095m-57.887 13.095-19.085 80.254m19.085-80.254-93.229-11.881m93.229 11.881-89.245-61.34m89.245 61.34 66.718 88.462M180 570l11 30m-10.725-30.184 85.803 8.208M223 600l43-22m-70 22 3-110M243 0l13 25m33-25 63 56m-95.836-30.755 11.79 96.299m0 0 9.613 55.038m-9.613-55.038 77.425-4.104m-77.425 4.104 79.923 60.527m-79.923-60.527 84.178-65.614m-84.178 65.614 133.466 6.964m-123.853 48.074 70.31 5.489m-70.31-5.489 67.812-59.142m-67.812 59.142 46.71 79.151m-76.471 26.43 16.436 48.315m-.001 0 85.011-10.004m-85.011 10.004 60.035-74.745m-67.028 220.734 81.88-5.218m-73.05 106.775 78.137-6.362M266 578l-11 22m-45 0-30-30M290 0l-34 25m96.132 30.93-6.753 61.51M352 56l48-18m-48 18 48 72m-55-11 54 11m-51 54h52m-52.123.071-2.498-64.631m-21.102 138.293 24.975 64.741m-24.975-64.741 23.601-73.662m-23.601 73.662-76.471 26.43M349 320l51 12m-50.748-11.526-22.677 82.893M349 320l51-54m-50.747 54.474-101.447-38.311m78.769 121.204 12.553 67.882m-12.554-67.882-62.333-72.889m62.334 72.889-69.327 73.1M327 403l73-2m-61 70 61 20m-56 81 3 28m-3-28 47 28M387 0l-35 56m49.42 72.508-53.543 53.563m59.102.191-61.6-64.822m55.71 373.717-56.874 80.505M400 5l-48 51m48 422-61-7"
                Stroke="#C7E0EF"
                StrokeThickness="1.5" />
            <Path Data="M38.921 428.704a5.017 5.017 0 1 0 0-10.034 5.017 5.017 0 0 0 0 10.034M26.84 489.973a5.017 5.017 0 1 0 0-10.034 5.017 5.017 0 0 0 0 10.034m10.858 91.473a5.017 5.017 0 1 0 0-10.034 5.017 5.017 0 0 0 0 10.034m71.052-223.925a5.018 5.018 0 1 0-.001-10.035 5.018 5.018 0 0 0 .001 10.035m1.365 75.718a5.017 5.017 0 1 0 0-10.034 5.017 5.017 0 0 0 0 10.034m-3.984 49.459a5.018 5.018 0 1 0 0-10.035 5.018 5.018 0 0 0 0 10.035m-1.204 81.83a5.017 5.017 0 1 0 0-10.034 5.017 5.017 0 0 0 0 10.034m81.719-306.608a5.017 5.017 0 1 0 0-10.034 5.017 5.017 0 0 0 0 10.034M170.23 362.628a5.017 5.017 0 1 0 0-10.034 5.017 5.017 0 0 0 0 10.034m24.374 53.071a5.017 5.017 0 1 0 0-10.035 5.017 5.017 0 0 0 0 10.035m4.756 78.88a5.017 5.017 0 1 0 0-10.034 5.017 5.017 0 0 0 0 10.034m-19.085 80.254a5.017 5.017 0 1 0 0-10.034 5.017 5.017 0 0 0 0 10.034m75.889-544.571a5.017 5.017 0 1 0 0-10.034 5.017 5.017 0 0 0 0 10.034m11.79 96.299a5.017 5.017 0 1 0 0-10.034 5.017 5.017 0 0 0 0 10.034m9.613 55.038a5.018 5.018 0 1 0 0-10.035 5.018 5.018 0 0 0 0 10.035M247.805 287.18a5.017 5.017 0 1 0 0-10.034 5.017 5.017 0 0 0 0 10.034m16.436 48.315a5.017 5.017 0 1 0 0-10.034 5.017 5.017 0 0 0 0 10.034m-6.994 145.989a5.018 5.018 0 1 0 0-10.035 5.018 5.018 0 0 0 0 10.035m8.831 101.557a5.017 5.017 0 1 0 0-10.034 5.017 5.017 0 0 0 0 10.034m86.054-522.094a5.018 5.018 0 1 0 0-10.036 5.018 5.018 0 0 0 0 10.036m-6.753 61.51a5.017 5.017 0 1 0 0-10.034 5.017 5.017 0 0 0 0 10.034m2.498 64.631a5.018 5.018 0 1 0 0-10.035 5.018 5.018 0 0 0 0 10.035m-23.6 73.662a5.017 5.017 0 1 0 0-10.035 5.018 5.018 0 0 0 0 10.035m24.975 64.741a5.017 5.017 0 1 0 0-10.034 5.017 5.017 0 0 0 0 10.034m-22.677 82.893a5.017 5.017 0 1 0 0-10.034 5.017 5.017 0 0 0 0 10.034m12.553 67.882a5.017 5.017 0 1 0 0-10.034 5.017 5.017 0 0 0 0 10.034m5.087 100.414a5.018 5.018 0 1 0-.001-10.035 5.018 5.018 0 0 0 .001 10.035M398 491c1.105 0 2-1.119 2-2.5s-.895-2.5-2-2.5-2 1.119-2 2.5.895 2.5 2 2.5" Fill="#C7E0EF" />
            <Path
                Data="m98.024 476.638-47.07.271m47.069-.271 75.619 8.599m-75.619-8.599 4.807 89.987m-4.807-89.987 98.858-43.83M102.83 566.625l-72.839.131M103 567l3 33m-3-33-31 33m276.419-274.509-4.545-46.83M348 325l52 28m-51.581-27.509 2.549 74.043M348 325l52-73m-51.581 73.491-78.799-55.199M356 471l44 11m-43.774-11.172-5.257-71.294M356 471l44-57m-43.774 56.828-78.599-53.975M387 600l13-13m-86 13-48-37M387 0l-36 37m49 74-76 21m76-27-49-68m49-30-49 30M0 556l30 11M0 531l51-54M0 566l38 34m-38-3 30-30M3 600l100-33M0 564l51-87m-.047-.091L29.99 566.756m20.963-89.847 51.877 89.716m-51.877-89.716 122.689 8.328m-122.689-8.328 145.929-44.101M30 567l14 33m111 0 37-25m4.881-142.192-23.239 52.429m23.239-52.429 57.186 51.255m-57.186-51.255 80.746-15.955m-103.985 68.384 80.425-1.174m-80.425 1.174 18.322 89.616M192 575l-2 24m1.965-24.147 74.394-11.73m-74.394 11.73-89.135-8.228m166.79-296.333-22.898 63.839m22.898-63.839 74.254 8.369m-74.254-8.369 86.425-73.712M270 270l130-19m-153.278 83.131 30.905 82.722m-30.905-82.722 101.697-8.64m-101.697 8.64-49.841 98.677m80.746-15.955-23.56 67.21m23.56-67.21 73.341-17.319m-96.901 84.529 12.292 79.06M266 563l6 37m-6-37-36 37m36.359-36.877-92.717-77.886M351 1v36m1-35 48 103M350 3l-26 129m-.275-.271 32.32 64.851m-32.32-64.851 26.781-94.694m-26.781 94.694 79.913 74.334M356 197l44 8m-44-7 44 53m-56.126 27.661 57.235-27.574m-57.235 27.574 12.171-82.081M351 400l49 6m-49-6 49-44"
                Stroke="#7BC2EA"
                StrokeThickness="1.5" />

            <Ellipse
                Canvas.Left="26"
                Canvas.Top="452"
                Width="50.171"
                Height="50.171"
                Fill="{DynamicResource radialGradientD}" />
            <Ellipse
                Canvas.Left="77.745"
                Canvas.Top="541.625"
                Width="50.171"
                Height="50.171"
                Fill="{DynamicResource radialGradientE}" />
            <Ellipse
                Canvas.Left="323.334"
                Canvas.Top="300.405"
                Width="50.171"
                Height="50.171"
                Fill="{DynamicResource radialGradientF}" />
            <Ellipse
                Canvas.Left="331.141"
                Canvas.Top="445.742"
                Width="50.171"
                Height="50.171"
                Fill="{DynamicResource radialGradientG}" />

            <Path Data="M50.953 481.926a5.018 5.018 0 1 0-.001-10.035 5.018 5.018 0 0 0 0 10.035m-20.961 89.847a5.017 5.017 0 1 0 0-10.034 5.017 5.017 0 0 0 0 10.034m166.89-133.948a5.018 5.018 0 1 0 0-10.035 5.018 5.018 0 0 0 0 10.035m-23.239 52.429a5.017 5.017 0 1 0 0-10.034 5.017 5.017 0 0 0 0 10.034m18.323 89.616a5.017 5.017 0 1 0 0-10.034 5.017 5.017 0 0 0 0 10.034m77.655-304.56a5.018 5.018 0 1 0-.001-10.035 5.018 5.018 0 0 0 .001 10.035m-22.898 63.838a5.017 5.017 0 1 0 0-10.035 5.017 5.017 0 0 0 0 10.035m30.905 82.722a5.017 5.017 0 1 0 0-10.034 5.017 5.017 0 0 0 0 10.034m-23.56 67.21a5.017 5.017 0 1 0 0-10.034 5.017 5.017 0 0 0 0 10.034m12.292 79.06a5.017 5.017 0 1 0 0-10.034 5.017 5.017 0 0 0 0 10.034m84.147-526.088a5.018 5.018 0 1 0 0-10.035 5.018 5.018 0 0 0 0 10.035m-26.781 94.694a5.017 5.017 0 1 0 0-10.034 5.017 5.017 0 0 0 0 10.034m32.32 64.852a5.018 5.018 0 0 0 0-10.035 5.018 5.018 0 0 0 0 10.035m-12.171 82.08a5.017 5.017 0 1 0 0-10.034 5.017 5.017 0 0 0 0 10.034m7.094 120.873a5.017 5.017 0 1 0 0-10.034 5.017 5.017 0 0 0 0 10.034" Fill="#9AD0EF" />

            <Canvas.Resources>
                <RadialGradientBrush x:Key="radialGradientB" Center="0.5,0.5" GradientOrigin="0.5,0.5" RadiusX="1" RadiusY="1">
                    <GradientStop Offset="0.1" Color="White" />
                    <GradientStop Offset="0.2" Color="#34A1DF" />
                    <GradientStop Offset="1" Color="Transparent" />
                </RadialGradientBrush>
                <RadialGradientBrush x:Key="radialGradientC" Center="0.5,0.5" GradientOrigin="0.5,0.5" RadiusX="1" RadiusY="1">
                    <GradientStop Offset="0.1" Color="White" />
                    <GradientStop Offset="0.2" Color="#34A1DF" />
                    <GradientStop Offset="1" Color="Transparent" />
                </RadialGradientBrush>
                <RadialGradientBrush x:Key="radialGradientD" Center="0.5,0.5" GradientOrigin="0.5,0.5" RadiusX="1" RadiusY="1">
                    <GradientStop Offset="0.1" Color="White" />
                    <GradientStop Offset="0.2" Color="#34A1DF" />
                    <GradientStop Offset="1" Color="Transparent" />
                </RadialGradientBrush>
                <RadialGradientBrush x:Key="radialGradientE" Center="0.5,0.5" GradientOrigin="0.5,0.5" RadiusX="1" RadiusY="1">
                    <GradientStop Offset="0.1" Color="White" />
                    <GradientStop Offset="0.2" Color="#34A1DF" />
                    <GradientStop Offset="1" Color="Transparent" />
                </RadialGradientBrush>
                <RadialGradientBrush x:Key="radialGradientF" Center="0.5,0.5" GradientOrigin="0.5,0.5" RadiusX="1" RadiusY="1">
                    <GradientStop Offset="0.1" Color="White" />
                    <GradientStop Offset="0.2" Color="#34A1DF" />
                    <GradientStop Offset="1" Color="Transparent" />
                </RadialGradientBrush>
                <RadialGradientBrush x:Key="radialGradientG" Center="0.5,0.5" GradientOrigin="0.5,0.5" RadiusX="1" RadiusY="1">
                    <GradientStop Offset="0.1" Color="White" />
                    <GradientStop Offset="0.2" Color="#34A1DF" />
                    <GradientStop Offset="1" Color="Transparent" />
                </RadialGradientBrush>
            </Canvas.Resources>
        </Canvas>
    </Viewbox>
    <!--#endregion-->


    <!--#region Icon Button Style-->
    <Style x:Key="IconButtonStyle" TargetType="Button">
        <!--  Default Properties  -->
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Opacity" Value="0.8" />
        <Setter Property="Foreground" Value="#687A99" />

        <!--  Custom Control Template  -->
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>

        <!--  Triggers for Mouse-Over and Pressed States  -->
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Foreground" Value="#465A77" />
                <!--  Darker color  -->
                <Setter Property="Opacity" Value="1" />
                <Setter Property="RenderTransform">
                    <Setter.Value>
                        <ScaleTransform CenterX="12" CenterY="12" ScaleX="1.1" ScaleY="1.1" />
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Opacity" Value="0.7" />
                <Setter Property="RenderTransform">
                    <Setter.Value>
                        <ScaleTransform CenterX="12" CenterY="12" ScaleX="0.95" ScaleY="0.95" />
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>
    <!--#endregion-->


    <!--#region Icon Button Style-->
    <Style x:Key="OutlinedButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="#3ea5e1" />
        <Setter Property="Foreground" Value="#3ea5e1" />
        <Setter Property="BorderThickness" Value="1.5" />
        <Setter Property="Padding" Value="10" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="10">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <!--  MouseOver Trigger with Delay  -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Foreground" Value="White" />
                            <Setter Property="Background" Value="#3ea5e1" />
                            <Setter Property="BorderBrush" Value="#3ea5e1" />
                        </Trigger>

                        <!--  Click effect (pressed)  -->
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="#2b8bb1" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <!--#endregion-->


    <!--#region Import Button Style-->
    <ControlTemplate x:Key="ImportButtonIconTemplate">
        <Viewbox
            Width="20"
            Height="20"
            HorizontalAlignment="Center"
            VerticalAlignment="Top"
            Stretch="Uniform">
            <Canvas Width="20" Height="20">
                <Path Data="M12.9526 8.725C13.0098 8.66359 13.0788 8.61434 13.1555 8.58018C13.2321 8.54602 13.3149 8.52765 13.3988 8.52617C13.4827 8.52469 13.5661 8.54013 13.6439 8.57156C13.7217 8.603 13.7924 8.64979 13.8518 8.70913C13.9111 8.76848 13.9579 8.83918 13.9894 8.917C14.0208 8.99482 14.0362 9.07818 14.0347 9.1621C14.0333 9.24602 14.0149 9.32878 13.9807 9.40545C13.9466 9.48211 13.8973 9.55111 13.8359 9.60833L11.3359 12.1083C11.2187 12.2254 11.0599 12.2911 10.8943 12.2911C10.7286 12.2911 10.5698 12.2254 10.4526 12.1083L7.95259 9.60833C7.89118 9.55111 7.84193 9.48211 7.80777 9.40545C7.77361 9.32878 7.75524 9.24602 7.75376 9.1621C7.75228 9.07818 7.76772 8.99482 7.79915 8.917C7.83058 8.83918 7.87737 8.76848 7.93672 8.70913C7.99607 8.64979 8.06676 8.603 8.14459 8.57156C8.22241 8.54013 8.30577 8.52469 8.38969 8.52617C8.47361 8.52765 8.55637 8.54602 8.63303 8.58018C8.7097 8.61434 8.7787 8.66359 8.83592 8.725L10.2693 10.1583V3.33333C10.2693 3.16757 10.3351 3.0086 10.4523 2.89139C10.5695 2.77418 10.7285 2.70833 10.8943 2.70833C11.06 2.70833 11.219 2.77418 11.3362 2.89139C11.4534 3.0086 11.5193 3.16757 11.5193 3.33333V10.1583L12.9526 8.725Z" Fill="#FF687A99" />
                <Path Data="M18.1859 10C18.1859 9.83424 18.1201 9.67527 18.0029 9.55806C17.8857 9.44085 17.7267 9.375 17.5609 9.375C17.3952 9.375 17.2362 9.44085 17.119 9.55806C17.0018 9.67527 16.9359 9.83424 16.9359 10C16.9359 10.7934 16.7797 11.579 16.476 12.312C16.1724 13.0451 15.7274 13.7111 15.1664 14.2721C14.6054 14.8331 13.9393 15.2782 13.2063 15.5818C12.4733 15.8854 11.6877 16.0417 10.8943 16.0417C10.1009 16.0417 9.31523 15.8854 8.58222 15.5818C7.84921 15.2782 7.18318 14.8331 6.62216 14.2721C6.06114 13.7111 5.61612 13.0451 5.31249 12.312C5.00887 11.579 4.8526 10.7934 4.8526 10C4.8526 9.83424 4.78675 9.67527 4.66954 9.55806C4.55233 9.44085 4.39336 9.375 4.2276 9.375C4.06184 9.375 3.90287 9.44085 3.78566 9.55806C3.66845 9.67527 3.6026 9.83424 3.6026 10C3.6026 11.9339 4.37083 13.7885 5.73828 15.156C7.10573 16.5234 8.9604 17.2917 10.8943 17.2917C12.8281 17.2917 14.6828 16.5234 16.0503 15.156C17.4177 13.7885 18.1859 11.9339 18.1859 10Z" Fill="#FF687A99" />
            </Canvas>
        </Viewbox>
    </ControlTemplate>

    <Style x:Key="ImportButtonStyle" TargetType="Button">
        <Setter Property="Width" Value="50" />
        <Setter Property="Height" Value="45" />
        <Setter Property="Background" Value="#FFD9D9D9" />
        <Setter Property="Foreground" Value="#FF55657F" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="HorizontalAlignment" Value="Center" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="Margin" Value="0,0,7,0" />
        <Setter Property="BorderBrush" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border
                        x:Name="Border"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="5">
                        <StackPanel
                            Height="39.5"
                            HorizontalAlignment="Center"
                            Orientation="Vertical">
                            <!--  Reference the Viewbox template  -->
                            <ContentControl Template="{StaticResource ImportButtonIconTemplate}" />
                            <TextBlock
                                HorizontalAlignment="Center"
                                FontFamily="{StaticResource PoppinsFont}"
                                FontSize="11"
                                Text="Import" />
                        </StackPanel>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Border" Property="Background" Value="#FFB5B5B5" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="Border" Property="Background" Value="#FF9B9B9B" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <!--#endregion-->


    <!--#region Rounded ComboBox Style-->
    <Style x:Key="RoundedComboBox" TargetType="ComboBox">
        <Setter Property="Margin" Value="5,2" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="Padding" Value="2" />
        <Setter Property="Background" Value="White" />
        <Setter Property="BorderBrush" Value="Gray" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ComboBox">
                    <Grid>
                        <!--  Border with rounded corners for the ComboBox  -->
                        <Border
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4" />

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <!--  Content area  -->
                            <ContentPresenter
                                Name="ContentSite"
                                Grid.Column="0"
                                Margin="8,3,0,3"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Center"
                                Content="{TemplateBinding SelectionBoxItem}"
                                ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                IsHitTestVisible="False" />

                            <!--  Arrow button centered at right  -->
                            <Path
                                x:Name="Arrow"
                                Grid.Column="1"
                                Margin="0,0,8,0"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Data="M 0 0 L 4 4 L 8 0 Z"
                                Fill="{TemplateBinding Foreground}" />
                        </Grid>

                        <!--  Toggle button covers the entire control  -->
                        <ToggleButton
                            Name="ToggleButton"
                            Background="Transparent"
                            BorderBrush="Transparent"
                            ClickMode="Press"
                            Focusable="false"
                            IsChecked="{Binding Path=IsDropDownOpen, RelativeSource={RelativeSource TemplatedParent}, Mode=TwoWay}">
                            <ToggleButton.Template>
                                <ControlTemplate TargetType="ToggleButton">
                                    <Border Background="Transparent" />
                                </ControlTemplate>
                            </ToggleButton.Template>
                        </ToggleButton>

                        <Popup
                            Name="Popup"
                            AllowsTransparency="True"
                            Focusable="False"
                            IsOpen="{TemplateBinding IsDropDownOpen}"
                            Placement="Bottom"
                            PopupAnimation="Slide">
                            <Border
                                x:Name="DropDownBorder"
                                Background="White"
                                BorderBrush="Gray"
                                BorderThickness="1"
                                CornerRadius="4">
                                <ScrollViewer Margin="4" SnapsToDevicePixels="True">
                                    <StackPanel IsItemsHost="True" KeyboardNavigation.DirectionalNavigation="Contained" />
                                </ScrollViewer>
                            </Border>
                        </Popup>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="HasItems" Value="false">
                            <Setter TargetName="DropDownBorder" Property="MinHeight" Value="95" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground" Value="Gray" />
                        </Trigger>
                        <Trigger Property="IsDropDownOpen" Value="True">
                            <Setter TargetName="Arrow" Property="Data" Value="M 0 4 L 4 0 L 8 4 Z" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <!--#endregion-->


    <!--#region Display button Style-->
    <ControlTemplate x:Key="DisplayButtonIconTemplate">
        <Viewbox
            Width="22"
            Height="22"
            HorizontalAlignment="Center"
            VerticalAlignment="Top"
            Stretch="Uniform">
            <Canvas Width="22" Height="22">
                <Path Fill="#FF687A99">
                    <Path.Data>
                        <GeometryGroup FillRule="EvenOdd">
                            <PathGeometry Figures="M11.9633 0.19149C12.973 0.19149 13.947 0.320396 14.8851 0.578209C15.8233 0.836021 16.7006 1.20484 17.517 1.68465C18.3334 2.16447 19.0746 2.73739 19.7406 3.4034C20.4066 4.06942 20.9795 4.81421 21.4594 5.63778C21.9392 6.46135 22.308 7.33863 22.5658 8.26962C22.8236 9.2006 22.9561 10.1746 22.9633 11.1915C22.9633 12.2013 22.8344 13.1752 22.5765 14.1134C22.3187 15.0515 21.9499 15.9288 21.4701 16.7452C20.9903 17.5616 20.4174 18.3028 19.7513 18.9688C19.0853 19.6348 18.3405 20.2078 17.517 20.6876C16.6934 21.1674 15.8161 21.5362 14.8851 21.794C13.9541 22.0518 12.9802 22.1843 11.9633 22.1915C10.9535 22.1915 9.97953 22.0626 9.04138 21.8048C8.10323 21.547 7.22595 21.1781 6.40955 20.6983C5.59314 20.2185 4.85193 19.6456 4.18591 18.9796C3.5199 18.3136 2.94698 17.5688 2.46716 16.7452C1.98735 15.9216 1.61853 15.0479 1.36072 14.1241C1.10291 13.2003 0.970418 12.2227 0.963257 11.1915C0.963257 10.1817 1.09216 9.20777 1.34998 8.26962C1.60779 7.33146 1.9766 6.45419 2.45642 5.63778C2.93624 4.82137 3.50916 4.08016 4.17517 3.41415C4.84119 2.74813 5.58598 2.17521 6.40955 1.6954C7.23311 1.21558 8.10681 0.846764 9.03064 0.588951C9.95447 0.331139 10.932 0.198652 11.9633 0.19149ZM11.9633 20.8165C12.8441 20.8165 13.6927 20.7019 14.5092 20.4727C15.3256 20.2436 16.0918 19.9213 16.808 19.5059C17.5241 19.0906 18.1758 18.5857 18.7631 17.9913C19.3503 17.3969 19.8516 16.7488 20.267 16.047C20.6823 15.3451 21.0082 14.5789 21.2445 13.7481C21.4808 12.9174 21.5954 12.0652 21.5883 11.1915C21.5883 10.3106 21.4737 9.462 21.2445 8.64559C21.0153 7.82919 20.6931 7.06291 20.2777 6.34676C19.8623 5.63062 19.3575 4.97893 18.7631 4.39169C18.1687 3.80445 17.5205 3.30314 16.8187 2.88778C16.1169 2.47241 15.3506 2.14657 14.5199 1.91024C13.6892 1.67391 12.837 1.55933 11.9633 1.56649C11.0824 1.56649 10.2338 1.68107 9.41736 1.91024C8.60095 2.13941 7.83468 2.46167 7.11853 2.87704C6.40238 3.2924 5.75069 3.79728 5.16345 4.39169C4.57621 4.98609 4.07491 5.6342 3.65955 6.33602C3.24418 7.03784 2.91833 7.80412 2.68201 8.63485C2.44568 9.46558 2.3311 10.3178 2.33826 11.1915C2.33826 12.0723 2.45284 12.921 2.68201 13.7374C2.91117 14.5538 3.23344 15.3201 3.6488 16.0362C4.06417 16.7524 4.56905 17.4041 5.16345 17.9913C5.75785 18.5785 6.40597 19.0798 7.10779 19.4952C7.80961 19.9106 8.57589 20.2364 9.40662 20.4727C10.2373 20.7091 11.0896 20.8237 11.9633 20.8165ZM10.7924 12.7706L11.7592 13.7374L8.52576 16.9708L5.29236 13.7374L6.25916 12.7706L7.83826 14.339V5.69149H9.21326V14.339L10.7924 12.7706ZM17.6674 12.7706L18.6342 13.7374L15.4008 16.9708L12.1674 13.7374L13.1342 12.7706L14.7133 14.339V5.69149H16.0883V14.339L17.6674 12.7706Z" />
                        </GeometryGroup>
                    </Path.Data>
                </Path>
            </Canvas>
        </Viewbox>
    </ControlTemplate>

    <Style x:Key="DisplayButtonStyle" TargetType="Button">
        <Setter Property="Width" Value="50" />
        <Setter Property="Height" Value="50" />
        <Setter Property="Background" Value="#FFD9D9D9" />
        <Setter Property="Foreground" Value="#FF55657F" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="HorizontalAlignment" Value="Center" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="Margin" Value="0,0,7,0" />
        <Setter Property="BorderBrush" Value="{x:Null}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border
                        x:Name="Border"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="5">
                        <StackPanel
                            Height="39.5"
                            HorizontalAlignment="Center"
                            Orientation="Vertical">
                            <!--  Reference the Viewbox template  -->
                            <ContentControl Template="{StaticResource DisplayButtonIconTemplate}" />
                            <TextBlock
                                HorizontalAlignment="Center"
                                FontFamily="{StaticResource PoppinsFont}"
                                FontSize="11"
                                Text="Display" />
                        </StackPanel>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Border" Property="Background" Value="#FFB5B5B5" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="Border" Property="Background" Value="#FF9B9B9B" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <!--#endregion-->


    <!--#region Points DataGrid-->
    <!--  DataGrid Row Style  -->
    <Style x:Key="PointsDataGridRowStyle" TargetType="DataGridRow">
        <Style.Triggers>
            <Trigger Property="IsSelected" Value="True">
                <Setter Property="BorderBrush" Value="#686F93" />
                <Setter Property="Foreground" Value="White" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--  DataGrid Cell Style  -->
    <Style x:Key="PointsDataGridCellStyle" TargetType="DataGridCell">
        <Style.Triggers>
            <Trigger Property="IsSelected" Value="True">
                <Setter Property="Foreground" Value="White" />
                <Setter Property="Background" Value="#686F93" />
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--  DataGrid Text Cell Style  -->
    <Style x:Key="DataGridTextCellStyle" TargetType="TextBlock">
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="Padding" Value="5" />
        <Setter Property="FontSize" Value="12" />
    </Style>

    <!--  DataGrid Header Cell Style  -->
    <Style x:Key="DataGridHeaderCellStyle" TargetType="DataGridColumnHeader">
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="FontSize" Value="12.5" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Foreground" Value="#FF55657F" />
        <Setter Property="HorizontalAlignment" Value="Stretch" />
        <Setter Property="VerticalAlignment" Value="Center" />
    </Style>

    <!--  DataGrid Style with "No data..." message  -->
    <Style TargetType="{x:Type DataGrid}">
        <Style.Triggers>
            <!--  Trigger to check if there are items  -->
            <DataTrigger Binding="{Binding HasItems, RelativeSource={RelativeSource Self}}" Value="false">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type DataGrid}">
                            <Border
                                Padding="{TemplateBinding Padding}"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                SnapsToDevicePixels="True">
                                <ScrollViewer x:Name="DG_ScrollViewer" Focusable="False">
                                    <ScrollViewer.Template>
                                        <ControlTemplate TargetType="{x:Type ScrollViewer}">
                                            <Grid>
                                                <!--  Column Definitions  -->
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto" />
                                                    <ColumnDefinition Width="*" />
                                                    <ColumnDefinition Width="Auto" />
                                                </Grid.ColumnDefinitions>
                                                <!--  Row Definitions  -->
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto" />
                                                    <RowDefinition Height="*" />
                                                    <RowDefinition Height="Auto" />
                                                </Grid.RowDefinitions>

                                                <!--  Select All Button  -->
                                                <Button
                                                    Width="{Binding CellsPanelHorizontalOffset, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type DataGrid}}}"
                                                    Command="ApplicationCommands.SelectAll"
                                                    Focusable="False"
                                                    Style="{DynamicResource {ComponentResourceKey ResourceId=DataGridSelectAllButtonStyle,
                                                                                                  TypeInTargetAssembly={x:Type DataGrid}}}">
                                                    <Button.Visibility>
                                                        <Binding Path="HeadersVisibility" RelativeSource="{RelativeSource FindAncestor, AncestorType={x:Type DataGrid}}">
                                                            <Binding.ConverterParameter>
                                                                <DataGridHeadersVisibility>All</DataGridHeadersVisibility>
                                                            </Binding.ConverterParameter>
                                                        </Binding>
                                                    </Button.Visibility>
                                                </Button>

                                                <!--  Column Headers  -->
                                                <DataGridColumnHeadersPresenter x:Name="PART_ColumnHeadersPresenter" Grid.Column="1">
                                                    <DataGridColumnHeadersPresenter.Visibility>
                                                        <Binding Path="HeadersVisibility" RelativeSource="{RelativeSource FindAncestor, AncestorType={x:Type DataGrid}}">
                                                            <Binding.ConverterParameter>
                                                                <DataGridHeadersVisibility>Column</DataGridHeadersVisibility>
                                                            </Binding.ConverterParameter>
                                                        </Binding>
                                                    </DataGridColumnHeadersPresenter.Visibility>
                                                </DataGridColumnHeadersPresenter>

                                                <!--  Content Presenter  -->
                                                <ScrollContentPresenter
                                                    x:Name="PART_ScrollContentPresenter"
                                                    Grid.Row="1"
                                                    Grid.ColumnSpan="2"
                                                    CanContentScroll="{TemplateBinding CanContentScroll}"
                                                    Content="{TemplateBinding Content}"
                                                    ContentStringFormat="{TemplateBinding ContentStringFormat}"
                                                    ContentTemplate="{TemplateBinding ContentTemplate}" />

                                                <!--  Vertical Scroll Bar  -->
                                                <ScrollBar
                                                    x:Name="PART_VerticalScrollBar"
                                                    Grid.Row="1"
                                                    Grid.Column="2"
                                                    Maximum="{TemplateBinding ScrollableHeight}"
                                                    Orientation="Vertical"
                                                    ViewportSize="{TemplateBinding ViewportHeight}"
                                                    Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}"
                                                    Value="{Binding VerticalOffset, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}" />

                                                <!--  Horizontal Scroll Bar  -->
                                                <Grid Grid.Row="2" Grid.Column="1">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="{Binding NonFrozenColumnsViewportHorizontalOffset, RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type DataGrid}}}" />
                                                        <ColumnDefinition Width="*" />
                                                    </Grid.ColumnDefinitions>
                                                    <ScrollBar
                                                        x:Name="PART_HorizontalScrollBar"
                                                        Grid.Column="1"
                                                        Maximum="{TemplateBinding ScrollableWidth}"
                                                        Orientation="Horizontal"
                                                        ViewportSize="{TemplateBinding ViewportWidth}"
                                                        Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}"
                                                        Value="{Binding HorizontalOffset, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}" />
                                                </Grid>

                                                <!--  "No data" Label  -->
                                                <Label
                                                    Grid.Row="1"
                                                    Grid.Column="1"
                                                    HorizontalAlignment="Center"
                                                    VerticalAlignment="Center"
                                                    Content="No data..."
                                                    FontSize="13"
                                                    Foreground="#FF687A99"
                                                    Visibility="Visible" />
                                            </Grid>
                                        </ControlTemplate>
                                    </ScrollViewer.Template>
                                    <ItemsPresenter SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                                </ScrollViewer>
                            </Border>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </DataTrigger>
        </Style.Triggers>
    </Style>
    <!--#endregion-->


    <!--#region Define the ViewBox EmptySettings-->
    <Viewbox
        x:Key="EmptySettingsIcon"
        Width="30"
        Height="30"
        Stretch="Uniform">
        <Canvas Width="30" Height="30">
            <Path Data="M33.83 23.43a1.16 1.16 0 0 0-.71-1.12l-1.68-.5c-.09-.24-.18-.48-.29-.71l.78-1.44a1.16 1.16 0 0 0-.21-1.37l-1.42-1.41a1.16 1.16 0 0 0-1.37-.2l-1.45.76a8 8 0 0 0-.76-.32l-.48-1.58a1.15 1.15 0 0 0-1.11-.77h-2a1.16 1.16 0 0 0-1.11.82l-.47 1.54a8 8 0 0 0-.77.32l-1.42-.76a1.16 1.16 0 0 0-1.36.2l-1.45 1.4a1.16 1.16 0 0 0-.21 1.38l.74 1.33a8 8 0 0 0-.31.74l-1.58.47a1.15 1.15 0 0 0-.83 1.11v2a1.15 1.15 0 0 0 .83 1.1l1.59.47a8 8 0 0 0 .31.72l-.78 1.46a1.16 1.16 0 0 0 .21 1.37l1.42 1.4a1.16 1.16 0 0 0 1.37.21l1.48-.78c.23.11.47.2.72.29l.49 1.62a1.16 1.16 0 0 0 1.11.81h2a1.16 1.16 0 0 0 1.11-.82l.47-1.58c.24-.08.47-.18.7-.29l1.5.79a1.16 1.16 0 0 0 1.36-.2l1.42-1.4a1.16 1.16 0 0 0 .21-1.38l-.79-1.45q.16-.34.29-.69L33 26.5a1.15 1.15 0 0 0 .83-1.11Zm-1.6 1.63l-2.11.62l-.12.42a6 6 0 0 1-.5 1.19l-.21.38l1 1.91l-1 1l-2-1l-.37.2a6.2 6.2 0 0 1-1.2.49l-.42.12l-.63 2.09h-1.25l-.63-2.08l-.42-.12a6.2 6.2 0 0 1-1.21-.49l-.37-.2l-1.94 1l-1-1l1-1.94l-.22-.38a6 6 0 0 1-.46-1.27l-.17-.37l-2-.63v-1.31l2-.61l.13-.41a6 6 0 0 1 .53-1.23l.24-.44l-1-1.85l1-.94l1.89 1l.38-.21a6.2 6.2 0 0 1 1.26-.52l.41-.12l.63-2h1.38l.62 2l.41.12a6.2 6.2 0 0 1 1.22.52l.38.21l1.92-1l1 1l-1 1.89l.21.38a6 6 0 0 1 .5 1.21l.12.42l2.06.61Z" Fill="#FFB2B7C0" />
            <Path Data="M24.12 20.35a4 4 0 1 0 4.08 4a4.06 4.06 0 0 0-4.08-4m0 6.46a2.43 2.43 0 1 1 2.48-2.43a2.46 2.46 0 0 1-2.48 2.44Z" Fill="#FFB2B7C0" />
            <Path Data="M14.49 31H6V5h20v7.89a3.2 3.2 0 0 1 2 1.72V5a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v26a2 2 0 0 0 2 2h10.23l-1.1-1.08a3.1 3.1 0 0 1-.64-.92" Fill="#FFB2B7C0" />
        </Canvas>
    </Viewbox>
    <!--#endregion-->


    <!--#region Rounded TextBox Style-->
    <Style x:Key="RoundedTextBox" TargetType="TextBox">
        <Setter Property="Margin" Value="5,2" />
        <Setter Property="Padding" Value="5" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border
                        Background="White"
                        BorderBrush="Gray"
                        BorderThickness="1"
                        CornerRadius="5">
                        <ScrollViewer x:Name="PART_ContentHost" Margin="0" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value="0.6" />
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="PART_ContentHost" Property="Background" Value="#FFF0F0F0" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <!--#endregion-->


    <!--#region History ListView Style-->
    <Style x:Key="ListViewItemStyle" TargetType="ListViewItem">
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="MinHeight" Value="44" />
        <!--  Matches the py-4 class height  -->
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ListViewItem}">
                    <Border
                        x:Name="Bd"
                        Padding="0"
                        Background="{TemplateBinding Background}"
                        BorderThickness="0"
                        SnapsToDevicePixels="true">
                        <GridViewRowPresenter VerticalAlignment="{TemplateBinding VerticalContentAlignment}" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="ItemsControl.AlternationIndex" Value="0">
                            <Setter Property="Background" Value="White" />
                        </Trigger>
                        <Trigger Property="ItemsControl.AlternationIndex" Value="1">
                            <Setter Property="Background" Value="#F3F4F6" />
                            <!--  Light gray like bg-gray-100  -->
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter Property="Background" Value="#E5E7EB" />
                            <!--  Slightly darker on hover  -->
                        </Trigger>
                        <Trigger Property="IsSelected" Value="true">
                            <Setter Property="Background" Value="#DBEAFE" />
                            <!--  Light blue when selected  -->
                            <Setter Property="TextElement.Foreground" Value="#2563EB" />
                            <!--  Blue text when selected  -->
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="ModernListView" TargetType="ListView">
        <Setter Property="Background" Value="White" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Auto" />
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto" />
        <Setter Property="ScrollViewer.CanContentScroll" Value="True" />
        <Setter Property="ScrollViewer.PanningMode" Value="Both" />
        <Setter Property="Stylus.IsFlicksEnabled" Value="False" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
    </Style>

    <!--  Style for Column Headers  -->
    <Style x:Key="GridViewColumnHeaderStyle" TargetType="{x:Type GridViewColumnHeader}">
        <Setter Property="Foreground" Value="#6B7280" />
        <!--  text-gray-500  -->
        <Setter Property="Background" Value="White" />
        <Setter Property="BorderThickness" Value="0,0,0,1" />
        <Setter Property="BorderBrush" Value="#E5E7EB" />
        <!--  divide-gray-200  -->
        <Setter Property="ContentTemplate">
            <Setter.Value>
                <DataTemplate>
                    <TextBlock Text="{Binding}" />
                </DataTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Padding" Value="10,8" />
        <!--  px-6 py-3  -->
        <Setter Property="FontSize" Value="12" />
        <!--  text-xs  -->
        <Setter Property="FontWeight" Value="Medium" />
        <!--  font-medium  -->
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <!--  text-start  -->
    </Style>

    <!--  TextBlock style for cell content  -->
    <Style x:Key="CellTextBlockStyle" TargetType="TextBlock">
        <Setter Property="Padding" Value="8,12" />
        <!--  Reduced padding to fit narrower columns  -->
        <Setter Property="TextWrapping" Value="NoWrap" />
        <!--  whitespace-nowrap  -->
        <Setter Property="TextTrimming" Value="CharacterEllipsis" />
        <!--  Add ellipsis for text that doesn't fit  -->
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="FontSize" Value="12" />
        <!--  Slightly smaller text for better fit  -->
    </Style>


    <!--#endregion-->


    <!--#region Settings Rounded GroupBox Style-->
    <Style x:Key="RoundedGroupBox" TargetType="GroupBox">
        <Setter Property="Margin" Value="5" />
        <Setter Property="Padding" Value="8" />
        <Setter Property="BorderBrush" Value="#FF55657F" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="FontFamily" Value="Poppins" />
        <Setter Property="FontSize" Value="14" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="GroupBox">
                    <Border
                        Padding="{TemplateBinding Padding}"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="8">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>
                            <TextBlock
                                Margin="5,0,0,5"
                                FontWeight="Bold"
                                Foreground="{TemplateBinding Foreground}"
                                Text="{TemplateBinding Header}" />
                            <ContentPresenter Grid.Row="1" />
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <!--#endregion-->


    <!--#region Settings Rounded Button Style-->
    <Style x:Key="RoundedButton" TargetType="Button">
        <Setter Property="Margin" Value="3" />
        <Setter Property="Padding" Value="6,2" />
        <Setter Property="FontFamily" Value="Poppins" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="Background" Value="#FF55657F" />
        <Setter Property="Foreground" Value="White" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border
                        Padding="{TemplateBinding Padding}"
                        Background="{TemplateBinding Background}"
                        CornerRadius="6">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#FF3E4B63" />
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="#FF2C3653" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value="0.5" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <!--#endregion-->


    <!--#region Settings TabControl Style-->
    <Style TargetType="TabControl">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TabControl">
                    <Grid ClipToBounds="True">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>

                        <!--  Tab Headers (with gap between tabs)  -->
                        <TabPanel
                            Grid.Row="0"
                            Margin="0,0,0,10"
                            HorizontalAlignment="Center"
                            Background="Transparent"
                            IsItemsHost="True"
                            KeyboardNavigation.TabIndex="1">
                            <TabPanel.Resources>
                                <!--  Tab Gap Style  -->
                                <Style TargetType="TabItem">
                                    <Setter Property="Margin" Value="5,0" />
                                </Style>
                            </TabPanel.Resources>
                        </TabPanel>

                        <!--  Content Panel  -->
                        <Border
                            Grid.Row="1"
                            Width="330"
                            Height="395"
                            Background="White"
                            BorderBrush="White"
                            BorderThickness="1"
                            CornerRadius="10">
                            <ContentPresenter Margin="10" ContentSource="SelectedContent" />
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="TabItem">
        <Setter Property="Background" Value="#ffffff" />
        <Setter Property="Foreground" Value="#687a99" />
        <Setter Property="Width" Value="70" />
        <Setter Property="Height" Value="30" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TabItem">
                    <Border
                        x:Name="Border"
                        Margin="10,0,0,0"
                        Padding="5"
                        Background="{TemplateBinding Background}"
                        BorderBrush="#FFECECEC"
                        BorderThickness="1"
                        ClipToBounds="True"
                        CornerRadius="10">
                        <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                            <ContentPresenter ContentSource="Header" />
                        </StackPanel>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter TargetName="Border" Property="Background" Value="#dfe6f3" />
                            <Setter TargetName="Border" Property="BorderBrush" Value="#687a99" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <!--#endregion-->

    <!--#region Effects-->
    <BlurEffect x:Key="BlurEffectShadow" Radius="10" />
    <!--#endregion-->

</ResourceDictionary>