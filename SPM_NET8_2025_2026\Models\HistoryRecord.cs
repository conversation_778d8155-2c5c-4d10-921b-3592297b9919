﻿using System;

namespace SPM_NET8_2025_2026.Models
{
    [Serializable]
    public class HistoryRecord
    {
        public string FileName { get; set; }
        public string OperationType { get; set; }
        public string FileType { get; set; }
        public DateTime DateTime { get; set; }
        public int PointCount { get; set; }

        // Parameterless constructor (Required for XML serialization)
        public HistoryRecord() { }

        public HistoryRecord(string fileName, string operationType, string fileType, int pointCount)
        {
            FileName = fileName;
            OperationType = operationType;
            FileType = fileType;
            DateTime = DateTime.Now;
            PointCount = pointCount;
        }
    }
}
