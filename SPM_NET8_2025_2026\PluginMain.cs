
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Runtime;
using Autodesk.AutoCAD.Windows;
using SPM_NET8_2025_2026.Managers;
using System.Text;

// Assembly attributes
[assembly: ExtensionApplication(typeof(SPM_NET8_2025_2026.PluginMain))]
[assembly: CommandClass(typeof(SPM_NET8_2025_2026.PluginMain))]

namespace SPM_NET8_2025_2026
{

    public class PluginMain : IExtensionApplication
    {
        #region Constants and Static Fields

        private const string CMD_GROUP = "POINTFLOW"; // Internal command group name
        private const string PLUGIN_DISPLAY_NAME = "Survey Point Manager"; // User-facing name

        // --- Palettes GUIDs & Titles ---
        // MAIN_PALETTE_TITLE is already "Survey Point Manager" - GOOD
        private const string MAIN_PALETTE_TITLE = "Survey Point Manager";
        // MAIN_PALETTE_INTERNAL_NAME is for AutoCAD's internal tracking, can remain PointFlow related
        private const string MAIN_PALETTE_INTERNAL_NAME = "PointFlowMainTools";
        private static readonly Guid MAIN_PALETTE_SET_GUID = new Guid("C6CA2521-7CF9-4FA1-BA5F-328AFB71C072");

        // ENTITLEMENT_PALETTE_TITLE should reflect the plugin it's for
        private const string ENTITLEMENT_PALETTE_TITLE = "Survey Point Manager Registration";
        // ENTITLEMENT_PALETTE_INTERNAL_NAME can remain PointFlow related
        private const string ENTITLEMENT_PALETTE_INTERNAL_NAME = "PointFlowEntitlementPrompt";
        private static readonly Guid ENTITLEMENT_PALETTE_GUID = Guid.NewGuid();

        // --- Tab Indices for Main Palette ---
        private const int TAB_INDEX_HOME = 0;
        private const int TAB_INDEX_IMPORT = 1;
        private const int TAB_INDEX_EXPORT = 2;
        private const int TAB_INDEX_HISTORY = 3;
        private const int TAB_INDEX_SETTINGS = 4;

        // --- Palette Instances ---
        public static PaletteSet MainPaletteSet { get; private set; }
        private static PaletteSet _entitlementPromptPaletteSet;

        // --- UI Control Instances ---
        private static Views.EntitlementPromptControl _entitlementPromptControl;
        private static Views.HomePalette _homePaletteView;
        private static Views.ImportPalette _importPaletteView;
        private static Views.ExportPalette _exportPaletteView;
        private static Views.HistoryPalette _historyPaletteView;
        private static Views.SettingsPalette _settingsPaletteView;

        // --- State Management ---
        private static Document _associatedDocument = null;
        public static bool IsUserVerifiedThisSession { get; private set; } = false;
        private static EntitlementManager _entitlementManager;

        // --- Static Instance for Navigation ---
        private static PluginMain _instance;

        #endregion

        #region AutoCAD Helper Properties

        private static DocumentCollection DocumentManager => Autodesk.AutoCAD.ApplicationServices.Application.DocumentManager;
        private static Document ActiveDocument => DocumentManager.MdiActiveDocument;
        private static Database ActiveDatabase => ActiveDocument?.Database;
        private static Editor ActiveEditor => ActiveDocument?.Editor;

        #endregion

        #region IExtensionApplication Implementation

        public void Initialize()
        {
            try
            {
                _instance = this; // Set static instance
                // Register legacy encodings for .NET 8 compatibility (fixes CSV/TXT import encoding issues)
                Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

                _entitlementManager = new EntitlementManager();
                // Use PLUGIN_DISPLAY_NAME for user messages
                WriteToCommandLine($"\n{PLUGIN_DISPLAY_NAME} plugin initialized. Use _SURVEYPOINTMANAGER command.");
                DocumentManager.DocumentToBeDestroyed += OnDocumentToBeDestroyed;
            }
            catch (System.Exception ex) { LogException("Error during plugin initialization", ex); }
        }

        public void Terminate()
        {
            try
            {
                if (_entitlementPromptPaletteSet != null && !_entitlementPromptPaletteSet.IsDisposed)
                {
                    _entitlementPromptPaletteSet.Dispose();
                }

                if (MainPaletteSet != null && !MainPaletteSet.IsDisposed)
                {
                    MainPaletteSet.Dispose();
                }

                MainPaletteSet = null; _entitlementPromptPaletteSet = null;
                _associatedDocument = null; _entitlementPromptControl = null;
                _homePaletteView = null; _importPaletteView = null; _exportPaletteView = null;
                _historyPaletteView = null; _settingsPaletteView = null;
                _entitlementManager = null;

                DocumentManager.DocumentToBeDestroyed -= OnDocumentToBeDestroyed;
                // Use PLUGIN_DISPLAY_NAME
                WriteToCommandLine($"\n{PLUGIN_DISPLAY_NAME} plugin terminated.");
            }
            catch (System.Exception ex) { LogException("Error during plugin termination", ex); }
        }

        #endregion

        #region Event Handlers

        private void OnDocumentToBeDestroyed(object sender, DocumentCollectionEventArgs e)
        {
            if (e.Document == _associatedDocument)
            {
                if (MainPaletteSet != null && !MainPaletteSet.IsDisposed && MainPaletteSet.Visible)
                {
                    try { MainPaletteSet.Visible = false; }
                    catch (System.Exception ex) { LogException($"Error hiding {MAIN_PALETTE_TITLE} on close of '{e.Document?.Name ?? "document"}'", ex); }
                }
                _associatedDocument = null;
            }
        }

        #endregion

        #region AutoCAD Commands

        [CommandMethod(CMD_GROUP, "SURVEYPOINTMANAGER", CommandFlags.Modal)]
        public void ShowSurveyPointManager()
        {
            if (ActiveDocument == null)
            {
                WriteToCommandLine($"\n{PLUGIN_DISPLAY_NAME}: No active document found.");
                return;
            }

            if (IsUserVerifiedThisSession)
            {
                ShowMainApplicationPalette(ActiveDocument);
                return;
            }

            EntitlementManager.CachedEntitlementStatus cacheStatus = _entitlementManager.GetCachedStatus();
            if (cacheStatus == EntitlementManager.CachedEntitlementStatus.Entitled)
            {
                IsUserVerifiedThisSession = true;
                // Use PLUGIN_DISPLAY_NAME
                ActiveEditor?.WriteMessage($"\n{PLUGIN_DISPLAY_NAME}: License verified from cache for this session.");
                ShowMainApplicationPalette(ActiveDocument);
                return;
            }
            ShowEntitlementPromptPalette();
        }

        #endregion

        #region Palette Management Methods

        private void ShowMainApplicationPalette(Document doc)
        {
            try
            {
                if (doc == null)
                {
                    return;
                }

                _associatedDocument = doc;
                EnsureMainPaletteSetExists();
                if (MainPaletteSet != null && !MainPaletteSet.IsDisposed)
                {
                    MainPaletteSet.Visible = true;
                    MainPaletteSet.Activate(TAB_INDEX_HOME);
                }
            }
            catch (System.Exception ex) { LogException("Error showing main application palette", ex); }
        }

        private void ShowEntitlementPromptPalette()
        {
            try
            {
                EnsureEntitlementPromptPaletteExists();
                if (_entitlementPromptPaletteSet != null && !_entitlementPromptPaletteSet.IsDisposed)
                {
                    _entitlementPromptPaletteSet.Visible = true;
                }
            }
            catch (System.Exception ex) { LogException("Error showing entitlement prompt palette", ex); }
        }

        private void EnsureMainPaletteSetExists()
        {
            try
            {
                if (MainPaletteSet == null || MainPaletteSet.IsDisposed)
                {
                    MainPaletteSet = new PaletteSet(MAIN_PALETTE_TITLE, MAIN_PALETTE_INTERNAL_NAME, MAIN_PALETTE_SET_GUID)
                    {
                        Size = new Size(400, 600),
                        MinimumSize = new Size(400, 600),
                        DockEnabled = DockSides.Left | DockSides.Right,
                        Style = PaletteSetStyles.ShowPropertiesMenu | PaletteSetStyles.ShowAutoHideButton | PaletteSetStyles.ShowCloseButton,
                        //Icon = Properties.Resources.SurveyPointManager32x32
                    };
                    AddMainPalettes();
                }
            }
            catch (System.Exception ex) { LogException("Error ensuring main palette set exists", ex); }
        }

        private void EnsureEntitlementPromptPaletteExists()
        {
            try
            {
                if (_entitlementPromptPaletteSet == null || _entitlementPromptPaletteSet.IsDisposed)
                {
                    _entitlementPromptPaletteSet = new PaletteSet(ENTITLEMENT_PALETTE_TITLE, ENTITLEMENT_PALETTE_INTERNAL_NAME, ENTITLEMENT_PALETTE_GUID)
                    {
                        Size = new Size(400, 600),
                        MinimumSize = new Size(400, 600),
                        DockEnabled = DockSides.None,
                        Style = PaletteSetStyles.ShowCloseButton
                    };
                    _entitlementPromptControl = new Views.EntitlementPromptControl(_entitlementManager);
                    _entitlementPromptControl.EntitlementSucceeded += OnEntitlementSucceeded;
                    _entitlementPromptPaletteSet.AddVisual(ENTITLEMENT_PALETTE_TITLE, _entitlementPromptControl);
                }
            }
            catch (System.Exception ex) { LogException("Error ensuring entitlement prompt palette exists", ex); }
        }

        private void OnEntitlementSucceeded()
        {
            try
            {
                IsUserVerifiedThisSession = true;
                if (_entitlementPromptPaletteSet != null && _entitlementPromptPaletteSet.Visible)
                {
                    _entitlementPromptPaletteSet.Visible = false;
                }
                ShowMainApplicationPalette(ActiveDocument);
            }
            catch (System.Exception ex) { LogException("Error handling entitlement success", ex); }
        }

        private void AddMainPalettes()
        {
            try
            {
                _homePaletteView = new Views.HomePalette();
                _importPaletteView = new Views.ImportPalette();
                _exportPaletteView = new Views.ExportPalette();
                _historyPaletteView = new Views.HistoryPalette();
                _settingsPaletteView = new Views.SettingsPalette();

                MainPaletteSet.AddVisual("Home", _homePaletteView);
                MainPaletteSet.AddVisual("Import", _importPaletteView);
                MainPaletteSet.AddVisual("Export", _exportPaletteView);
                MainPaletteSet.AddVisual("History", _historyPaletteView);
                MainPaletteSet.AddVisual("Settings", _settingsPaletteView);
                WriteToCommandLine($"\n{MAIN_PALETTE_TITLE}: Main palettes added.");
            }
            catch (System.Exception ex) { LogException("Error adding main palettes", ex); }
        }

        #endregion

        #region Helper Methods

        private static void WriteToCommandLine(string message)
        {
            try { ActiveEditor?.WriteMessage(message); }
            catch (System.Exception ex) { LogException("Error writing to command line", ex); }
        }

        private static void LogException(string context, System.Exception ex)
        {
            try
            {
                string logMessage = $"\n{PLUGIN_DISPLAY_NAME} Error [{context}]: {ex.Message}";
                ActiveEditor?.WriteMessage(logMessage);
                System.Diagnostics.Debug.WriteLine($"{context}: {ex}");
            }
            catch { /* Ignore logging errors */ }
        }

        #endregion

        #region Public Navigation Methods

        public static void ShowImportPalette() => _instance?.EnsureEntitledAndActivateTab(TAB_INDEX_IMPORT, "Import");
        public static void ShowExportPalette() => _instance?.EnsureEntitledAndActivateTab(TAB_INDEX_EXPORT, "Export");
        public static void ShowHistoryPalette() => _instance?.EnsureEntitledAndActivateTab(TAB_INDEX_HISTORY, "History");
        public static void ShowSettingsPalette() => _instance?.EnsureEntitledAndActivateTab(TAB_INDEX_SETTINGS, "Settings");

        private void ActivateMainPaletteTab(int tabIndex, string tabNameForLogging)
        {
            try
            {
                if (MainPaletteSet != null && !MainPaletteSet.IsDisposed)
                {
                    MainPaletteSet.Activate(tabIndex);
                    WriteToCommandLine($"\n{MAIN_PALETTE_TITLE}: Activated '{tabNameForLogging}' tab.");
                }
            }
            catch (System.Exception ex) { LogException($"Error activating '{tabNameForLogging}' tab", ex); }
        }

        private void EnsureEntitledAndActivateTab(int tabIndex, string tabNameForLogging)
        {
            if (IsUserVerifiedThisSession)
            {
                // Check if the palette is already visible and associated with the current document.
                if (MainPaletteSet != null && !MainPaletteSet.IsDisposed && MainPaletteSet.Visible && _associatedDocument == ActiveDocument)
                {
                    // If it's already visible, activate the correct tab.
                    ActivateMainPaletteTab(tabIndex, tabNameForLogging);
                }
                else
                {
                    // If the palette is hidden, uninitialized, or associated with a different document,
                    // then call the master "show" method to display it and set it up.
                    ShowMainApplicationPalette(ActiveDocument);

                    // After showing it, explicitly ensure the correct tab is active.
                    if (MainPaletteSet != null && !MainPaletteSet.IsDisposed && MainPaletteSet.Visible)
                    {
                        ActivateMainPaletteTab(tabIndex, tabNameForLogging);
                    }
                }
            }
            else
            {
                // If the user is not verified, show the entitlement prompt.
                ActiveEditor?.WriteMessage($"\n{PLUGIN_DISPLAY_NAME}: License verification required to access '{tabNameForLogging}'.");
                ShowEntitlementPromptPalette();
            }
        }

        #endregion
    }
}