﻿<UserControl
    x:Class="SPM_NET46_2017_2018.Views.ImportPalette"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:SPM_NET46_2017_2018.Views"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:partialViews="clr-namespace:SPM_NET46_2017_2018.PartialViews"
    Width="400"
    Height="600"
    MinWidth="400"
    MinHeight="600"
    mc:Ignorable="d">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/SPM_NET46_2017_2018;component/ResourceDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid Background="#FFECECEC">
        <!--  Main Grid Definition  -->
        <Grid.RowDefinitions>
            <!--  Header  -->
            <RowDefinition Height="Auto" />
            <!--  Import, Setings and Display  -->
            <RowDefinition Height="Auto" />
            <!--  Controls (Search, Delete All, Undo, and Redo)  -->
            <RowDefinition Height="Auto" />
            <!--  DataGrid (Point List)  -->
            <RowDefinition Height="Auto" />
            <!--  Footer (point counter, Add Table, Add Points to drawing, Formats Informations (Like SDR), and Export)  -->
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>


        <!--#region Header-->
        <partialViews:Header
            Grid.Row="0"
            HorizontalAlignment="Stretch"
            VerticalAlignment="Top"
            BreadcrumbText="IMPORT" />
        <!--#endregion-->


        <!--#region Import, Setings and Display-->
        <Grid Grid.Row="1" Margin="0,5,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <GroupBox
                Grid.Column="0"
                Height="77"
                Margin="5,0,2,0"
                Background="#80FFFFFF"
                BorderBrush="#80BFB6EA"
                BorderThickness="1.5"
                Foreground="#FF464F9E">
                <GroupBox.Header>
                    <TextBlock FontFamily="{StaticResource PoppinsFont}" Text="File Selection" />
                </GroupBox.Header>

                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <Button
                        x:Name="btnImportFile"
                        Grid.RowSpan="2"
                        Grid.Column="0"
                        Margin="4,0,0,0"
                        HorizontalAlignment="Center"
                        Click="btnImportFile_Click"
                        Style="{StaticResource ImportButtonStyle}"
                        ToolTip="Click to browse and select a data file" />

                    <!--  Supported file formats text  -->
                    <TextBlock
                        x:Name="txtSupportedFormats"
                        Grid.Row="0"
                        Grid.Column="1"
                        Margin="3,0,0,0"
                        VerticalAlignment="Center"
                        FontFamily="{StaticResource PoppinsFont}"
                        FontSize="9"
                        Foreground="#FF687A99"
                        TextTrimming="CharacterEllipsis"
                        TextWrapping="Wrap"
                        ToolTip="List of supported file formats">
                        <Run x:Name="runCsv" Text="CSV," />
                        <Run x:Name="runTxt" Text="TXT," />
                        <Run x:Name="runKml" Text="KML," />
                        <Run x:Name="runIdx" Text="IDX," />
                        <Run x:Name="runSdr" Text="SDR," />
                        <Run x:Name="runGsi" Text="GSI" />
                    </TextBlock>

                    <!--  Selected filename display  -->
                    <TextBlock
                        x:Name="txtSelectedFileName"
                        Grid.Row="1"
                        Grid.Column="2"
                        Width="115"
                        Margin="6,0,0,0"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        FontFamily="{StaticResource PoppinsFont}"
                        FontSize="11"
                        Foreground="#FF6993B9"
                        Text="Select File Please ..."
                        ToolTip="Name of the currently selected file"
                        ToolTipService.InitialShowDelay="200" />

                </Grid>
            </GroupBox>

            <GroupBox
                Grid.Column="1"
                Height="77"
                Margin="2,0,5,0"
                Padding="0,2,0,0"
                Background="#80FFFFFF"
                BorderBrush="#80BFB6EA"
                BorderThickness="1.5"
                Foreground="#FF464F9E">
                <GroupBox.Header>
                    <TextBlock FontFamily="{StaticResource PoppinsFont}" Text="Settings and Display" />
                </GroupBox.Header>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>

                    <!--  ================= Import Settings ==================  -->

                    <!--  Empty state indicator (shown when no file selected)  -->
                    <StackPanel
                        x:Name="pnlEmptyFileSettings"
                        Grid.Column="0"
                        HorizontalAlignment="center"
                        VerticalAlignment="Center"
                        Orientation="Horizontal"
                        ToolTip="No file selected"
                        Visibility="Visible">
                        <Viewbox
                            Width="30"
                            Height="30"
                            Stretch="Uniform">
                            <Canvas Width="30" Height="30">
                                <Path Data="M33.83 23.43a1.16 1.16 0 0 0-.71-1.12l-1.68-.5c-.09-.24-.18-.48-.29-.71l.78-1.44a1.16 1.16 0 0 0-.21-1.37l-1.42-1.41a1.16 1.16 0 0 0-1.37-.2l-1.45.76a8 8 0 0 0-.76-.32l-.48-1.58a1.15 1.15 0 0 0-1.11-.77h-2a1.16 1.16 0 0 0-1.11.82l-.47 1.54a8 8 0 0 0-.77.32l-1.42-.76a1.16 1.16 0 0 0-1.36.2l-1.45 1.4a1.16 1.16 0 0 0-.21 1.38l.74 1.33a8 8 0 0 0-.31.74l-1.58.47a1.15 1.15 0 0 0-.83 1.11v2a1.15 1.15 0 0 0 .83 1.1l1.59.47a8 8 0 0 0 .31.72l-.78 1.46a1.16 1.16 0 0 0 .21 1.37l1.42 1.4a1.16 1.16 0 0 0 1.37.21l1.48-.78c.**********.72.29l.49 1.62a1.16 1.16 0 0 0 1.11.81h2a1.16 1.16 0 0 0 1.11-.82l.47-1.58c.24-.08.47-.18.7-.29l1.5.79a1.16 1.16 0 0 0 1.36-.2l1.42-1.4a1.16 1.16 0 0 0 .21-1.38l-.79-1.45q.16-.34.29-.69L33 26.5a1.15 1.15 0 0 0 .83-1.11Zm-1.6 1.63l-2.11.62l-.12.42a6 6 0 0 1-.5 1.19l-.21.38l1 1.91l-1 1l-2-1l-.37.2a6.2 6.2 0 0 1-1.2.49l-.42.12l-.63 2.09h-1.25l-.63-2.08l-.42-.12a6.2 6.2 0 0 1-1.21-.49l-.37-.2l-1.94 1l-1-1l1-1.94l-.22-.38a6 6 0 0 1-.46-1.27l-.17-.37l-2-.63v-1.31l2-.61l.13-.41a6 6 0 0 1 .53-1.23l.24-.44l-1-1.85l1-.94l1.89 1l.38-.21a6.2 6.2 0 0 1 1.26-.52l.41-.12l.63-2h1.38l.62 2l.41.12a6.2 6.2 0 0 1 1.22.52l.38.21l1.92-1l1 1l-1 1.89l.21.38a6 6 0 0 1 .5 1.21l.12.42l2.06.61Z" Fill="#FFB2B7C0" />
                                <Path Data="M24.12 20.35a4 4 0 1 0 4.08 4a4.06 4.06 0 0 0-4.08-4m0 6.46a2.43 2.43 0 1 1 2.48-2.43a2.46 2.46 0 0 1-2.48 2.44Z" Fill="#FFB2B7C0" />
                                <Path Data="M14.49 31H6V5h20v7.89a3.2 3.2 0 0 1 2 1.72V5a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v26a2 2 0 0 0 2 2h10.23l-1.1-1.08a3.1 3.1 0 0 1-.64-.92" Fill="#FFB2B7C0" />
                            </Canvas>
                        </Viewbox>
                    </StackPanel>

                    <!--  CSV and TXT file specific settings  -->
                    <StackPanel
                        x:Name="pnlCsvFileSettings"
                        Grid.Column="0"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Orientation="Vertical"
                        ToolTip="CSV and TXT file format settings"
                        Visibility="Collapsed">

                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontFamily="{StaticResource PoppinsFont}"
                            FontSize="12"
                            Foreground="#FF7A7A9A"
                            Text="Points File Format" />

                        <!--  Format Selection Dropdown  -->
                        <ComboBox
                            x:Name="CSVFormatComboBox"
                            Grid.Row="0"
                            Width="118"
                            Height="25"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            VerticalContentAlignment="Center"
                            Background="White"
                            BorderBrush="#FFD3D3D3"
                            BorderThickness="1"
                            FontFamily="{StaticResource PoppinsFont}"
                            FontSize="11"
                            Foreground="#FF687A99"
                            Style="{StaticResource RoundedComboBox}">

                            <ComboBoxItem IsEnabled="False" IsSelected="True">
                                Select format...
                            </ComboBoxItem>

                            <!--  With Point Number first, Easting before Northing  -->
                            <ComboBoxItem Content="P E N (Point, East, North)" Tag="PEN" />
                            <ComboBoxItem Content="P E N Z (Point, East, North, Elevation)" Tag="PENZ" />
                            <ComboBoxItem Content="P E N Z D (Point, East, North, Elevation, Description)" Tag="PENZD" />

                            <!--  With Point Number first, Northing before Easting  -->
                            <ComboBoxItem Content="P N E (Point, North, East)" Tag="PNE" />
                            <ComboBoxItem Content="P N E Z (Point, North, East, Elevation)" Tag="PNEZ" />
                            <ComboBoxItem Content="P N E Z D (Point, North, East, Elevation, Description)" Tag="PNEZD" />

                            <!--  With Easting first  -->
                            <ComboBoxItem Content="E N (East, North)" Tag="EN" />
                            <ComboBoxItem Content="E N Z (East, North, Elevation)" Tag="ENZ" />
                            <ComboBoxItem Content="E N Z D (East, North, Elevation, Description)" Tag="ENZD" />

                            <!--  With Northing first  -->
                            <ComboBoxItem Content="N E (North, East)" Tag="NE" />
                            <ComboBoxItem Content="N E Z (North, East, Elevation)" Tag="NEZ" />
                            <ComboBoxItem Content="N E Z D (North, East, Elevation, Description)" Tag="NEZD" />
                        </ComboBox>

                    </StackPanel>

                    <!--  KML file specific settings  -->
                    <StackPanel
                        x:Name="pnlKmlFileSettings"
                        Grid.Column="0"
                        Width="125"
                        HorizontalAlignment="Center"
                        Orientation="Vertical"
                        ToolTip="KML file coordinate system settings"
                        Visibility="Collapsed">
                        <Grid Width="120" Height="50">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*" />
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="56" />
                                <ColumnDefinition />
                            </Grid.ColumnDefinitions>

                            <!--  Hemisphere selection  -->
                            <TextBlock
                                x:Name="txtKmlHemisphereLabel"
                                Grid.Column="0"
                                Width="40"
                                Height="18"
                                Margin="10,0,0,0"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Center"
                                FontFamily="{StaticResource PoppinsFont}"
                                FontSize="12"
                                Foreground="#FF687A99"
                                Text="Hemis."
                                ToolTip="Hemisphere label" />

                            <ComboBox
                                x:Name="cboKmlHemisphere"
                                Grid.Row="0"
                                Grid.Column="1"
                                Width="55"
                                Margin="5,2,4,1"
                                HorizontalContentAlignment="Center"
                                VerticalContentAlignment="Center"
                                FontFamily="{StaticResource PoppinsFont}"
                                FontSize="10"
                                Foreground="#FF687A99"
                                Style="{StaticResource RoundedComboBox}"
                                ToolTip="Select North or South hemisphere">
                                <ComboBoxItem Content="North" IsSelected="True" />
                                <ComboBoxItem Content="South" />
                            </ComboBox>

                            <!--  UTM Zone selection  -->
                            <TextBlock
                                x:Name="txtKmlZoneLabel"
                                Grid.Row="1"
                                Grid.Column="0"
                                Width="29"
                                Margin="10,0,0,0"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Center"
                                FontFamily="{StaticResource PoppinsFont}"
                                FontSize="12"
                                Foreground="#FF687A99"
                                Text="Zone"
                                ToolTip="UTM Zone label" />

                            <ComboBox
                                x:Name="cboKmlUtmZone"
                                Grid.Row="1"
                                Grid.Column="1"
                                Width="55"
                                Height="Auto"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                HorizontalContentAlignment="Center"
                                VerticalContentAlignment="Center"
                                FontFamily="{StaticResource PoppinsFont}"
                                FontSize="10"
                                Foreground="#FF687A99"
                                SelectedIndex="36"
                                Style="{StaticResource RoundedComboBox}"
                                ToolTip="Select UTM zone number (1-60)">

                                <!--  UTM Zone numbers 1-60  -->
                                <ComboBoxItem Content="1" />
                                <ComboBoxItem Content="2" />
                                <ComboBoxItem Content="3" />
                                <ComboBoxItem Content="4" />
                                <ComboBoxItem Content="5" />
                                <ComboBoxItem Content="6" />
                                <ComboBoxItem Content="7" />
                                <ComboBoxItem Content="8" />
                                <ComboBoxItem Content="9" />
                                <ComboBoxItem Content="10" />
                                <ComboBoxItem Content="11" />
                                <ComboBoxItem Content="12" />
                                <ComboBoxItem Content="13" />
                                <ComboBoxItem Content="14" />
                                <ComboBoxItem Content="15" />
                                <ComboBoxItem Content="16" />
                                <ComboBoxItem Content="17" />
                                <ComboBoxItem Content="18" />
                                <ComboBoxItem Content="19" />
                                <ComboBoxItem Content="20" />
                                <ComboBoxItem Content="21" />
                                <ComboBoxItem Content="22" />
                                <ComboBoxItem Content="23" />
                                <ComboBoxItem Content="24" />
                                <ComboBoxItem Content="25" />
                                <ComboBoxItem Content="26" />
                                <ComboBoxItem Content="27" />
                                <ComboBoxItem Content="28" />
                                <ComboBoxItem Content="29" />
                                <ComboBoxItem Content="30" />
                                <ComboBoxItem Content="31" />
                                <ComboBoxItem Content="32" />
                                <ComboBoxItem Content="33" />
                                <ComboBoxItem Content="34" />
                                <ComboBoxItem Content="35" />
                                <ComboBoxItem Content="36" />
                                <ComboBoxItem Content="37" />
                                <ComboBoxItem Content="38" />
                                <ComboBoxItem Content="39" />
                                <ComboBoxItem Content="40" />
                                <ComboBoxItem Content="41" />
                                <ComboBoxItem Content="42" />
                                <ComboBoxItem Content="43" />
                                <ComboBoxItem Content="44" />
                                <ComboBoxItem Content="45" />
                                <ComboBoxItem Content="46" />
                                <ComboBoxItem Content="47" />
                                <ComboBoxItem Content="48" />
                                <ComboBoxItem Content="49" />
                                <ComboBoxItem Content="50" />
                                <ComboBoxItem Content="51" />
                                <ComboBoxItem Content="52" />
                                <ComboBoxItem Content="53" />
                                <ComboBoxItem Content="54" />
                                <ComboBoxItem Content="55" />
                                <ComboBoxItem Content="56" />
                                <ComboBoxItem Content="57" />
                                <ComboBoxItem Content="58" />
                                <ComboBoxItem Content="59" />
                                <ComboBoxItem Content="60" />
                            </ComboBox>
                        </Grid>
                    </StackPanel>

                    <!--  GSI file specific settings  -->
                    <StackPanel
                        x:Name="pnlGsiFileSettings"
                        Grid.Column="0"
                        MaxHeight="60"
                        HorizontalAlignment="center"
                        VerticalAlignment="Center"
                        Orientation="Vertical"
                        ToolTip="GSI file format settings"
                        Visibility="Collapsed">

                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>
                        </Grid>

                        <ComboBox
                            x:Name="cboGsiFormatVariant"
                            Grid.Row="0"
                            Width="65"
                            Height="25"
                            HorizontalContentAlignment="Center"
                            VerticalContentAlignment="Center"
                            FontFamily="{StaticResource PoppinsFont}"
                            FontSize="12"
                            Foreground="#FF687A99"
                            SelectionChanged="GSIFileVariant_SelectionChanged"
                            Style="{StaticResource RoundedComboBox}"
                            ToolTip="Select GSI format variant (8 or 16 bit)">
                            <ComboBoxItem Content="GSI 8" IsSelected="True" />
                            <ComboBoxItem Content="GSI 16" />
                        </ComboBox>

                        <TextBlock
                            x:Name="txtGsiSelectionInfo"
                            Grid.Row="1"
                            Margin="0,5,0,0"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontFamily="{StaticResource PoppinsFont}"
                            FontSize="9"
                            Foreground="#FF687A99"
                            Text=""
                            TextWrapping="Wrap"
                            ToolTip="Additional information about GSI selection" />
                    </StackPanel>

                    <!--  ================= End Import Settings ==============  -->


                    <!--  Display button (enabled when a file is selected)  -->
                    <Button
                        x:Name="btnDisplayPoints"
                        Grid.Column="1"
                        Margin="0,0,2,0"
                        Click="btnDisplayPoints_Click"
                        IsEnabled="False"
                        Style="{StaticResource DisplayButtonStyle}"
                        ToolTip="Display the points from the selected file in the drawing" />
                </Grid>
            </GroupBox>
        </Grid>
        <!--#endregion-->


        <!--#region Controls (Search, Delete All, Undo, and Redo)-->
        <Grid
            Grid.Row="2"
            Width="380"
            Margin="0,5,0,5"
            VerticalAlignment="Stretch">
            <Grid.ColumnDefinitions>
                <!--  Search Box Section  -->
                <ColumnDefinition Width="*" />
                <!--  Buttons Section  -->
                <ColumnDefinition Width="auto" />
            </Grid.ColumnDefinitions>

            <!--  Search Box Container  -->
            <Border
                Grid.Column="0"
                Height="30"
                Margin="0,0,10,0"
                Background="#F3F4F6"
                BorderBrush="#D1D5DB"
                BorderThickness="1"
                CornerRadius="8">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <!--  Search Icon  -->
                    <Border
                        Grid.Column="0"
                        Width="34"
                        Height="30"
                        VerticalAlignment="Center">
                        <Viewbox
                            Width="18"
                            Height="18"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Stretch="Uniform">
                            <Canvas Width="24" Height="24">
                                <Path
                                    Fill="#556085"
                                    Stroke="#6B7280"
                                    StrokeLineJoin="Round"
                                    StrokeThickness="1">
                                    <Path.Data>
                                        <PathGeometry Figures="M21.71 20.29L18 16.61A9 9 0 1 0 16.61 18l3.68 3.68a1 1 0 0 0 1.42 0a1 1 0 0 0 0-1.39M11 18a7 7 0 1 1 7-7a7 7 0 0 1-7 7" />
                                    </Path.Data>
                                </Path>
                            </Canvas>
                        </Viewbox>
                    </Border>

                    <!--  TextBox  -->
                    <TextBox
                        x:Name="SearchPointsTextBox"
                        Grid.Column="1"
                        Padding="0,0"
                        VerticalContentAlignment="Center"
                        Background="Transparent"
                        BorderThickness="0"
                        FontSize="13"
                        Foreground="#111827"
                        TextChanged="SearchPointsTextBox_TextChanged">
                        <TextBox.Resources>
                            <Style TargetType="{x:Type TextBox}">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="{x:Type TextBox}">
                                            <Border
                                                x:Name="border"
                                                Background="{TemplateBinding Background}"
                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                BorderThickness="{TemplateBinding BorderThickness}">
                                                <Grid>
                                                    <ScrollViewer
                                                        x:Name="PART_ContentHost"
                                                        Focusable="False"
                                                        HorizontalScrollBarVisibility="Hidden"
                                                        VerticalScrollBarVisibility="Hidden" />
                                                    <TextBlock
                                                        x:Name="WatermarkText"
                                                        VerticalAlignment="Center"
                                                        Foreground="#6B7280"
                                                        Text="Search Points..."
                                                        Visibility="Collapsed" />
                                                </Grid>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <MultiTrigger>
                                                    <MultiTrigger.Conditions>
                                                        <Condition Property="Text" Value="" />
                                                        <Condition Property="IsFocused" Value="False" />
                                                    </MultiTrigger.Conditions>
                                                    <Setter TargetName="WatermarkText" Property="Visibility" Value="Visible" />
                                                </MultiTrigger>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter TargetName="border" Property="BorderBrush" Value="#3B82F6" />
                                                </Trigger>
                                                <Trigger Property="IsFocused" Value="True">
                                                    <Setter TargetName="border" Property="BorderBrush" Value="#3B82F6" />
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </TextBox.Resources>
                    </TextBox>
                </Grid>
            </Border>

            <!--  Button Stack Panel  -->
            <StackPanel
                Grid.Column="1"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                Orientation="Horizontal">
                <!--  Undo button (positioned on left)  -->
                <Button
                    x:Name="UndoBTN"
                    Width="26"
                    Height="26"
                    Margin="0,0,0,0"
                    Background="#FFD9D9D9"
                    BorderThickness="0"
                    Click="UndoBTN_Click"
                    Cursor="Hand"
                    Foreground="#FF55657F"
                    ToolTip="Undo last action">
                    <Button.Template>
                        <ControlTemplate TargetType="{x:Type Button}">
                            <Border
                                x:Name="Border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="5 0 0 5">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="Border" Property="Background" Value="#FFB5B5B5" />
                                    <Setter TargetName="Border" Property="BorderBrush" Value="#FFB5B5B5" />
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter TargetName="Border" Property="Background" Value="#FF9B9B9B" />
                                    <Setter TargetName="Border" Property="BorderBrush" Value="#FF9B9B9B" />
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Button.Template>

                    <Viewbox
                        Width="24"
                        Height="24"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Stretch="Uniform">
                        <Canvas Width="24" Height="24">
                            <Path Fill="#FF55657F">
                                <Path.Data>
                                    <PathGeometry Figures="M7.404 18v-1h7.254q1.556 0 2.65-1.067q1.096-1.067 1.096-2.606t-1.095-2.596q-1.096-1.058-2.651-1.058H6.916l2.965 2.965l-.708.708L5 9.173L9.173 5l.708.708l-2.965 2.965h7.742q1.963 0 3.355 1.354q1.39 1.354 1.39 3.3t-1.39 3.31T14.657 18z" />
                                </Path.Data>
                            </Path>
                        </Canvas>
                    </Viewbox>
                </Button>

                <!--  Redo button (positioned in middle)  -->
                <Button
                    x:Name="RedoBTN"
                    Width="26"
                    Height="26"
                    Margin="0,0,0,0"
                    Background="#FFD9D9D9"
                    BorderThickness="0"
                    Click="RedoBTN_Click"
                    Cursor="Hand"
                    Foreground="#FF55657F"
                    ToolTip="Redo last undone action">
                    <Button.Template>
                        <ControlTemplate TargetType="{x:Type Button}">
                            <Border
                                x:Name="Border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="0 5 5 0">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="Border" Property="Background" Value="#FFB5B5B5" />
                                    <Setter TargetName="Border" Property="BorderBrush" Value="#FFB5B5B5" />
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter TargetName="Border" Property="Background" Value="#FF9B9B9B" />
                                    <Setter TargetName="Border" Property="BorderBrush" Value="#FF9B9B9B" />
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Button.Template>

                    <Viewbox
                        Width="24"
                        Height="24"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Stretch="Uniform">
                        <Canvas Width="24" Height="24">
                            <Path Fill="#FF55657F">
                                <Path.Data>
                                    <PathGeometry Figures="M9.342 18q-1.963 0-3.355-1.364t-1.39-3.309t1.39-3.3Q7.38 8.673 9.343 8.673h7.743L14.12 5.708L14.828 5L19 9.173l-4.173 4.173l-.708-.707l2.966-2.966H9.342q-1.556 0-2.65 1.058q-1.096 1.058-1.096 2.596t1.095 2.606Q7.787 17 9.342 17h7.254v1z" />
                                </Path.Data>
                            </Path>
                        </Canvas>
                    </Viewbox>
                </Button>

                <!--  Delete button (positioned on right)  -->
                <Button
                    x:Name="DeleteAllPointsBTN"
                    Width="26"
                    Height="26"
                    Margin="25,0,0,0"
                    Background="#FFD9D9D9"
                    BorderThickness="0"
                    Click="DeleteAllPointsBTN_Click"
                    Cursor="Hand"
                    Foreground="#FF55657F"
                    ToolTip="Delete all points">
                    <Button.Template>
                        <ControlTemplate TargetType="{x:Type Button}">
                            <Border
                                x:Name="Border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="5">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="Border" Property="Background" Value="#FFB5B5B5" />
                                    <Setter TargetName="Border" Property="BorderBrush" Value="#FFB5B5B5" />
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter TargetName="Border" Property="Background" Value="#FF9B9B9B" />
                                    <Setter TargetName="Border" Property="BorderBrush" Value="#FF9B9B9B" />
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Button.Template>
                    <Viewbox
                        Width="20"
                        Height="20"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Stretch="Uniform">
                        <Canvas Width="20" Height="20">
                            <Path Fill="#FF55657F">
                                <Path.Data>
                                    <PathGeometry Figures="M8.5 4h3a1.5 1.5 0 0 0-3 0m-1 0a2.5 2.5 0 0 1 5 0h5a.5.5 0 0 1 0 1h-1.054l-1.194 10.344A3 3 0 0 1 12.272 18H7.728a3 3 0 0 1-2.98-2.656L3.554 5H2.5a.5.5 0 0 1 0-1zM5.741 15.23A2 2 0 0 0 7.728 17h4.544a2 2 0 0 0 1.987-1.77L15.439 5H4.561zM8.5 7.5A.5.5 0 0 1 9 8v6a.5.5 0 0 1-1 0V8a.5.5 0 0 1 .5-.5M12 8a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0z" />
                                </Path.Data>
                            </Path>
                        </Canvas>
                    </Viewbox>
                </Button>
            </StackPanel>
        </Grid>
        <!--#endregion-->


        <!--#region DataGrid (Point List)-->
        <Border Grid.Row="3" Padding="3">
            <DataGrid
                Name="PointsDataGrid"
                Width="380"
                Height="350"
                HorizontalAlignment="Center"
                VerticalAlignment="Top"
                HorizontalContentAlignment="Center"
                VerticalContentAlignment="Center"
                AlternatingRowBackground="White"
                AlternationCount="2"
                AutoGenerateColumns="False"
                Background="#F5F5F5"
                BorderBrush="{x:Null}"
                CellStyle="{StaticResource PointsDataGridCellStyle}"
                ColumnHeaderHeight="30"
                EnableRowVirtualization="True"
                FontFamily="{StaticResource PoppinsFont}"
                Foreground="#FF55657F"
                GridLinesVisibility="Vertical"
                HeadersVisibility="Column"
                HorizontalGridLinesBrush="{x:Null}"
                HorizontalScrollBarVisibility="Disabled"
                ItemsSource="{Binding Points}"
                MinColumnWidth="7"
                RowBackground="#FFE4E4E4"
                RowHeight="25"
                RowStyle="{StaticResource PointsDataGridRowStyle}"
                VerticalGridLinesBrush="White"
                VerticalScrollBarVisibility="Hidden">

                <!--  DataGrid Column Definitions  -->
                <DataGrid.Columns>
                    <DataGridTextColumn
                        Width="*"
                        MinWidth="20"
                        MaxWidth="45"
                        Binding="{Binding PointNumber}"
                        ElementStyle="{StaticResource DataGridTextCellStyle}"
                        Header="PN"
                        HeaderStyle="{StaticResource DataGridHeaderCellStyle}" />
                    <DataGridTextColumn
                        Width="*"
                        Binding="{Binding Easting}"
                        ElementStyle="{StaticResource DataGridTextCellStyle}"
                        Header="Easting"
                        HeaderStyle="{StaticResource DataGridHeaderCellStyle}" />
                    <DataGridTextColumn
                        Width="*"
                        Binding="{Binding Northing}"
                        ElementStyle="{StaticResource DataGridTextCellStyle}"
                        Header="Northing"
                        HeaderStyle="{StaticResource DataGridHeaderCellStyle}" />
                    <DataGridTextColumn
                        Width="*"
                        Binding="{Binding Elevation}"
                        ElementStyle="{StaticResource DataGridTextCellStyle}"
                        Header="Elevation"
                        HeaderStyle="{StaticResource DataGridHeaderCellStyle}" />
                    <DataGridTextColumn
                        Width="*"
                        Binding="{Binding Description}"
                        ElementStyle="{StaticResource DataGridTextCellStyle}"
                        Header="Description"
                        HeaderStyle="{StaticResource DataGridHeaderCellStyle}" />
                </DataGrid.Columns>

            </DataGrid>

        </Border>

        <!--#endregion-->


        <!--#region Footer (point counter, Add Table, Add Points to drawing, Formats Informations (Like SDR), and Export)-->
        <Grid
            Grid.Row="4"
            Width="380"
            Margin="0,5,0,5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="auto" />
            </Grid.ColumnDefinitions>

            <TextBlock
                x:Name="totalPointsCounter"
                Grid.Column="0"
                Width="126"
                Height="20"
                Margin="0,0,0,0"
                HorizontalAlignment="Left"
                VerticalAlignment="Center"
                FontFamily="{StaticResource PoppinsFont}"
                Foreground="#FF55657F">

                <Run Text="Total Points: " />
                <Run
                    x:Name="totalPointsValue"
                    FontWeight="SemiBold"
                    Foreground="#FF4F4F8C"
                    Text="{Binding PointCounter, FallbackValue=0}" />
            </TextBlock>

            <!--  SDR popup Button  -->
            <Button
                x:Name="SDRInfoButton"
                Grid.Column="1"
                Width="45"
                Height="20"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                Click="SDRInfoButton_Click"
                Cursor="Hand"
                Style="{StaticResource IconButtonStyle}"
                ToolTip="Export options and settings."
                Visibility="Collapsed">
                <Viewbox
                    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    Width="22"
                    Height="22"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Stretch="Uniform">
                    <Canvas Width="15" Height="10">


                        <Path Fill="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Button}}">
                            <Path.Data>
                                <PathGeometry Figures="M18.8 0H3.2A3.2 3.2 0 0 0 0 3.2v4.6A3.2 3.2 0 0 0 3.2 11h15.6A3.2 3.2 0 0 0 22 7.8V3.2A3.2 3.2 0 0 0 18.8 0M21 7.8a2.21 2.21 0 0 1-2.2 2.2H3.2A2.21 2.21 0 0 1 1 7.8V3.2A2.21 2.21 0 0 1 3.2 1h15.6A2.21 2.21 0 0 1 21 3.2Z" />
                            </Path.Data>
                        </Path>

                        <Path Fill="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Button}}">
                            <Path.Data>
                                <PathGeometry Figures="M6.7 2.47v1.71a1 1 0 0 0-.29-.06.5.5 0 0 0-.5.51 1 1 0 0 0 .16.45l.14.24a2.55 2.55 0 0 1 .37 1.24A2 2 0 0 1 6 8a2.14 2.14 0 0 1-1.52.61 2.1 2.1 0 0 1-.86-.2v-1.7a.9.9 0 0 0 .38.17.6.6 0 0 0 .39-.14.43.43 0 0 0 .15-.34 1.7 1.7 0 0 0-.25-.57 2.8 2.8 0 0 1-.4-1.41A2 2 0 0 1 4.54 3 1.94 1.94 0 0 1 6 2.35a2.2 2.2 0 0 1 .7.12m2.69 1.75V8.5H7.61v-6h2.32a3.77 3.77 0 0 1 2.4.69 2.75 2.75 0 0 1 1 2.26 3 3 0 0 1-.88 2.22 3.12 3.12 0 0 1-2.26.86h-.54V6.75H10c1 0 1.56-.43 1.56-1.3S11 4.22 10 4.22ZM16 4.11V8.5h-1.8v-6h2.29a2.34 2.34 0 0 1 1.62.5 1.87 1.87 0 0 1 .64 1.47A1.86 1.86 0 0 1 18 6l1 2.5h-1.89l-.86-1.94V5h.14c.37 0 .56-.16.56-.48s-.22-.43-.65-.43Z" />
                            </Path.Data>
                        </Path>

                    </Canvas>
                </Viewbox>
            </Button>

            <!--  SDR INFO Popup  -->
            <Popup
                x:Name="SDRPopup"
                AllowsTransparency="True"
                IsOpen="False"
                Placement="Top"
                PlacementTarget="{Binding ElementName=SDRInfoButton}"
                PopupAnimation="Fade"
                StaysOpen="False">
                <Border
                    Width="220"
                    Height="200"
                    Padding="12"
                    Background="#FFFFFF"
                    BorderBrush="#80BFB6EA"
                    BorderThickness="2"
                    CornerRadius="15">

                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <!--  Header  -->
                            <RowDefinition Height="*" />
                            <!--  Content  -->
                        </Grid.RowDefinitions>

                        <!--  🔹 Header with Title and Close Button  -->
                        <Grid Grid.Row="0" Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <!--  Title  -->
                            <TextBlock
                                Grid.Column="0"
                                VerticalAlignment="Center"
                                FontSize="16"
                                FontWeight="SemiBold"
                                Foreground="#FF55657F"
                                Text="SDR File Information" />

                            <!--  Close Button (X)  -->
                            <Button
                                x:Name="CloseSDRPopupButton"
                                Grid.Column="1"
                                Width="30"
                                Height="30"
                                Background="Transparent"
                                BorderBrush="{x:Null}"
                                Click="CloseSDRPopup_Click"
                                Cursor="Hand"
                                ToolTip="Close">
                                <Viewbox Width="24" Height="24">
                                    <Canvas Width="24" Height="24">
                                        <Path
                                            Stroke="#FF707079"
                                            StrokeLineJoin="round"
                                            StrokeThickness="2">
                                            <Path.Data>
                                                <PathGeometry Figures="M6 18 18 6M6 6l12 12" />
                                            </Path.Data>
                                        </Path>
                                    </Canvas>
                                </Viewbox>
                            </Button>

                            <!--  Separator  -->
                            <Separator
                                Grid.Row="1"
                                Grid.ColumnSpan="2"
                                Margin="0,30,0,0"
                                Background="#FFD3D3D3" />
                        </Grid>

                        <!--  🔹 Striped Table Layout  -->
                        <Border Grid.Row="1" CornerRadius="10">
                            <Grid Margin="5">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="1*" />
                                    <RowDefinition Height="1*" />
                                    <RowDefinition Height="1*" />
                                    <RowDefinition Height="1*" />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="90" />
                                    <!--  Label Column  -->
                                    <ColumnDefinition Width="*" />
                                    <!--  Value Column  -->
                                </Grid.ColumnDefinitions>

                                <!--  🔹 Job Name  -->
                                <Border
                                    Grid.Row="0"
                                    Grid.ColumnSpan="2"
                                    Background="#E8E8E8"
                                    BorderBrush="#BFBFBF"
                                    BorderThickness="1,1,1,0"
                                    CornerRadius="10,10,0,0">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock
                                            Grid.Column="0"
                                            Margin="5"
                                            FontWeight="Bold"
                                            Foreground="#FF6E7198"
                                            Text="Job Name:" />
                                        <TextBlock
                                            x:Name="txtJobName"
                                            Grid.Column="1"
                                            Margin="5"
                                            Foreground="#6A6A6A"
                                            Text="N/A"
                                            TextAlignment="Right" />
                                    </Grid>
                                </Border>

                                <!--  🔹 Instrument Model  -->
                                <Border
                                    Grid.Row="1"
                                    Grid.ColumnSpan="2"
                                    Background="#FFFFFF"
                                    BorderBrush="#BFBFBF"
                                    BorderThickness="1,1,1,0">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock
                                            Grid.Column="0"
                                            Margin="5"
                                            FontWeight="Bold"
                                            Foreground="#FF6E7198"
                                            Text="Instrument:" />
                                        <TextBlock
                                            x:Name="txtInstrumentModel"
                                            Grid.Column="1"
                                            Margin="5"
                                            Foreground="#6A6A6A"
                                            Text="N/A"
                                            TextAlignment="Right" />
                                    </Grid>
                                </Border>

                                <!--  🔹 Target Height  -->
                                <Border
                                    Grid.Row="2"
                                    Grid.ColumnSpan="2"
                                    Background="#E8E8E8"
                                    BorderBrush="#BFBFBF"
                                    BorderThickness="1,1,1,0">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock
                                            Grid.Column="0"
                                            Margin="5"
                                            FontWeight="Bold"
                                            Foreground="#FF6E7198"
                                            Text="Target Height:" />
                                        <TextBlock
                                            x:Name="txtTargetHeight"
                                            Grid.Column="1"
                                            Margin="5"
                                            Foreground="#6A6A6A"
                                            Text="N/A"
                                            TextAlignment="Right" />
                                    </Grid>
                                </Border>

                                <!--  🔹 Scale  -->
                                <Border
                                    Grid.Row="3"
                                    Grid.ColumnSpan="2"
                                    Background="#FFFFFF"
                                    BorderBrush="#BFBFBF"
                                    BorderThickness="1"
                                    CornerRadius="0,0,10,10">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <TextBlock
                                            Grid.Column="0"
                                            Margin="5"
                                            FontWeight="Bold"
                                            Foreground="#FF6E7198"
                                            Text="Scale:" />
                                        <TextBlock
                                            x:Name="txtScale"
                                            Grid.Column="1"
                                            Margin="5"
                                            Foreground="#6A6A6A"
                                            Text="N/A"
                                            TextAlignment="Right" />
                                    </Grid>
                                </Border>
                            </Grid>
                        </Border>
                    </Grid>
                </Border>

            </Popup>

            <!--  IDX popup Button  -->
            <Button
                x:Name="IDXInfoButton"
                Grid.Column="1"
                Width="45"
                Height="20"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                Click="IDXInfoButton_Click"
                Cursor="Hand"
                Style="{StaticResource IconButtonStyle}"
                ToolTip="Export options and settings."
                Visibility="Collapsed">
                <Viewbox
                    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    Width="22"
                    Height="22"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Stretch="Uniform">
                    <Canvas Width="14" Height="8">

                        <Path Fill="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Button}}">
                            <Path.Data>
                                <PathGeometry Figures="M18.8 0H3.2A3.2 3.2 0 0 0 0 3.2v4.6A3.2 3.2 0 0 0 3.2 11h15.6A3.2 3.2 0 0 0 22 7.8V3.2A3.2 3.2 0 0 0 18.8 0M21 7.8a2.21 2.21 0 0 1-2.2 2.2H3.2A2.21 2.21 0 0 1 1 7.8V3.2A2.21 2.21 0 0 1 3.2 1h15.6A2.21 2.21 0 0 1 21 3.2Z" />
                            </Path.Data>
                        </Path>

                        <Path Fill="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Button}}">
                            <Path.Data>
                                <PathGeometry Figures="M5.61 2.29v.3L5.58 5v3.29H2.92v-6Zm1.02 0h2.31a7.7 7.7 0 0 1 1.36.09 2.78 2.78 0 0 1 1.82 1.2 2.88 2.88 0 0 1 .55 1.71A2.94 2.94 0 0 1 12.11 7a2.86 2.86 0 0 1-1.77 1.2 4 4 0 0 1-.5.07h-3.2V5.39Zm2.68.28V8a6 6 0 0 0 .85 0 2.5 2.5 0 0 0 .59-.19A2.58 2.58 0 0 0 12 6.68a2.64 2.64 0 0 0 .39-1.4 2.7 2.7 0 0 0-.46-1.5 2.75 2.75 0 0 0-2.44-1.22Z" />
                            </Path.Data>
                        </Path>

                        <Path Fill="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Button}}">
                            <Path.Data>
                                <PathGeometry Figures="m17.23 4.76.09.15q.88 1.41 1.23 1.92c.23.34.57.83 1 1.46h-3.11l-1.52-2.48c-.47.57-1 1.28-1.65 2.15a2.5 2.5 0 0 1-.26.33.3.3 0 0 1-.18.08.13.13 0 0 1-.15-.15.3.3 0 0 1 0-.11s.1-.14.24-.31l.15-.19 1.65-2.07-2.08-3.25h3.09l1.32 2.2c.32-.38.77-1 1.34-1.77a3 3 0 0 1 .34-.43.21.21 0 0 1 .16-.09.17.17 0 0 1 .12 0 .14.14 0 0 1 .06.12.34.34 0 0 1-.08.18c0 .07-.21.28-.5.63Z" />
                            </Path.Data>
                        </Path>
                    </Canvas>

                </Viewbox>
            </Button>

            <!--  IDX INFO Popup  -->
            <Popup
                x:Name="IDXPopup"
                AllowsTransparency="True"
                IsOpen="False"
                Placement="Top"
                PlacementTarget="{Binding ElementName=IDXInfoButton}"
                PopupAnimation="Fade"
                StaysOpen="False">
                <Border
                    Width="220"
                    Height="200"
                    Padding="12"
                    Background="#FFFFFF"
                    BorderBrush="#80BFB6EA"
                    BorderThickness="2"
                    CornerRadius="15">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <!--  Header  -->
                            <RowDefinition Height="*" />
                            <!--  Content  -->
                        </Grid.RowDefinitions>

                        <!--  Header with Title and Close Button  -->
                        <Grid Grid.Row="0" Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <TextBlock
                                Grid.Column="0"
                                VerticalAlignment="Center"
                                FontSize="16"
                                FontWeight="SemiBold"
                                Foreground="#FF55657F"
                                Text="IDX File Information" />
                            <Button
                                x:Name="CloseIDXPopupButton"
                                Grid.Column="1"
                                Width="30"
                                Height="30"
                                Background="Transparent"
                                BorderBrush="{x:Null}"
                                Click="CloseIDXPopup_Click"
                                Cursor="Hand"
                                ToolTip="Close">
                                <Viewbox Width="24" Height="24">
                                    <Canvas Width="24" Height="24">
                                        <Path
                                            Stroke="#FF707079"
                                            StrokeLineJoin="round"
                                            StrokeThickness="2">
                                            <Path.Data>
                                                <PathGeometry Figures="M6 18 18 6M6 6l12 12" />
                                            </Path.Data>
                                        </Path>
                                    </Canvas>
                                </Viewbox>
                            </Button>
                        </Grid>

                        <!--  Content Section (using a table-like layout)  -->
                        <Grid Grid.Row="1" Margin="5">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="1*" />
                                <RowDefinition Height="1*" />
                                <RowDefinition Height="1*" />
                                <RowDefinition Height="1*" />
                                <RowDefinition Height="1*" />
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="5" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <!--  Instrument Model  -->
                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="0"
                                FontWeight="Bold"
                                Foreground="#3A3A3A"
                                Text="Instrument:" />
                            <TextBlock
                                x:Name="txtIDXInstrumentModel"
                                Grid.Row="0"
                                Grid.Column="2"
                                Foreground="#6A6A6A"
                                Text="N/A"
                                TextAlignment="Right" />

                            <!--  Linear Unit  -->
                            <TextBlock
                                Grid.Row="1"
                                Grid.Column="0"
                                FontWeight="Bold"
                                Foreground="#3A3A3A"
                                Text="Linear Unit:" />
                            <TextBlock
                                x:Name="txtIDXLinearUnit"
                                Grid.Row="1"
                                Grid.Column="2"
                                Foreground="#6A6A6A"
                                Text="N/A"
                                TextAlignment="Right" />

                            <!--  Project Name  -->
                            <TextBlock
                                Grid.Row="2"
                                Grid.Column="0"
                                FontWeight="Bold"
                                Foreground="#3A3A3A"
                                Text="Project:" />
                            <TextBlock
                                x:Name="txtIDXProjectName"
                                Grid.Row="2"
                                Grid.Column="2"
                                Foreground="#6A6A6A"
                                Text="N/A"
                                TextAlignment="Right" />

                            <!--  Operator  -->
                            <TextBlock
                                Grid.Row="3"
                                Grid.Column="0"
                                FontWeight="Bold"
                                Foreground="#3A3A3A"
                                Text="Operator:" />
                            <TextBlock
                                x:Name="txtIDXOperator"
                                Grid.Row="3"
                                Grid.Column="2"
                                Foreground="#6A6A6A"
                                Text="N/A"
                                TextAlignment="Right" />

                            <!--  Creation Date  -->
                            <TextBlock
                                Grid.Row="4"
                                Grid.Column="0"
                                FontWeight="Bold"
                                Foreground="#3A3A3A"
                                Text="Created:" />
                            <TextBlock
                                x:Name="txtIDXCreationDate"
                                Grid.Row="4"
                                Grid.Column="2"
                                Foreground="#6A6A6A"
                                Text="N/A"
                                TextAlignment="Right" />
                        </Grid>
                    </Grid>
                </Border>
            </Popup>

            <!--  Add Table to Drawing Button  -->
            <Button
                x:Name="AddTableToDrawingButton"
                Grid.Column="2"
                Width="25"
                Height="25"
                Margin="0,0,7,0"
                Click="AddTableToDrawingButton_Click"
                Cursor="Hand"
                Style="{StaticResource IconButtonStyle}"
                ToolTip="Add table to drawing."
                Visibility="{Binding IsSettingsVisible, RelativeSource={RelativeSource AncestorType=UserControl}}">
                <Viewbox
                    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    Width="25"
                    Height="25"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Stretch="Uniform">
                    <Canvas Width="24" Height="24">

                        <Path Fill="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Button}}">
                            <Path.Data>
                                <PathGeometry Figures="M18 14h2v3h3v2h-3v3h-2v-3h-3v-2h3zM4 3h14a2 2 0 0 1 2 2v7.08a6 6 0 0 0-4.32.92H12v4h1.08c-.11.68-.11 1.35 0 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2m0 4v4h6V7zm8 0v4h6V7zm-8 6v4h6v-4z" />
                            </Path.Data>
                        </Path>
                    </Canvas>
                </Viewbox>
            </Button>

            <!--  Add Points to Drawing Button  -->
            <Button
                x:Name="AddPointsToDrawingButton"
                Grid.Column="3"
                Width="25"
                Height="25"
                Margin="0,0,7,0"
                Click="AddPointsToDrawingButton_Click"
                Cursor="Hand"
                Style="{StaticResource IconButtonStyle}"
                ToolTip="Add points to drawing.">
                <Viewbox
                    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    Width="25"
                    Height="25"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Stretch="Uniform">
                    <Canvas Width="24" Height="24">

                        <Canvas>
                            <Path
                                Stroke="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Button}}"
                                StrokeEndLineCap="Round"
                                StrokeLineJoin="round"
                                StrokeStartLineCap="Round"
                                StrokeThickness="2">
                                <Path.Data>
                                    <PathGeometry Figures="M9 11a3 3 0 1 0 6 0a3 3 0 0 0-6 0" />
                                </Path.Data>
                            </Path>

                            <Path
                                Stroke="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Button}}"
                                StrokeEndLineCap="Round"
                                StrokeLineJoin="round"
                                StrokeStartLineCap="Round"
                                StrokeThickness="2">
                                <Path.Data>
                                    <PathGeometry Figures="M12.736 21.345a2 2 0 0 1-2.149-.445l-4.244-4.243a8 8 0 1 1 13.59-4.624M19 16v6m3-3l-3 3l-3-3" />
                                </Path.Data>
                            </Path>
                        </Canvas>
                    </Canvas>
                </Viewbox>
            </Button>


            <!--  Export Options Button  -->
            <Button
                x:Name="ExportOptionsButton"
                Grid.Column="4"
                Width="25"
                Height="25"
                Click="ExportOptionsButton_Click"
                Cursor="Hand"
                Style="{StaticResource IconButtonStyle}"
                ToolTip="Export options and settings.">
                <Viewbox
                    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    Width="25"
                    Height="25"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Stretch="Uniform">
                    <Canvas Width="24" Height="24">

                        <Canvas>
                            <Path>
                                <Path.Data>
                                    <PathGeometry Figures="m12.593 23.258l-.011.002l-.071.035l-.02.004l-.014-.004l-.071-.035q-.016-.005-.024.005l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427q-.004-.016-.017-.018m.265-.113l-.013.002l-.185.093l-.01.01l-.003.011l.018.43l.005.012l.008.007l.201.093q.019.005.029-.008l.004-.014l-.034-.614q-.005-.018-.02-.022m-.715.002a.02.02 0 0 0-.027.006l-.006.014l-.034.614q.001.018.017.024l.015-.002l.201-.093l.01-.008l.004-.011l.017-.43l-.003-.012l-.01-.01z" />
                                </Path.Data>
                            </Path>

                            <Path Fill="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Button}}">
                                <Path.Data>
                                    <PathGeometry Figures="M13.586 2a2 2 0 0 1 1.284.467l.13.119L19.414 7a2 2 0 0 1 .578 1.238l.008.176V12h-2v-2h-4.5a1.5 1.5 0 0 1-1.493-1.356L12 8.5V4H6v16h6v2H6a2 2 0 0 1-1.995-1.85L4 20V4a2 2 0 0 1 1.85-1.995L6 2zm5.121 12.465l2.828 2.828a1 1 0 0 1 0 1.414l-2.828 2.828a1 1 0 1 1-1.414-1.414L18.414 19H14a1 1 0 1 1 0-2h4.414l-1.121-1.121a1 1 0 0 1 1.414-1.415ZM14 4.414V8h3.586z" />
                                </Path.Data>
                            </Path>
                        </Canvas>
                    </Canvas>
                </Viewbox>
            </Button>

            <!--  Export Dialog Popup  -->
            <Popup
                x:Name="ExportDialogPopup"
                AllowsTransparency="True"
                IsOpen="False"
                Placement="Top"
                PlacementTarget="{Binding ElementName=ExportOptionsButton}"
                PopupAnimation="Fade"
                StaysOpen="False">

                <Border
                    Width="285"
                    Height="Auto"
                    Padding="10"
                    Background="#FFFFFF"
                    BorderBrush="#80BFB6EA"
                    BorderThickness="2"
                    CornerRadius="15">
                    <Grid>
                        <Grid.RowDefinitions>
                            <!--  Header  -->
                            <RowDefinition Height="Auto" />
                            <!--  Content  -->
                            <RowDefinition Height="Auto" />
                            <!--  footer export button  -->
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <!--  Header with Title and Close Button  -->
                        <Grid Grid.Row="0" Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>

                            <!--  Title  -->
                            <TextBlock
                                Grid.Column="0"
                                VerticalAlignment="Center"
                                FontFamily="{StaticResource PoppinsFont}"
                                FontSize="13"
                                FontWeight="SemiBold"
                                Foreground="#FF55657F"
                                Text="Export Points" />

                            <!--  Close Button  -->
                            <Button
                                x:Name="CloseExportPopupButton"
                                Grid.Column="1"
                                Width="30"
                                Height="30"
                                Background="Transparent"
                                BorderBrush="{x:Null}"
                                Click="CloseExportPopupButton_Click"
                                Cursor="Hand"
                                ToolTip="Close">
                                <Viewbox
                                    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                                    Width="24"
                                    Height="24"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Stretch="Uniform">
                                    <Canvas Width="24" Height="24">
                                        <Path
                                            Stroke="#FF707079"
                                            StrokeLineJoin="round"
                                            StrokeThickness="2">
                                            <Path.Data>
                                                <PathGeometry Figures="M6 18 18 6M6 6l12 12" />
                                            </Path.Data>
                                        </Path>
                                    </Canvas>
                                </Viewbox>
                            </Button>

                            <!--  Separator below  -->
                            <Separator
                                Grid.Row="1"
                                Grid.ColumnSpan="2"
                                Margin="0,5,0,0"
                                Background="#FFD3D3D3" />
                        </Grid>

                        <!--  Content  -->
                        <Grid Grid.Row="1">
                            <Grid.RowDefinitions>
                                <!--  Select File Format  -->
                                <RowDefinition Height="Auto" />
                                <!--  Export Settings  -->
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>

                            <GroupBox
                                Grid.Row="0"
                                Padding="5,10,10,5"
                                FontFamily="{StaticResource PoppinsFont}"
                                Foreground="#FF31516D"
                                Header="Select Export format">
                                <ComboBox
                                    x:Name="ExportFileFormatComboBox"
                                    Width="205"
                                    Height="27"
                                    Margin="0,0,0,0"
                                    FontFamily="{StaticResource PoppinsFont}"
                                    SelectionChanged="ExportFileFormatComboBox_SelectionChanged"
                                    Style="{StaticResource RoundedComboBox}">

                                    <ComboBoxItem
                                        Foreground="Bisque"
                                        IsEnabled="False"
                                        IsSelected="True">
                                        Select points format...
                                    </ComboBoxItem>
                                    <ComboBoxItem Content="CSV (Comma delimited) (*.csv)" Tag=".csv" />
                                    <ComboBoxItem Content="Text (Tab delimited) (*.txt)" Tag=".txt" />
                                    <ComboBoxItem Content="KML (*.kml)" Tag=".kml" />
                                    <ComboBoxItem Content="SDR33 (Sokkia) (*.sdr)" Tag=".sdr" />
                                    <ComboBoxItem Content="IDX (Leica) (*.idx)" Tag=".idx" />
                                    <ComboBoxItem Content="GSI (Leica) (*.gsi)" Tag=".gsi" />
                                </ComboBox>
                            </GroupBox>

                            <GroupBox
                                Grid.Row="1"
                                MinHeight="30"
                                Margin="0,5,0,0"
                                Padding="5,10,10,5"
                                FontFamily="{StaticResource PoppinsFont}"
                                Foreground="#FF31516D"
                                Header="Settings">
                                <Grid>
                                    <!--  Empty Settings Panel  -->
                                    <Grid
                                        x:Name="ExEmptySettings"
                                        MinHeight="40"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Top"
                                        ToolTip="No file selected"
                                        Visibility="Visible">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>

                                        <ContentControl
                                            Grid.Column="0"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Top"
                                            Content="{StaticResource EmptySettingsIcon}" />
                                    </Grid>

                                    <!--  CSV/TXT Settings Panel  -->
                                    <Grid x:Name="ExCSVSettings" Visibility="Collapsed">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>

                                        <CheckBox
                                            x:Name="PointsHaveNumberCheckBox"
                                            Grid.Column="0"
                                            Margin="0,0,10,0"
                                            Padding="4,0,4,0"
                                            HorizontalContentAlignment="Center"
                                            VerticalContentAlignment="Center"
                                            Background="#FFCDD7E8"
                                            BorderBrush="{x:Null}"
                                            Content="PN"
                                            FontFamily="{StaticResource PoppinsFont}"
                                            Foreground="#FF687A99"
                                            IsChecked="True" />

                                        <CheckBox
                                            x:Name="XYRadioButton"
                                            Grid.Column="1"
                                            Margin="0,0,10,0"
                                            Padding="4,0,0,0"
                                            VerticalContentAlignment="Center"
                                            Background="#FFCDD7E8"
                                            BorderBrush="{x:Null}"
                                            Content="X, Y Order"
                                            FontFamily="{StaticResource PoppinsFont}"
                                            Foreground="#FF687A99"
                                            IsChecked="True" />

                                        <CheckBox
                                            x:Name="FileHasHeadersCheckBox"
                                            Grid.Column="2"
                                            VerticalContentAlignment="Center"
                                            Background="#FFCDD7E8"
                                            BorderBrush="{x:Null}"
                                            Content="Has Headers"
                                            FontFamily="{StaticResource PoppinsFont}"
                                            Foreground="#FF687A99"
                                            IsChecked="False" />
                                    </Grid>

                                    <!--  GSI Settings Panel  -->
                                    <Grid
                                        x:Name="ExGSISettings"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Visibility="Collapsed">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>

                                        <TextBlock
                                            Grid.Column="0"
                                            Margin="0,0,10,0"
                                            VerticalAlignment="Center"
                                            FontFamily="{StaticResource PoppinsFont}"
                                            FontSize="13.5"
                                            Foreground="#FF687A99"
                                            Text="GSI variant" />

                                        <ComboBox
                                            x:Name="GSIFormatCB"
                                            Grid.Column="1"
                                            Width="75"
                                            Height="30"
                                            VerticalAlignment="Center"
                                            FontFamily="{StaticResource PoppinsFont}"
                                            FontSize="13"
                                            Style="{StaticResource RoundedComboBox}">
                                            <ComboBoxItem Content="GSI 8" />
                                            <ComboBoxItem Content="GSI 16" IsSelected="True" />
                                        </ComboBox>
                                    </Grid>

                                    <!--  KML Settings Panel  -->
                                    <Grid
                                        x:Name="ExKMLSettings"
                                        MaxWidth="250"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Visibility="Collapsed">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <!--  Hemisphere label  -->
                                            <ColumnDefinition Width="Auto" />
                                            <!--  Hemisphere ComboBox  -->
                                            <ColumnDefinition Width="Auto" />
                                            <!--  Zone label  -->
                                            <ColumnDefinition Width="Auto" />
                                            <!--  Zone ComboBox  -->
                                        </Grid.ColumnDefinitions>

                                        <!--  Hemisphere Label  -->
                                        <TextBlock
                                            Grid.Column="0"
                                            Margin="0,0,5,0"
                                            VerticalAlignment="Center"
                                            FontFamily="{StaticResource PoppinsFont}"
                                            FontSize="12"
                                            Foreground="#FF687A99"
                                            Text="Hemisphere:" />

                                        <!--  Hemisphere ComboBox  -->
                                        <ComboBox
                                            x:Name="HemisphereCB"
                                            Grid.Column="1"
                                            Width="Auto"
                                            MinWidth="60"
                                            Margin="0,0,10,0"
                                            VerticalContentAlignment="Center"
                                            FontFamily="{StaticResource PoppinsFont}"
                                            Style="{StaticResource ResourceKey=RoundedComboBox}">
                                            <ComboBoxItem Content="North" IsSelected="True" />
                                            <ComboBoxItem Content="South" />
                                        </ComboBox>

                                        <!--  Zone Label  -->
                                        <TextBlock
                                            Grid.Column="2"
                                            Margin="0,0,5,0"
                                            VerticalAlignment="Center"
                                            FontFamily="{StaticResource PoppinsFont}"
                                            FontSize="12"
                                            Foreground="#FF687A99"
                                            Text="Zone:" />

                                        <!--  Zone ComboBox  -->
                                        <ComboBox
                                            x:Name="ZoneCB"
                                            Grid.Column="3"
                                            Width="Auto"
                                            MinWidth="39"
                                            MinHeight="25"
                                            HorizontalContentAlignment="Center"
                                            VerticalContentAlignment="Center"
                                            FontFamily="{StaticResource PoppinsFont}"
                                            SelectedIndex="36"
                                            Style="{StaticResource ResourceKey=RoundedComboBox}">
                                            <!--  UTM Zone numbers 1-60  -->
                                            <ComboBoxItem Content="1" />
                                            <ComboBoxItem Content="2" />
                                            <ComboBoxItem Content="3" />
                                            <ComboBoxItem Content="4" />
                                            <ComboBoxItem Content="5" />
                                            <ComboBoxItem Content="6" />
                                            <ComboBoxItem Content="7" />
                                            <ComboBoxItem Content="8" />
                                            <ComboBoxItem Content="9" />
                                            <ComboBoxItem Content="10" />
                                            <ComboBoxItem Content="11" />
                                            <ComboBoxItem Content="12" />
                                            <ComboBoxItem Content="13" />
                                            <ComboBoxItem Content="14" />
                                            <ComboBoxItem Content="15" />
                                            <ComboBoxItem Content="16" />
                                            <ComboBoxItem Content="17" />
                                            <ComboBoxItem Content="18" />
                                            <ComboBoxItem Content="19" />
                                            <ComboBoxItem Content="20" />
                                            <ComboBoxItem Content="21" />
                                            <ComboBoxItem Content="22" />
                                            <ComboBoxItem Content="23" />
                                            <ComboBoxItem Content="24" />
                                            <ComboBoxItem Content="25" />
                                            <ComboBoxItem Content="26" />
                                            <ComboBoxItem Content="27" />
                                            <ComboBoxItem Content="28" />
                                            <ComboBoxItem Content="29" />
                                            <ComboBoxItem Content="30" />
                                            <ComboBoxItem Content="31" />
                                            <ComboBoxItem Content="32" />
                                            <ComboBoxItem Content="33" />
                                            <ComboBoxItem Content="34" />
                                            <ComboBoxItem Content="35" />
                                            <ComboBoxItem Content="36" />
                                            <ComboBoxItem Content="37" IsSelected="True" />
                                            <ComboBoxItem Content="38" />
                                            <ComboBoxItem Content="39" />
                                            <ComboBoxItem Content="40" />
                                            <ComboBoxItem Content="41" />
                                            <ComboBoxItem Content="42" />
                                            <ComboBoxItem Content="43" />
                                            <ComboBoxItem Content="44" />
                                            <ComboBoxItem Content="45" />
                                            <ComboBoxItem Content="46" />
                                            <ComboBoxItem Content="47" />
                                            <ComboBoxItem Content="48" />
                                            <ComboBoxItem Content="49" />
                                            <ComboBoxItem Content="50" />
                                            <ComboBoxItem Content="51" />
                                            <ComboBoxItem Content="52" />
                                            <ComboBoxItem Content="53" />
                                            <ComboBoxItem Content="54" />
                                            <ComboBoxItem Content="55" />
                                            <ComboBoxItem Content="56" />
                                            <ComboBoxItem Content="57" />
                                            <ComboBoxItem Content="58" />
                                            <ComboBoxItem Content="59" />
                                            <ComboBoxItem Content="60" />
                                        </ComboBox>
                                    </Grid>

                                </Grid>
                            </GroupBox>

                        </Grid>

                        <!--  Footer with Export Button  -->
                        <Button
                            x:Name="ExportButton"
                            Grid.Row="2"
                            Width="85"
                            Height="30"
                            Margin="0,5,0,0"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Background="#FFD9D9D9"
                            BorderBrush="{x:Null}"
                            Click="ExportButton_Click"
                            Cursor="Hand"
                            Foreground="#FF55657F"
                            ToolTip="Export points list according to selected format and settings">
                            <Button.Template>
                                <ControlTemplate TargetType="{x:Type Button}">
                                    <Border
                                        x:Name="Border"
                                        Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        CornerRadius="5">
                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter TargetName="Border" Property="Background" Value="#FFB5B5B5" />
                                        </Trigger>
                                        <Trigger Property="IsPressed" Value="True">
                                            <Setter TargetName="Border" Property="Background" Value="#FF9B9B9B" />
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Button.Template>
                            <StackPanel
                                Margin="-3,0,0,0"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Center"
                                Orientation="Horizontal">
                                <Viewbox
                                    Width="18"
                                    Height="18"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Top"
                                    Stretch="Uniform">
                                    <Canvas Width="16" Height="16">
                                        <Path Data="M9.85903,5.01999C9.90547,5.06912 9.96147,5.10852 10.0237,5.13585 10.0859,5.16317 10.1531,5.17787 10.2212,5.17905 10.2893,5.18024 10.3569,5.16789 10.4201,5.14274 10.4833,5.11759 10.5406,5.08016 10.5888,5.03268 10.637,4.98521 10.675,4.92865 10.7005,4.86639 10.726,4.80413 10.7385,4.73745 10.7373,4.67031 10.7361,4.60318 10.7212,4.53697 10.6935,4.47563 10.6657,4.4143 10.6258,4.3591 10.5759,4.31333L8.54695,2.31333C8.45184,2.21969 8.32292,2.1671 8.1885,2.1671 8.05408,2.1671 7.92515,2.21969 7.83004,2.31333L5.80106,4.31333C5.71146,4.40811 5.66268,4.53347 5.665,4.66301 5.66731,4.79254 5.72055,4.91613 5.81349,5.00774 5.90642,5.09935 6.0318,5.15182 6.16322,5.15411 6.29463,5.15639 6.42181,5.10831 6.51796,5.01999L7.68125,3.87333 7.68125,9.33333C7.68125,9.46593 7.73469,9.59311 7.82982,9.68688 7.92495,9.78065 8.05397,9.83333 8.1885,9.83333 8.32303,9.83333 8.45205,9.78065 8.54717,9.68688 8.6423,9.59311 8.69574,9.46593 8.69574,9.33333L8.69574,3.87333 9.85903,5.01999z" Fill="#FF687A99" />
                                        <Path Data="M14.1064 8C14.1064 7.86739 14.0529 7.74022 13.9578 7.64645C13.8627 7.55268 13.7337 7.5 13.5991 7.5C13.4646 7.5 13.3356 7.55268 13.2405 7.64645C13.1453 7.74022 13.0919 7.86739 13.0919 8C13.0919 8.63472 12.9651 9.26323 12.7186 9.84964C12.4722 10.436 12.111 10.9689 11.6557 11.4177C11.2004 11.8665 10.6599 12.2225 10.0649 12.4654C9.47004 12.7083 8.83243 12.8333 8.1885 12.8333C7.54458 12.8333 6.90697 12.7083 6.31206 12.4654C5.71716 12.2225 5.17661 11.8665 4.72129 11.4177C4.26597 10.9689 3.90479 10.436 3.65837 9.84964C3.41195 9.26323 3.28512 8.63472 3.28512 8C3.28512 7.86739 3.23168 7.74022 3.13655 7.64645C3.04143 7.55268 2.91241 7.5 2.77788 7.5C2.64335 7.5 2.51433 7.55268 2.4192 7.64645C2.32407 7.74022 2.27063 7.86739 2.27063 8C2.27063 9.5471 2.89412 11.0308 4.00394 12.1248C5.11375 13.2188 6.61899 13.8333 8.1885 13.8333C9.75802 13.8333 11.2633 13.2188 12.3731 12.1248C13.4829 11.0308 14.1064 9.5471 14.1064 8Z" Fill="#FF687A99" />
                                    </Canvas>
                                </Viewbox>
                                <TextBlock
                                    Margin="2,0,0,0"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    FontFamily="{StaticResource PoppinsFont}"
                                    FontSize="11"
                                    Text="Export" />
                            </StackPanel>
                        </Button>
                    </Grid>
                </Border>

            </Popup>

        </Grid>
        <!--#endregion-->


    </Grid>

</UserControl>
