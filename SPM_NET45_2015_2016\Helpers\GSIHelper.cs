﻿using System.Globalization;
using System.Linq;

namespace SPM_NET45_2015_2016.Helpers
{
    public static class GSIHelper
    {
        /// <summary>
        /// For GSI 16: Extracts the numeric part after the first '+' and divides by 1000.
        /// </summary>
        public static double ParseGsi16Coordinate(string word)
        {
            int plusIndex = word.IndexOf('+');
            if (plusIndex < 0)
            {
                return 0;
            }
            string numericPart = word.Substring(plusIndex + 1).Trim();
            if (double.TryParse(numericPart, NumberStyles.Any, CultureInfo.InvariantCulture, out double value))
            {
                return value / 1000.0;
            }
            return 0;
        }

        /// <summary>
        /// Splits the input string on '+' and returns the second part with leading zeros removed.
        /// If the resulting string is empty, returns "0".
        /// </summary>
        public static string ExtractNumericPart(string wordBlock)
        {
            if (string.IsNullOrWhiteSpace(wordBlock))
            {
                return string.Empty;
            }
            string[] parts = wordBlock.Split('+');
            if (parts.Length < 2)
            {
                return wordBlock; // Fallback if no '+' found.
            }
            string number = parts[1].TrimStart('0');
            if (string.IsNullOrEmpty(number) || (number.All(char.IsDigit) && number.All(c => c == '0')))
            {
                number = "0";
            }
            return number;
        }
    }

}
