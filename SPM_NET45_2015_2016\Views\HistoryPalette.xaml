﻿<UserControl
    x:Class="SPM_NET45_2015_2016.Views.HistoryPalette"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:SPM_NET45_2015_2016.Views"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:partialViews="clr-namespace:SPM_NET45_2015_2016.PartialViews"
    Width="400"
    Height="600"
    MinWidth="400"
    MinHeight="600"
    mc:Ignorable="d">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/SPM_NET45_2015_2016;component/ResourceDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid Background="#FFECECEC">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>


        <!--#region Header-->
        <partialViews:Header
            Grid.Row="0"
            HorizontalAlignment="Stretch"
            VerticalAlignment="Top"
            BreadcrumbText="HISTORY" />
        <!--#endregion-->

        <!--#region Controls-->
        <Grid Grid.Row="1" Margin="5,15,5,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <!--  Search  -->
            <StackPanel
                Grid.Column="0"
                HorizontalAlignment="Left"
                Orientation="Horizontal">
                <!--  Search TextBox  -->
                <TextBox
                    x:Name="SearchTextBox"
                    Width="120"
                    Height="30"
                    MaxWidth="120"
                    Margin="5,0,0,0"
                    Padding="7,2,2,2"
                    VerticalContentAlignment="Center"
                    Background="#FFD9D9D9"
                    Foreground="#FF687A99"
                    GotFocus="SearchTextBox_GotFocus"
                    LostFocus="SearchTextBox_LostFocus"
                    Text="Search...">
                    <TextBox.Style>
                        <Style TargetType="TextBox">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="TextBox">
                                        <Border
                                            Padding="5"
                                            Background="{TemplateBinding Background}"
                                            CornerRadius="15,0,0,15">
                                            <ScrollViewer x:Name="PART_ContentHost" />
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </TextBox.Style>
                </TextBox>

                <!--  Search Button  -->
                <Button
                    x:Name="SearchButton"
                    Width="30"
                    Height="30"
                    MaxWidth="30"
                    Background="#FFD9D9D9"
                    Click="SearchButton_Click"
                    Cursor="Hand"
                    Foreground="#FF687A99">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border
                                            Padding="5"
                                            Background="{TemplateBinding Background}"
                                            CornerRadius="0,15,15,0">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                    <!--  Search Icon (Text or Unicode)  -->
                    <TextBlock FontSize="14" Text="🔍" />
                </Button>
            </StackPanel>

            <!--  records counter  -->
            <Border
                Grid.Column="1"
                Width="65"
                Height="25"
                Margin="11,0,5,0"
                Padding="5"
                VerticalAlignment="Center"
                Background="#FFD9D9D9"
                BorderBrush="#FFB5B5B5"
                BorderThickness="1"
                CornerRadius="5">
                <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">


                    <Viewbox
                        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                        Width="15"
                        Height="15"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Stretch="Uniform">
                        <Canvas Width="23" Height="23">


                            <Path
                                Stroke="#FF55657F"
                                StrokeEndLineCap="Round"
                                StrokeLineJoin="round"
                                StrokeStartLineCap="Round">
                                <Path.Data>
                                    <PathGeometry Figures="M8.593 3.217H4.698A1.95 1.95 0 0 0 2.75 5.164v13.633c0 1.075.872 1.947 1.948 1.947h3.895a1.95 1.95 0 0 0 1.947-1.947V5.164a1.95 1.95 0 0 0-1.947-1.947" />
                                </Path.Data>
                            </Path>

                            <Path
                                Stroke="#FF55657F"
                                StrokeEndLineCap="Round"
                                StrokeLineJoin="round"
                                StrokeStartLineCap="Round">
                                <Path.Data>
                                    <PathGeometry Figures="M6.645 17.379a1.503 1.503 0 1 0 0-3.007a1.503 1.503 0 0 0 0 3.007M10.54 7.93l3.116 11.685a1.95 1.95 0 0 0 2.386 1.373l3.768-.974a1.947 1.947 0 0 0 1.373-2.386L17.658 4.385a1.947 1.947 0 0 0-2.386-1.373l-3.758 1.003c-.406.111-.764.35-1.023.682" />
                                </Path.Data>
                            </Path>

                            <Path
                                Stroke="#FF55657F"
                                StrokeEndLineCap="Round"
                                StrokeLineJoin="round"
                                StrokeStartLineCap="Round">
                                <Path.Data>
                                    <PathGeometry Figures="M16.665 17.241a1.502 1.502 0 1 0 0-3.004a1.502 1.502 0 0 0 0 3.004" />
                                </Path.Data>
                            </Path>

                        </Canvas>
                    </Viewbox>

                    <TextBlock
                        Width="30"
                        Height="Auto"
                        Margin="6,0,0,0"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        FontFamily="{StaticResource PoppinsFont}"
                        FontSize="10"
                        Foreground="#FF687A99"
                        Text="{Binding HistoryRecordCounter, FallbackValue=0}" />
                </StackPanel>
            </Border>

            <!--  Undo clear  -->
            <Button
                x:Name="UndoClearHistoryRecordsDataGridButton"
                Grid.Column="2"
                Width="68"
                Height="30"
                Margin="7,0,10,0"
                HorizontalAlignment="Right"
                VerticalAlignment="Center"
                Background="#FFD9D9D9"
                BorderBrush="{x:Null}"
                Click="UndoClearHistoryRecordsDataGridButton_Click"
                Cursor="Hand"
                Foreground="#FF55657F">
                <Button.Template>
                    <ControlTemplate TargetType="{x:Type Button}">
                        <Border
                            x:Name="Border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="#FFB5B5B5" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="#FF9B9B9B" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Button.Template>

                <StackPanel
                    Margin="-2,0,0,0"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center"
                    Orientation="Horizontal">
                    <Viewbox
                        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                        Width="22"
                        Height="22"
                        Margin="0,0,1,0"
                        HorizontalAlignment="Left"
                        Stretch="Uniform">
                        <Canvas Width="22" Height="22">
                            <Path Data="M7.404 18v-1h7.254q1.556 0 2.65-1.067q1.096-1.067 1.096-2.606t-1.095-2.596q-1.096-1.058-2.651-1.058H6.916l2.965 2.965l-.708.708L5 9.173L9.173 5l.708.708l-2.965 2.965h7.742q1.963 0 3.355 1.354q1.39 1.354 1.39 3.3t-1.39 3.31T14.657 18z" Fill="#FF687A99" />
                        </Canvas>
                    </Viewbox>
                    <TextBlock
                        Margin="2,0,3,0"
                        VerticalAlignment="Center"
                        FontFamily="{StaticResource PoppinsFont}"
                        FontSize="11"
                        Text="Undo" />
                </StackPanel>
            </Button>

            <!--  clear history  -->
            <Button
                x:Name="ClearHistoryRecordsDataGridButton"
                Grid.Column="3"
                Width="64"
                Height="30"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                Background="#FFD9D9D9"
                BorderBrush="{x:Null}"
                Click="ClearHistoryRecordsDataGridButton_Click"
                Cursor="Hand"
                Foreground="#FF55657F">
                <Button.Template>
                    <ControlTemplate TargetType="{x:Type Button}">
                        <Border
                            x:Name="Border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="#FFB5B5B5" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="#FF9B9B9B" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Button.Template>

                <StackPanel
                    Margin="-2,0,0,0"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center"
                    Orientation="Horizontal">
                    <Viewbox
                        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                        Width="18"
                        Height="18"
                        HorizontalAlignment="Center"
                        VerticalAlignment="top"
                        Stretch="Uniform">
                        <Canvas Width="16" Height="16">
                            <Path Data="M12,12.6667C12,13.1971 11.7893,13.7058 11.4142,14.0809 11.0392,14.456 10.5305,14.6667 10,14.6667L5.33335,14.6667C4.80292,14.6667 4.29421,14.456 3.91914,14.0809 3.54407,13.7058 3.33335,13.1971 3.33335,12.6667L3.33335,4.66667 2.66669,4.66667 2.66669,2.66667 5.66669,2.66667 6.33335,2 9.00002,2 9.66669,2.66667 12.6667,2.66667 12.6667,4.66667 12,4.66667 12,12.6667z M4.00002,4.66667L4.00002,12.6667C4.00002,13.0203 4.1405,13.3594 4.39054,13.6095 4.64059,13.8595 4.97973,14 5.33335,14L10,14C10.3536,14 10.6928,13.8595 10.9428,13.6095 11.1929,13.3594 11.3334,13.0203 11.3334,12.6667L11.3334,4.66667 4.00002,4.66667z M12,4L12,3.33333 9.33335,3.33333 8.66669,2.66667 6.66669,2.66667 6.00002,3.33333 3.33335,3.33333 3.33335,4 12,4z M5.33335,6L6.00002,6 6.00002,12.6667 5.33335,12.6667 5.33335,6z M9.33335,6L10,6 10,12.6667 9.33335,12.6667 9.33335,6z" Fill="#FF687A99" />
                        </Canvas>
                    </Viewbox>
                    <TextBlock
                        Margin="2,0,0,0"
                        VerticalAlignment="Center"
                        FontFamily="{StaticResource PoppinsFont}"
                        FontSize="11"
                        Text="Clear" />
                </StackPanel>
            </Button>

        </Grid>
        <!--#endregion-->


        <!--#region ListView for history records-->
        <ListView
            x:Name="HistoryListView"
            Grid.Row="2"
            MaxWidth="380"
            Margin="7"
            AlternationCount="2"
            ItemContainerStyle="{StaticResource ListViewItemStyle}"
            ItemsSource="{Binding HistoryRecords}"
            ScrollViewer.HorizontalScrollBarVisibility="Disabled"
            Style="{StaticResource ModernListView}">
            <ListView.View>
                <GridView ColumnHeaderContainerStyle="{StaticResource GridViewColumnHeaderStyle}">
                    <GridViewColumn Width="120" Header="File Name">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    FontWeight="Medium"
                                    Style="{StaticResource CellTextBlockStyle}"
                                    Text="{Binding FileName}" />
                                <!--  font-medium  -->
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="65" Header="Action">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Style="{StaticResource CellTextBlockStyle}" Text="{Binding OperationType}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="60" Header="Format">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Style="{StaticResource CellTextBlockStyle}" Text="{Binding FileType}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="54" Header="Points">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Style="{StaticResource CellTextBlockStyle}" Text="{Binding PointCount}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Width="88" Header="Date">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock
                                    FontSize="11"
                                    Style="{StaticResource CellTextBlockStyle}"
                                    Text="{Binding DateTime}" />
                                <!--  Smaller font size for date  -->
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                </GridView>
            </ListView.View>
        </ListView>
        <!--#endregion-->


        <!--#region Info-->
        <TextBlock
            Grid.Row="3"
            Width="369"
            Height="50"
            Margin="0,10,0,10"
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            FontFamily="{StaticResource PoppinsFont}"
            FontSize="11.5"
            Foreground="#FF687A99"
            TextWrapping="Wrap">
            <Run FontWeight="Bold" Text="History Overview:" />
            <Run Text=" Tracks file name, type, format, date, and point count for up to 5,000 records. Automatically deletes oldest entries. Disable recording in settings." />
        </TextBlock>
        <!--#endregion-->

    </Grid>

</UserControl>
