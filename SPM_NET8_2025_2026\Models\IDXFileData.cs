﻿using System.Collections.ObjectModel;

namespace SPM_NET8_2025_2026.Models
{
    public class IDXFileInfo
    {
        public string InstrumentModel { get; set; }
        public string LinearUnit { get; set; }
        public string ProjectName { get; set; }
        public string Operator { get; set; }
        public string CreationDate { get; set; }
    }

    public class IDXFileData
    {
        public ObservableCollection<SurveyPoint> SurveyPoints { get; set; } = new ObservableCollection<SurveyPoint>();
        public IDXFileInfo FileInfo { get; set; } = new IDXFileInfo();
    }
}

