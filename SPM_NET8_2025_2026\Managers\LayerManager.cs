﻿using Autodesk.AutoCAD.DatabaseServices;

namespace SPM_NET8_2025_2026.Managers
{
    public static class LayerManager
    {
        /// <summary>
        /// Creates a layer with the specified name and color if it doesn't already exist.
        /// </summary>
        public static void CreateLayer(Database db, Transaction tr, string layerName, short colorIndex)
        {
            var layerTable = (LayerTable)tr.GetObject(db.LayerTableId, OpenMode.ForRead);
            if (!layerTable.Has(layerName))
            {
                layerTable.UpgradeOpen();
                var layer = new LayerTableRecord
                {
                    Name = layerName,
                    Color = Autodesk.AutoCAD.Colors.Color.FromColorIndex(Autodesk.AutoCAD.Colors.ColorMethod.ByAci, colorIndex)
                };
                layerTable.Add(layer);
                tr.AddNewlyCreatedDBObject(layer, true);
            }
        }
    }
}
