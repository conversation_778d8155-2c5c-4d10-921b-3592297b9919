﻿using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;

namespace SPM_NET45_2015_2016.Services
{
    public static class DrawingService
    {
        #region AutoCAD Properties
        /// <summary>
        /// Gets the currently active AutoCAD document.
        /// </summary>
        private static Document ActiveDocument => Application.DocumentManager.MdiActiveDocument;

        /// <summary>
        /// Gets the database of the currently active AutoCAD document.
        /// </summary>
        private static Database ActiveDatabase => ActiveDocument?.Database;

        /// <summary>
        /// Gets the editor of the currently active AutoCAD document.
        /// </summary>
        private static Editor ActiveEditor => ActiveDocument?.Editor;
        #endregion


        /// <summary>
        /// Draws a DBPoint in AutoCAD on the specified layer.
        /// </summary>
        public static void DrawPoint(Autodesk.AutoCAD.ApplicationServices.Document doc, Point3d point, string layerName)
        {
            using (DocumentLock docLock = doc.LockDocument())
            {
                using (Transaction trans = doc.TransactionManager.StartTransaction())
                {
                    BlockTable blockTable = trans.GetObject(doc.Database.BlockTableId, OpenMode.ForRead) as BlockTable;
                    BlockTableRecord btr = trans.GetObject(blockTable[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                    DBPoint dbPoint = new DBPoint(point)
                    {
                        Layer = layerName
                    };
                    btr.AppendEntity(dbPoint);
                    trans.AddNewlyCreatedDBObject(dbPoint, true);

                    trans.Commit();
                }
            }
        }

        public static void DrawPointNumber(Autodesk.AutoCAD.ApplicationServices.Document doc, Point3d point, string pointNumber, double textHeight, string layerName)
        {
            using (DocumentLock docLock = doc.LockDocument())
            {
                using (Transaction trans = doc.TransactionManager.StartTransaction())
                {
                    BlockTable blockTable = trans.GetObject(doc.Database.BlockTableId, OpenMode.ForRead) as BlockTable;
                    BlockTableRecord btr = trans.GetObject(blockTable[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                    using (DBText dbText = new DBText())
                    {
                        dbText.Position = new Point3d(point.X, point.Y, point.Z);
                        dbText.Height = textHeight;
                        dbText.TextString = pointNumber;
                        dbText.Layer = layerName;

                        btr.AppendEntity(dbText);
                        trans.AddNewlyCreatedDBObject(dbText, true);
                    }

                    trans.Commit();
                }
            }
        }

        public static string GetCurrentDrawingName()
        {
            // Check if the drawing has been saved (the Name property will have an asterisk '*' if it's unsaved)
            if (string.IsNullOrEmpty(ActiveDocument.Name) || ActiveDocument.Name.EndsWith(".dwt")) // .dwt is the template file extension
            {
                return "Untitled"; // Drawing is unsaved or a template
            }

            // Return the drawing name without the extension
            return System.IO.Path.GetFileNameWithoutExtension(ActiveDocument.Name);
        }

        public static void Zoom(Point3d pMin, Point3d pMax, Point3d pCenter, double dFactor)
        {
            int nCurVport = System.Convert.ToInt32(Application.GetSystemVariable("CVPORT"));

            // If no pMin or pMax provided, use the extents.
            if (ActiveDatabase.TileMode == true)
            {
                if (pMin.Equals(new Point3d()) && pMax.Equals(new Point3d()))
                {
                    pMin = ActiveDatabase.Extmin;
                    pMax = ActiveDatabase.Extmax;
                }
            }
            else
            {
                if (nCurVport == 1)
                {
                    if (pMin.Equals(new Point3d()) && pMax.Equals(new Point3d()))
                    {
                        pMin = ActiveDatabase.Pextmin;
                        pMax = ActiveDatabase.Pextmax;
                    }
                }
                else
                {
                    if (pMin.Equals(new Point3d()) && pMax.Equals(new Point3d()))
                    {
                        pMin = ActiveDatabase.Extmin;
                        pMax = ActiveDatabase.Extmax;
                    }
                }
            }

            // Start a transaction.
            using (Transaction acTrans = ActiveDatabase.TransactionManager.StartTransaction())
            {
                // Get the current view.
                using (ViewTableRecord acView = ActiveDocument.Editor.GetCurrentView())
                {
                    Extents3d eExtents;

                    // Translate WCS coordinates to DCS.
                    Matrix3d matWCS2DCS = Matrix3d.PlaneToWorld(acView.ViewDirection);
                    matWCS2DCS = Matrix3d.Displacement(acView.Target - Point3d.Origin) * matWCS2DCS;
                    matWCS2DCS = Matrix3d.Rotation(-acView.ViewTwist, acView.ViewDirection, acView.Target) * matWCS2DCS;

                    // If a center point is provided, define pMin and pMax based on it.
                    if (pCenter.DistanceTo(Point3d.Origin) != 0)
                    {
                        pMin = new Point3d(pCenter.X - (acView.Width / 2),
                                           pCenter.Y - (acView.Height / 2), 0);
                        pMax = new Point3d((acView.Width / 2) + pCenter.X,
                                           (acView.Height / 2) + pCenter.Y, 0);
                    }

                    // Create an extents object using a line.
                    using (Line acLine = new Line(pMin, pMax))
                    {
                        eExtents = new Extents3d(acLine.Bounds.Value.MinPoint, acLine.Bounds.Value.MaxPoint);
                    }

                    // Calculate the view’s width/height ratio.
                    double dViewRatio = acView.Width / acView.Height;

                    // Transform the extents.
                    matWCS2DCS = matWCS2DCS.Inverse();
                    eExtents.TransformBy(matWCS2DCS);

                    double dWidth, dHeight;
                    Point2d pNewCentPt;

                    // If a center point was provided (Center and Scale modes)
                    if (pCenter.DistanceTo(Point3d.Origin) != 0)
                    {
                        dWidth = acView.Width;
                        dHeight = acView.Height;
                        if (dFactor == 0)
                        {
                            pCenter = pCenter.TransformBy(matWCS2DCS);
                        }
                        pNewCentPt = new Point2d(pCenter.X, pCenter.Y);
                    }
                    else // Working in Window, Extents and Limits mode.
                    {
                        dWidth = eExtents.MaxPoint.X - eExtents.MinPoint.X;
                        dHeight = eExtents.MaxPoint.Y - eExtents.MinPoint.Y;
                        pNewCentPt = new Point2d((eExtents.MaxPoint.X + eExtents.MinPoint.X) * 0.5,
                                                 (eExtents.MaxPoint.Y + eExtents.MinPoint.Y) * 0.5);
                    }

                    // Adjust height if necessary.
                    if (dWidth > (dHeight * dViewRatio))
                    {
                        dHeight = dWidth / dViewRatio;
                    }

                    // Resize and scale the view.
                    if (dFactor != 0)
                    {
                        acView.Height = dHeight * dFactor;
                        acView.Width = dWidth * dFactor;
                    }

                    // Set the center of the view.
                    acView.CenterPoint = pNewCentPt;

                    // Apply the new view.
                    ActiveDocument.Editor.SetCurrentView(acView);
                }
                // Commit the changes.
                acTrans.Commit();
            }
        }

    }
}