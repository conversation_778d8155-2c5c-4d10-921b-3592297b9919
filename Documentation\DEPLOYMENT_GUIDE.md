# Survey Points Manager - Deployment Guide

*For Developers: How to Package and Deploy the Plugin Bundle*

## 📦 Bundle Structure

Survey Points Manager is deployed as an AutoCAD plugin bundle that automatically loads the appropriate version based on the user's AutoCAD installation.

### Complete Bundle Structure
```
SurveyPointsManager.bundle/
├── PackageContents.xml                    # Version detection and loading configuration
└── Contents/
    ├── Windows/                           # Platform-specific DLLs
    │   ├── AutoCAD2015-2016_NETFramework45/
    │   │   ├── SPM_NET45_2015_2016.dll
    │   │   └── Newtonsoft.Json.dll
    │   ├── AutoCAD2017-2018_NETFramework46/
    │   │   ├── SPM_NET46_2017_2018.dll
    │   │   └── Newtonsoft.Json.dll
    │   ├── AutoCAD2019-2020_NETFramework47/
    │   │   ├── SPM_NET47_2019_2020.dll
    │   │   └── Newtonsoft.Json.dll
    │   ├── AutoCAD2021-2024_NETFramework48/
    │   │   ├── SPM_NET48_2021_2024.dll
    │   │   └── Newtonsoft.Json.dll
    │   └── AutoCAD2025-2026_NET8/
    │       ├── SPM_NET8_2025_2026.dll
    │       └── Newtonsoft.Json.dll
    ├── Resources/                         # Shared resources
    │   ├── SurveyPointsManager128x128.ico # Plugin icon
    │   └── SurveyPointsManagerCUI.cuix    # Custom UI
    └── Help/                              # Documentation
        └── SurveyPointsManagerHelp.htm    # Help file
```

## 🔧 PackageContents.xml Configuration

The `PackageContents.xml` file is the heart of the bundle system. It defines:
- Which DLL to load for each AutoCAD version
- Runtime requirements and version bounds
- Commands exposed by the plugin
- Loading behavior

### Key Elements

#### RuntimeRequirements
Each ComponentEntry must specify the AutoCAD version range:
```xml
<RuntimeRequirements OS="Win64" SeriesMin="R25.0" SeriesMax="R26.0" />
```

#### Version Mapping
| AutoCAD Version | Series Code | DLL File |
|-----------------|-------------|----------|
| **2015-2016** | **R20.0-R20.1** | **SPM_NET45_2015_2016.dll** |
| 2017-2018 | R21.0-R22.0 | SPM_NET46_2017_2018.dll |
| 2019-2020 | R23.0-R24.0 | SPM_NET47_2019_2020.dll |
| 2021-2024 | R24.1-R24.3 | SPM_NET48_2021_2024.dll |
| 2025-2026 | R25.0-R26.0 | SPM_NET8_2025_2026.dll |

## 🏗️ Build Process

### Step 1: Build All Versions
```batch
# Build each project version
dotnet build SPM_NET8_2025_2026/SPM_NET8_2025_2026.csproj -c Release
msbuild SPM_NET48_2021_2024/SPM_NET48_2021_2024.csproj /p:Configuration=Release
msbuild SPM_NET47_2019_2020/SPM_NET47_2019_2020.csproj /p:Configuration=Release
msbuild SPM_NET46_2017_2018/SPM_NET46_2017_2018.csproj /p:Configuration=Release
msbuild SPM_NET45_2016/SPM_NET45_2016.csproj /p:Configuration=Release
```

### Step 2: Create Bundle Structure
```batch
# Create bundle directory
mkdir SurveyPointsManager.bundle\Contents

# Copy DLLs to bundle
copy SPM_NET8_2025_2026\bin\Release\SPM_NET8_2025_2026.dll SurveyPointsManager.bundle\Contents\Windows\AutoCAD2025-2026_NET8\
copy SPM_NET48_2021_2024\bin\Release\SPM_NET48_2021_2024.dll SurveyPointsManager.bundle\Contents\Windows\AutoCAD2021-2024_NETFramework48\
copy SPM_NET47_2019_2020\bin\Release\SPM_NET47_2019_2020.dll SurveyPointsManager.bundle\Contents\Windows\AutoCAD2019-2020_NETFramework47\
copy SPM_NET46_2017_2018\bin\Release\SPM_NET46_2017_2018.dll SurveyPointsManager.bundle\Contents\Windows\AutoCAD2017-2018_NETFramework46\
copy SPM_NET45_2015_2016\bin\Release\SPM_NET45_2015_2016.dll SurveyPointsManager.bundle\Contents\Windows\AutoCAD2015-2016_NETFramework45\

# Copy shared dependencies
copy SPM_NET8_2025_2026\bin\Release\Newtonsoft.Json.dll SurveyPointsManager.bundle\Contents\

# Copy PackageContents.xml
copy PackageContents.xml SurveyPointsManager.bundle\
```

### Step 3: Automated Build Script
```batch
@echo off
echo Building Survey Points Manager Bundle...

REM Clean previous build
if exist "SurveyPointsManager.bundle" rmdir /s /q "SurveyPointsManager.bundle"

REM Create bundle structure
mkdir "SurveyPointsManager.bundle\Contents"

REM Build all projects
echo Building .NET 8 version...
dotnet build SPM_NET8_2025_2026/SPM_NET8_2025_2026.csproj -c Release

echo Building .NET Framework versions...
msbuild SPM_NET48_2021_2024/SPM_NET48_2021_2024.csproj /p:Configuration=Release
msbuild SPM_NET47_2019_2020/SPM_NET47_2019_2020.csproj /p:Configuration=Release
msbuild SPM_NET46_2017_2018/SPM_NET46_2017_2018.csproj /p:Configuration=Release
msbuild SPM_NET45_2016/SPM_NET45_2016.csproj /p:Configuration=Release

REM Copy DLLs
echo Copying DLLs to bundle...
copy "SPM_NET8_2025_2026\bin\Release\net8.0-windows\SPM_NET8_2025_2026.dll" "SurveyPointsManager.bundle\Contents\"
copy "SPM_NET48_2021_2024\bin\Release\SPM_NET48_2021_2024.dll" "SurveyPointsManager.bundle\Contents\"
copy "SPM_NET47_2019_2020\bin\Release\SPM_NET47_2019_2020.dll" "SurveyPointsManager.bundle\Contents\"
copy "SPM_NET46_2017_2018\bin\Release\SPM_NET46_2017_2018.dll" "SurveyPointsManager.bundle\Contents\"
copy "SPM_NET45_2016\bin\Release\SPM_NET45_2016.dll" "SurveyPointsManager.bundle\Contents\"

REM Copy dependencies
echo Copying dependencies...
copy "SPM_NET8_2025_2026\bin\Release\net8.0-windows\Newtonsoft.Json.dll" "SurveyPointsManager.bundle\Contents\"

REM Copy configuration
copy "SurveyPointsManager.bundle\PackageContents.xml" "SurveyPointsManager.bundle\"

echo Bundle creation complete!
echo Location: SurveyPointsManager.bundle\
```

## 📋 Testing the Bundle

### Local Testing
1. **Copy bundle to test location:**
   ```
   %APPDATA%\Autodesk\ApplicationPlugins\SurveyPointsManager.bundle
   ```

2. **Test with different AutoCAD versions:**
   - Start each AutoCAD version
   - Verify correct DLL loads
   - Check plugin functionality

3. **Verify automatic loading:**
   - Plugin should load automatically on startup
   - SPM palette should appear
   - Commands should be available

### Validation Checklist
- [ ] Bundle structure is correct
- [ ] PackageContents.xml is valid
- [ ] All DLLs are present and built correctly
- [ ] Dependencies are included
- [ ] Version detection works for all AutoCAD versions
- [ ] Plugin loads automatically
- [ ] All features work in each version
- [ ] No version conflicts or errors

## 🚀 Distribution

### Customer Distribution
1. **Create installer package** containing the bundle
2. **Include installation instructions** for customers
3. **Provide licensing activation** mechanism
4. **Include documentation** and user guides

### Distribution Formats
- **ZIP Archive**: Simple bundle distribution
- **MSI Installer**: Professional installation experience
- **Custom Installer**: With licensing and activation
- **Enterprise Deployment**: Network installation scripts

### Installation Locations
**User Installation:**
```
%APPDATA%\Autodesk\ApplicationPlugins\SurveyPointsManager.bundle
```

**System-wide Installation:**
```
%ALLUSERSPROFILE%\Autodesk\ApplicationPlugins\SurveyPointsManager.bundle
```

**Network Installation:**
```
\\server\share\AutoCAD\Plugins\SurveyPointsManager.bundle
```

## 🔍 Troubleshooting Bundle Issues

### Common Problems

#### Bundle Not Loading
- Check PackageContents.xml syntax
- Verify RuntimeRequirements version ranges
- Ensure DLL files are present
- Check AutoCAD version compatibility

#### Wrong Version Loading
- Verify SeriesMin/SeriesMax values
- Check AutoCAD version with ACADVER command
- Ensure no overlapping version ranges

#### Missing Dependencies
- Include all required DLLs in Contents folder
- Check for .NET Framework version requirements
- Verify Newtonsoft.Json.dll is present

### Debug Information
Enable AutoCAD plugin loading debug:
1. Set registry key: `HKEY_CURRENT_USER\Software\Autodesk\AutoCAD\R25.0\ACAD-xxxx\Profiles\<<Unnamed Profile>>\Dialogs\AcadAppLoad`
2. Set `DebugMode` = 1
3. Check AutoCAD command line for loading messages

## 📝 Version Management

### Adding New AutoCAD Versions
1. **Create new project** targeting appropriate .NET version
2. **Update PackageContents.xml** with new ComponentEntry
3. **Add to build script** and deployment process
4. **Test with new AutoCAD version**

### Deprecating Old Versions
1. **Update documentation** to reflect support status
2. **Maintain existing ComponentEntry** for backward compatibility
3. **Stop active development** but keep for existing customers
4. **Plan end-of-life** timeline

### Version Numbering
- **Bundle Version**: Matches latest component version
- **Component Versions**: Individual DLL versions
- **API Compatibility**: Maintain across versions where possible

---

**Survey Points Manager Bundle Deployment**
*Professional AutoCAD plugin with automatic version detection*

**For Developers: Complete bundle packaging and deployment guide**
