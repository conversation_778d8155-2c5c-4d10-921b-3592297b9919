﻿using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.EditorInput;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using SPM_NET45_2015_2016.Utils;
using System;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;


namespace SPM_NET45_2015_2016.Managers
{
    /// <summary>
    /// Represents the possible outcomes of an entitlement check.
    /// </summary>
    public enum EntitlementStatus
    {
        Unknown,           // Default or error before check starts
        Entitled,          // Verified online successfully
        NotEntitled,       // Verified online, user lacks entitlement
        LoginRequired,     // Check couldn't proceed, Autodesk UserID missing
        OfflineCacheValid, // Offline, using valid cache
        OfflineCacheExpired,// Offline, cache expired
        ConnectivityError, // Cannot reach necessary endpoints
        ServiceError,      // API endpoint returned an error status code or check failed unexpectedly online
        CheckError         // Unexpected internal error during the check process
    }

    /// <summary>
    /// Manages entitlement checks for the PointFlowCAD plugin using Autodesk App Store API.
    /// Includes online verification, offline caching (DPAPI encrypted), and login handling.
    /// Uses Newtonsoft.Json (Json.NET) for serialization.
    /// </summary>
    public class EntitlementManager
    {
        // --- Configuration ---
        private const string AppId = "5391612840032124388"; // Official Autodesk App Store App ID
        private const string AutodeskApiBaseUrl = "https://apps.autodesk.com";
        private const int OfflineGracePeriodDays = 7; // Days the offline cache is valid
        private const int ConnectivityCheckIntervalSeconds = 30; // How often to re-check internet connection
        private const int RequestTimeoutSeconds = 15; // Timeout for API calls

        // --- Cache ---
        private static readonly string CacheFilePath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "PointFlowCAD", // App-specific folder
            "entitlement_cache.dat" // Cache file
        );
        private static readonly byte[] Entropy = Encoding.UTF8.GetBytes("PointFlowCAD-SecureCacheEntropy-V1.1"); // Unique entropy

        // --- State ---
        private readonly string _machineId;
        private DateTime _lastConnectivityCheckTime = DateTime.MinValue;
        private bool _isLikelyOnline = false; // Cached connectivity status

        // --- HTTP Client ---
        // Re-use HttpClient instance for performance
        private static readonly HttpClient httpClient = CreateHttpClient();

        private static HttpClient CreateHttpClient()
        {
            // Enable TLS 1.2 for .NET Framework 4.5 compatibility
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;

            return new HttpClient
            {
                BaseAddress = new Uri(AutodeskApiBaseUrl),
                Timeout = TimeSpan.FromSeconds(RequestTimeoutSeconds + 5) // Slightly longer timeout for client than request itself
            };
        }

        // --- P/Invoke for Login ---
        [DllImport("AcConnectWebServices.arx", CharSet = CharSet.Unicode, EntryPoint = "AcConnectWebServicesLogin")]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool AcConnectWebServicesLogin();

        // --- Constructor ---
        public EntitlementManager()
        {
            try
            {
                // Ensure SecurityUtils.GetMachineIdentifier() is implemented and reliable
                _machineId = SecurityUtils.GetMachineIdentifier();
                if (string.IsNullOrEmpty(_machineId))
                {
                    WriteMessage("CRITICAL ERROR: Could not retrieve unique machine identifier. Entitlement checks may fail.");
                    // Consider throwing an exception or logging severity
                }
            }
            catch (Exception ex)
            {
                WriteMessage($"CRITICAL ERROR initialising EntitlementManager: {ex.Message}");
                _machineId = "ERROR_GETTING_ID"; // Fallback value
            }
            // TLS 1.2 is configured in CreateHttpClient() for .NET Framework 4.5 compatibility
        }

        // --- Helper Properties/Methods ---
        private static Editor Editor => Application.DocumentManager.MdiActiveDocument?.Editor;

        private void WriteMessage(string message)
        {
            try
            {
                Editor?.WriteMessage($"\nPointFlowCAD Entitlement: {message}");
            }
            catch {/* ignored - Editor might not be available */}
        }

        private string GetAutoCADUserId() => Application.GetSystemVariable("ONLINEUSERID") as string ?? string.Empty;


        /// <summary>
        /// Represents the status of the entitlement based purely on the local cache.
        /// </summary>
        public enum CachedEntitlementStatus
        {
            Unknown,         // No cache, error reading cache, or cache not for this user/machine
            NotEntitled,     // Cache explicitly states not entitled
            Expired,         // Cache exists, was entitled, but has expired
            Entitled         // Cache exists, is entitled, and not expired for this user/machine
        }

        /// <summary>
        /// Gets the entitlement status based solely on the local cache. Fast, synchronous check.
        /// </summary>
        public CachedEntitlementStatus GetCachedStatus()
        {
            EntitlementCacheData cachedData = LoadEntitlementCache();
            if (cachedData == null) return CachedEntitlementStatus.Unknown;

            string currentUserId = GetAutoCADUserId();
            // Validate cache context (User ID and Machine ID must match)
            if (cachedData.UserId != currentUserId || cachedData.MachineId != _machineId)
            {
                WriteMessage($"Cache found but is for different user/machine (Cache: U={cachedData.UserId} M={cachedData.MachineId}, Current: U={currentUserId} M={_machineId}).");
                return CachedEntitlementStatus.Unknown;
            }

            // Check entitlement status stored in the cache
            if (!cachedData.IsEntitled) return CachedEntitlementStatus.NotEntitled;
            if (DateTime.UtcNow < cachedData.ExpirationUtc) return CachedEntitlementStatus.Entitled;
            return CachedEntitlementStatus.Expired;
        }


        /// <summary>
        /// Checks the user's entitlement status, attempting online verification first,
        /// then falling back to the local cache. Handles login prompts.
        /// </summary>
        /// <returns>An EntitlementStatus enum indicating the result.</returns>
        public async Task<EntitlementStatus> CheckUserEntitlementAsync()
        {
            try
            {
                string userId = GetAutoCADUserId();

                // --- Check Login Status FIRST ---
                if (string.IsNullOrEmpty(userId))
                {
                    if (!await CheckConnectivityAsync())
                    {
                        WriteMessage("Cannot verify: No Autodesk User ID and no internet connection.");
                        return EntitlementStatus.ConnectivityError;
                    }
                    WriteMessage("Autodesk User ID not found. Login is required.");
                    return EntitlementStatus.LoginRequired;
                }

                // --- We have a UserID, proceed ---
                if (await CheckConnectivityAsync())
                {
                    WriteMessage($"Attempting online verification for User ID: {userId}...");
                    EntitlementApiResponse onlineResult = await TryVerifyOnlineEntitlementAsync(userId);

                    if (onlineResult != null) // API call succeeded and response parsed
                    {
                        if (onlineResult.IsValid)
                        {
                            WriteMessage("Online verification successful: User IS entitled.");
                            SaveEntitlementCache(new EntitlementCacheData(userId, _machineId, true, DateTime.UtcNow.AddDays(OfflineGracePeriodDays)));
                            return EntitlementStatus.Entitled;
                        }
                        else // Server response indicates user is NOT entitled
                        {
                            string serverMessage = string.IsNullOrWhiteSpace(onlineResult.Message) ? "Please check subscriptions." : onlineResult.Message;
                            WriteMessage($"Online verification returned: NOT entitled. Reason: {serverMessage}");
                            // Cache this negative result
                            SaveEntitlementCache(new EntitlementCacheData(userId, _machineId, false, DateTime.UtcNow.AddHours(1)));
                            return EntitlementStatus.NotEntitled;
                        }
                    }
                    else // Online check itself failed (network, timeout, parsing error, server status code error etc.)
                    {
                        WriteMessage("Online check could not be completed (Network/Server/Parse Error). Falling back to local cache.");
                        // Fall through to check cache, but know that online failed
                        EntitlementCacheData cachedData = LoadEntitlementCache();
                        if (cachedData != null && cachedData.IsValidFor(userId, _machineId))
                        {
                            WriteMessage("Using valid offline cache after online failure.");
                            return EntitlementStatus.OfflineCacheValid;
                        }
                        // Online check failed AND cache is not valid/present
                        return EntitlementStatus.ServiceError;
                    }
                }
                else // --- Offline Section ---
                {
                    WriteMessage("No internet connectivity. Attempting to use local cache.");
                    EntitlementCacheData cachedData = LoadEntitlementCache();
                    if (cachedData != null)
                    {
                        if (cachedData.IsValidFor(userId, _machineId))
                        {
                            TimeSpan remaining = cachedData.ExpirationUtc - DateTime.UtcNow;
                            WriteMessage($"Verified using offline cache. Valid for ~{remaining.Days}d {remaining.Hours}h.");
                            return EntitlementStatus.OfflineCacheValid;
                        }
                        else if (cachedData.UserId == userId && cachedData.MachineId == _machineId)
                        {
                            WriteMessage("Offline cache found but has expired or is invalid.");
                            return EntitlementStatus.OfflineCacheExpired;
                        }
                        else
                        {
                            WriteMessage("Offline cache exists but is for a different user/machine.");
                            return EntitlementStatus.Unknown;
                        }
                    }
                    else
                    {
                        WriteMessage("Offline: No local license cache found.");
                        return EntitlementStatus.Unknown;
                    }
                }
            }
            catch (Exception ex) // Catch unexpected errors during the process
            {
                WriteMessage($"Unexpected error during entitlement check: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"[EntitlementManager] Check Error: {ex.ToString()}");
                return EntitlementStatus.CheckError;
            }
        }

        #region Online / Connectivity Helpers

        private async Task<bool> CheckConnectivityAsync(bool forceCheck = false)
        {
            if (!forceCheck && (DateTime.UtcNow - _lastConnectivityCheckTime).TotalSeconds < ConnectivityCheckIntervalSeconds)
            {
                return _isLikelyOnline;
            }
            _lastConnectivityCheckTime = DateTime.UtcNow;
            _isLikelyOnline = false; // Assume offline until proven otherwise
            try
            {
                // Use HEAD request for efficiency
                var request = new HttpRequestMessage(HttpMethod.Head, "/"); // Check base address root
                using (var cts = new System.Threading.CancellationTokenSource(TimeSpan.FromSeconds(5))) // 5s timeout
                {
                    HttpResponseMessage response = await httpClient.SendAsync(request, cts.Token);
                    _isLikelyOnline = response.IsSuccessStatusCode; // OK, Redirect are often OK indicators
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[EntitlementManager] Connectivity Check Failed: {ex.Message}");
                _isLikelyOnline = false;
            }
            System.Diagnostics.Debug.WriteLine($"[EntitlementManager] Connectivity Check Result: {_isLikelyOnline}");
            return _isLikelyOnline;
        }


        private async Task<EntitlementApiResponse> TryVerifyOnlineEntitlementAsync(string userId)
        {
            if (string.IsNullOrEmpty(userId)) return null;
            string requestUrl = $"webservices/checkentitlement?userid={WebUtility.UrlEncode(userId)}&appid={WebUtility.UrlEncode(AppId)}";
            System.Diagnostics.Debug.WriteLine($"[EntitlementManager] Requesting: {httpClient.BaseAddress}{requestUrl}");

            try
            {
                using (var cts = new System.Threading.CancellationTokenSource(TimeSpan.FromSeconds(RequestTimeoutSeconds)))
                {
                    HttpResponseMessage response = await httpClient.GetAsync(requestUrl, cts.Token);
                    string jsonResponse = await response.Content.ReadAsStringAsync(); // Read content regardless of status for logging

                    System.Diagnostics.Debug.WriteLine($"[EntitlementManager] Response status: {response.StatusCode}");
                    System.Diagnostics.Debug.WriteLine($"[EntitlementManager] Response JSON: {jsonResponse}");

                    if (!response.IsSuccessStatusCode)
                    {
                        WriteMessage($"Entitlement API request failed. Status: {response.StatusCode}");
                        return null; // Indicate failure to verify
                    }

                    // Use Newtonsoft.Json here
                    EntitlementApiResponse apiResult = JsonConvert.DeserializeObject<EntitlementApiResponse>(jsonResponse);

                    // Basic validation
                    if (apiResult == null || string.IsNullOrEmpty(apiResult.AppIdReturned))
                    {
                        System.Diagnostics.Debug.WriteLine("[EntitlementManager] API response format unexpected (Newtonsoft).");
                        WriteMessage("Received an unexpected response format from the licensing server.");
                        return null;
                    }
                    return apiResult;
                }
            }
            catch (JsonException jsonEx) { WriteMessage("Error parsing server response."); System.Diagnostics.Debug.WriteLine($"[EntitlementManager] Newtonsoft JSON parsing error: {jsonEx.ToString()}"); return null; }
            catch (HttpRequestException httpEx) { WriteMessage("Network error contacting licensing server."); System.Diagnostics.Debug.WriteLine($"[EntitlementManager] Network error: {httpEx.ToString()}"); return null; }
            catch (TaskCanceledException cancelEx) { WriteMessage("License server request timed out."); System.Diagnostics.Debug.WriteLine($"[EntitlementManager] Request timeout: {cancelEx.ToString()}"); return null; }
            catch (Exception ex) { WriteMessage($"Unexpected verify error: {ex.Message}"); System.Diagnostics.Debug.WriteLine($"[EntitlementManager] Unexpected verify error: {ex.ToString()}"); return null; }
        }

        #endregion

        #region Cache (DPAPI + JSON) Helpers

        private void SaveEntitlementCache(EntitlementCacheData data)
        {
            try
            {
                string directoryPath = Path.GetDirectoryName(CacheFilePath);
                if (!string.IsNullOrEmpty(directoryPath)) Directory.CreateDirectory(directoryPath);

                string json = JsonConvert.SerializeObject(data, Formatting.None);
                byte[] plainTextBytes = Encoding.UTF8.GetBytes(json);
                byte[] encryptedBytes = ProtectedData.Protect(plainTextBytes, Entropy, DataProtectionScope.CurrentUser);
                File.WriteAllBytes(CacheFilePath, encryptedBytes);
            }
            catch (Exception ex)
            {
                WriteMessage($"Failed to save cache: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"[EntitlementManager] Cache save error: {ex.ToString()}");
            }
        }

        private EntitlementCacheData LoadEntitlementCache()
        {
            if (!File.Exists(CacheFilePath)) return null;
            try
            {
                byte[] encryptedBytes = File.ReadAllBytes(CacheFilePath);
                if (encryptedBytes.Length == 0) { WriteMessage("Cache file is empty."); return null; }

                byte[] plainTextBytes = ProtectedData.Unprotect(encryptedBytes, Entropy, DataProtectionScope.CurrentUser);
                string json = Encoding.UTF8.GetString(plainTextBytes);

                return JsonConvert.DeserializeObject<EntitlementCacheData>(json);
            }
            catch (CryptographicException) { WriteMessage("Failed to decrypt cache (corrupt/context changed?)."); ClearEntitlementCache(); return null; }
            catch (JsonException) { WriteMessage("Failed to parse cache (format error?)."); ClearEntitlementCache(); return null; }
            catch (Exception ex) { WriteMessage($"Failed to load cache: {ex.Message}"); return null; }
        }

        private void ClearEntitlementCache()
        {
            try
            {
                if (File.Exists(CacheFilePath)) File.Delete(CacheFilePath);
            }
            catch (Exception ex) { WriteMessage($"Failed to clear cache: {ex.Message}"); }
        }

        #endregion

        #region DTOs (Data Transfer Objects)

        // Represents the data stored in the encrypted cache file
        private class EntitlementCacheData
        {
            [JsonProperty("userId")]
            public string UserId { get; set; }
            [JsonProperty("machineId")]
            public string MachineId { get; set; }
            [JsonProperty("isEntitled")]
            public bool IsEntitled { get; set; }
            [JsonProperty("expirationUtc")]
            public DateTime ExpirationUtc { get; set; }

            public EntitlementCacheData() { } // Needed for deserialization

            public EntitlementCacheData(string userId, string machineId, bool isEntitled, DateTime expirationUtc)
            {
                UserId = userId; MachineId = machineId; IsEntitled = isEntitled; ExpirationUtc = expirationUtc;
            }

            // Helper method used by logic outside DTO
            public bool IsValidFor(string currentUserId, string currentMachineId)
            {
                return IsEntitled && UserId == currentUserId && MachineId == currentMachineId && DateTime.UtcNow < ExpirationUtc;
            }
        }

        // Represents the expected JSON structure from the Autodesk entitlement API
        private class EntitlementApiResponse
        {
            [JsonProperty("userId")]
            public string UserIdReturned { get; set; }
            [JsonProperty("appId")]
            public string AppIdReturned { get; set; }
            [JsonProperty("isValid")]
            public bool IsValid { get; set; }
            [JsonProperty("message")]
            public string Message { get; set; }
        }

        #endregion
    }

}
