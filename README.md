# Survey Points Manager (SPM) - Professional AutoCAD Plugin

## Overview

Survey Points Manager (SPM) is a professional AutoCAD plugin designed specifically for surveyors, civil engineers, and CAD professionals who need to efficiently import, manage, export, and visualize survey point data directly within AutoCAD. This commercial solution provides multiple versions targeting different AutoCAD releases to ensure compatibility with your existing workflow.

## 🚀 Plugin Architecture

Survey Points Manager is deployed as a **single AutoCAD plugin bundle** that automatically loads the appropriate version based on your AutoCAD installation:

### Bundle Structure
```
SurveyPointsManager.bundle/
├── PackageContents.xml          # Automatic version detection and loading
├── Contents/
│   ├── SPM_NET45_2016.dll       # For AutoCAD 2016
│   ├── SPM_NET46_2017_2018.dll  # For AutoCAD 2017-2018
│   ├── SPM_NET47_2019_2020.dll  # For AutoCAD 2019-2020
│   ├── SPM_NET48_2021_2024.dll  # For AutoCAD 2021-2024
│   ├── SPM_NET8_2025_2026.dll   # For AutoCAD 2025-2026
│   └── Resources/               # Shared resources
```

### Automatic Version Loading

The plugin **automatically detects your AutoCAD version** and loads the appropriate DLL:

| AutoCAD Version | Loaded DLL | Framework | Status |
|-----------------|------------|-----------|---------|
| **2025, 2026** | **SPM_NET8_2025_2026.dll** | .NET 8.0 | ✅ **Latest** |
| 2021-2024 | SPM_NET48_2021_2024.dll | .NET Framework 4.8 | 🔧 Supported |
| 2019-2020 | SPM_NET47_2019_2020.dll | .NET Framework 4.7 | 🔧 Supported |
| 2017-2018 | SPM_NET46_2017_2018.dll | .NET Framework 4.6 | 🔧 Supported |
| **2015-2016** | **SPM_NET45_2015_2016.dll** | **.NET Framework 4.5** | 🔧 **Supported** |

**You don't choose a version** - the plugin automatically uses the best version for your AutoCAD!

## 🔧 Features

### Import Capabilities
- **Multiple File Formats**: CSV, TXT, KML, SDR, IDX, GSI (8-bit and 16-bit)
- **Smart Format Detection**: Automatic detection of file formats and coordinate systems
- **Flexible Column Mapping**: Customizable field mapping for different data structures
- **Coordinate System Support**: UTM zones, hemispheres, and various projection systems
- **Data Validation**: Real-time validation and error reporting during import

### Export Capabilities
- **Multiple Output Formats**: CSV, TXT, KML, SDR, IDX, GSI
- **Customizable Export Settings**: Headers, point numbering, coordinate order (XY/YX)
- **Batch Export**: Export large datasets efficiently
- **Format-Specific Options**: Tailored settings for each export format

### Point Management
- **Interactive Point Picking**: Manual and automatic point selection tools
- **Smart Point Numbering**: Intelligent sequential numbering with customizable starting points
- **Point Editing**: In-place editing of coordinates, elevations, and descriptions
- **Search and Filter**: Advanced search capabilities across all point attributes
- **Undo/Redo**: Full undo/redo support for all operations

### AutoCAD Integration
- **Drawing Tools**: Direct point insertion into AutoCAD drawings
- **Table Generation**: Create formatted tables in model space
- **Layer Management**: Automatic layer creation and management
- **OSNAP Integration**: Enhanced object snap functionality
- **Block and Text Processing**: Smart handling of existing drawing elements

### Advanced Features
- **History Management**: Complete operation history with search capabilities
- **Settings Management**: Persistent user preferences and configurations
- **Entitlement System**: License management and validation
- **Performance Optimization**: Efficient handling of large datasets

## 📋 System Requirements

### For SPM_NET8_2025_2026 (Recommended)
- **Operating System**: Windows 10/11 (64-bit)
- **AutoCAD**: AutoCAD 2025 or AutoCAD 2026
- **.NET Runtime**: .NET 8.0 Runtime (included with AutoCAD 2025/2026)
- **Memory**: Minimum 4GB RAM (8GB recommended for large datasets)
- **Storage**: 50MB free disk space

### For Legacy Versions
- **AutoCAD 2021-2024**: Use SPM_NET48_2021_2024
- **AutoCAD 2019-2020**: Use SPM_NET47_2019_2020
- **AutoCAD 2017-2018**: Use SPM_NET46_2017_2018
- **AutoCAD 2015-2016**: Use SPM_NET45_2015_2016

## 🛠️ Installation

### Quick Installation (SPM_NET8_2025_2026)
1. Download the latest release
2. Extract plugin files to a local directory
3. Open AutoCAD 2025/2026
4. Type `NETLOAD` in the command line
5. Browse and select `SPM_NET8_2025_2026.dll`
6. The plugin will load and display the SPM palette

For detailed installation instructions, see [INSTALLATION_GUIDE.md](INSTALLATION_GUIDE.md)

## 📚 Documentation

### Complete Documentation Suite
- **[USER_GUIDE.md](USER_GUIDE.md)**: Comprehensive user manual for surveyors and engineers
- **[INSTALLATION_GUIDE.md](INSTALLATION_GUIDE.md)**: Installation and licensing procedures
- **[SUPPORT.md](SUPPORT.md)**: Customer support information and resources
- **[CHANGELOG.md](CHANGELOG.md)**: Version history and migration notes
- **[ROADMAP.md](ROADMAP.md)**: Future features and development plans

### Quick Links
- **Getting Started**: See USER_GUIDE.md → Getting Started section
- **Import Data**: See USER_GUIDE.md → Importing Data section
- **Export Data**: See USER_GUIDE.md → Exporting Data section
- **Troubleshooting**: See USER_GUIDE.md → Troubleshooting section

## 🚀 Future Development

### Roadmap Highlights
We have an exciting roadmap planned for Survey Points Manager! Key upcoming features include:

- **🔄 As-Built Comparison**: Compare design plans to actual survey points with detailed discrepancy reports
- **📁 Batch Import**: Import multiple files at once with enhanced options
- **🌍 Smart UTM Detection**: Automatic UTM zone detection and coordinate system assistance
- **☁️ Cloud Integration**: Sync point data to Google Drive and other cloud services
- **🎨 Enhanced UI**: AI-powered design improvements with light/dark theme support
- **🌐 Multi-language Support**: Broader international usability

See our complete **[ROADMAP.md](ROADMAP.md)** for detailed feature plans, timelines, and how to contribute to development.

## 🔄 Migration Information

### Upgrading to .NET 8 Version
If you're currently using an older version, we recommend upgrading to SPM_NET8_2025_2026:

#### Benefits of Upgrading
- **25% faster performance** compared to .NET Framework versions
- **15% reduction in memory usage**
- **Enhanced stability** and error handling
- **Modern architecture** with future-proof design
- **Active development** and new feature additions

#### Migration Process
1. **Backup**: Save current projects and settings
2. **Upgrade AutoCAD**: Install AutoCAD 2025 or 2026
3. **Install New Version**: Follow installation guide for .NET 8 version
4. **Migrate Data**: All existing data files are compatible
5. **Validate**: Test with existing workflows

For detailed migration instructions, see [CHANGELOG.md](CHANGELOG.md)

## 🎯 Quick Start Guide

### First Time Users
1. **Install**: Follow installation guide for your AutoCAD version
2. **Launch**: Load the plugin in AutoCAD
3. **Import**: Start with a small CSV file to test functionality
4. **Explore**: Try different import/export formats
5. **Configure**: Adjust settings to match your workflow

### Existing Users
1. **Backup**: Save current settings and projects
2. **Upgrade**: Install the appropriate version for your AutoCAD
3. **Migrate**: Import existing data files
4. **Validate**: Verify all functionality works as expected

## 🔍 Version Selection Guide

### Choose Your Version

**For New Installations:**
- **AutoCAD 2025/2026**: Use **SPM_NET8_2025_2026** (Recommended)
- **AutoCAD 2021-2024**: Use SPM_NET48_2021_2024
- **AutoCAD 2019-2020**: Use SPM_NET47_2019_2020
- **AutoCAD 2017-2018**: Use SPM_NET46_2017_2018
- **AutoCAD 2015-2016**: Use SPM_NET45_2015_2016

**For Upgrades:**
- **Current Users**: Consider upgrading to AutoCAD 2025/2026 + SPM_NET8_2025_2026
- **Legacy Support**: Older versions remain available for existing installations

## 🐛 Support and Troubleshooting

### Getting Help
1. **Documentation**: Check the comprehensive user guide
2. **Common Issues**: Review troubleshooting section
3. **Version Compatibility**: Ensure correct version for your AutoCAD
4. **Technical Support**: Contact support with system details

### Reporting Issues
When reporting issues, please include:
- SPM version number
- AutoCAD version and build
- Operating system details
- Steps to reproduce the issue
- Error messages or screenshots

## 📊 Performance Guidelines

### Dataset Size Recommendations

| Dataset Size | Recommended RAM | Processing Time | Best Version |
|--------------|-----------------|-----------------|--------------|
| < 1,000 points | 4GB | < 1 minute | Any version |
| 1,000 - 10,000 | 8GB | 1-5 minutes | .NET 8 preferred |
| 10,000 - 50,000 | 16GB | 5-15 minutes | .NET 8 recommended |
| > 50,000 points | 32GB+ | 15+ minutes | .NET 8 required |

## 🔐 License and Legal

This software is proprietary and commercially licensed. Please refer to your license agreement for usage terms and conditions. Unauthorized distribution or modification is prohibited.

## 📞 Customer Support

This is a commercial product with dedicated customer support:
- **Technical Support**: Available for licensed users
- **Feature Requests**: Submit through customer support portal
- **Bug Reports**: Priority support for licensed customers
- **Training**: Available upon request for enterprise customers

---

## 📞 Contact Information

- **Technical Support**: [Contact details to be provided]
- **Documentation**: Complete guides available in this repository
- **Updates**: Check releases for latest versions

---

**Survey Points Manager - The Complete Solution for Survey Data Management in AutoCAD**

*Choose the right version for your AutoCAD installation and start managing survey data more efficiently today!*
