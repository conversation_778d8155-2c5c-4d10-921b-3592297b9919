﻿using SPM_NET46_2017_2018.Managers;
using SPM_NET46_2017_2018.Models;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Xml.Linq;

namespace SPM_NET46_2017_2018.Services
{
    public static class ExportService
    {
        public static void ExportTXTPoints(IEnumerable<SurveyPoint> points, bool includeHeaders, bool includePointNumber, bool isXYOrder)
        {
            // Retrieve the current drawing name and build a descriptive default file name
            string drawingName = DrawingService.GetCurrentDrawingName();
            string datePart = DateTime.Now.ToString("yyyy-MM-dd");
            string defaultFileName = $"{drawingName}_Po_Exp_{datePart}.txt";
            int pointsCount = points.Count(); // Number of points exported

            // Prompt the user with a SaveFileDialog
            var saveFileDialog = new Microsoft.Win32.SaveFileDialog
            {
                Title = "Export Tab-Delimited Text Points",
                Filter = "Text Files (*.txt)|*.txt",
                FileName = defaultFileName
            };

            if (saveFileDialog.ShowDialog() != true)
            {
                return;
            }

            string filePath = saveFileDialog.FileName;

            try
            {
                using (var writer = new StreamWriter(filePath))
                {
                    // Write header row if enabled
                    if (includeHeaders)
                    {
                        string header = includePointNumber ? "PN\t" : string.Empty;
                        header += isXYOrder
                            ? "Easting\tNorthing\tElevation\tDescription"
                            : "Northing\tEasting\tElevation\tDescription";
                        writer.WriteLine(header);
                    }

                    // Loop through each SurveyPoint
                    foreach (var point in points)
                    {
                        string line;
                        if (includePointNumber)
                        {
                            line = isXYOrder
                                ? $"{point.PointNumber}\t{point.Easting}\t{point.Northing}\t{point.Elevation}\t{point.Description}"
                                : $"{point.PointNumber}\t{point.Northing}\t{point.Easting}\t{point.Elevation}\t{point.Description}";
                        }
                        else
                        {
                            line = isXYOrder
                                ? $"{point.Easting}\t{point.Northing}\t{point.Elevation}\t{point.Description}"
                                : $"{point.Northing}\t{point.Easting}\t{point.Elevation}\t{point.Description}";
                        }
                        writer.WriteLine(line);
                    }
                }

                HistoryManager.Instance.AddRecord(defaultFileName, "Export", "TXT", pointsCount);
                System.Windows.MessageBox.Show("Points successfully exported to tab-delimited text file.", "Export Success", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error exporting tab-delimited text file: {ex.Message}", "Export Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // BUG: if the data grid contains an empty description it look like : p1,812757.764,2426751.093,1028.38, and this is wrong , suppose no , in the end, ignor empty description
        public static void ExportCSVPoints(IEnumerable<SurveyPoint> points, bool includeHeaders, bool includePointNumber, bool isXYOrder)
        {
            // Retrieve the current drawing name and build a descriptive default file name
            string drawingName = DrawingService.GetCurrentDrawingName();
            string datePart = DateTime.Now.ToString("yyyy-MM-dd");
            string defaultFileName = $"{drawingName}_Po_Exp_{datePart}.csv";
            int pointsCount = points.Count(); // Number of points exported

            // Prompt the user with a SaveFileDialog
            var saveFileDialog = new Microsoft.Win32.SaveFileDialog
            {
                Title = "Export CSV Points",
                Filter = "CSV Files (*.csv)|*.csv",
                FileName = defaultFileName
            };

            if (saveFileDialog.ShowDialog() != true)
            {
                return;
            }

            string filePath = saveFileDialog.FileName;

            try
            {
                using (var writer = new StreamWriter(filePath))
                {
                    // Write header row if enabled
                    if (includeHeaders)
                    {
                        string header = includePointNumber ? "PN," : string.Empty;
                        header += isXYOrder
                            ? "Easting,Northing,Elevation,Description"
                            : "Northing,Easting,Elevation,Description";
                        writer.WriteLine(header);
                    }

                    // Loop through each SurveyPoint
                    foreach (var point in points)
                    {
                        string csvLine;
                        if (includePointNumber)
                        {
                            csvLine = isXYOrder
                                ? $"{point.PointNumber},{point.Easting},{point.Northing},{point.Elevation},{EscapeCsv(point.Description)}"
                                : $"{point.PointNumber},{point.Northing},{point.Easting},{point.Elevation},{EscapeCsv(point.Description)}";
                        }
                        else
                        {
                            csvLine = isXYOrder
                                ? $"{point.Easting},{point.Northing},{point.Elevation},{EscapeCsv(point.Description)}"
                                : $"{point.Northing},{point.Easting},{point.Elevation},{EscapeCsv(point.Description)}";
                        }
                        writer.WriteLine(csvLine);
                    }
                }

                // Record export in history
                HistoryManager.Instance.AddRecord(defaultFileName, "Export", "CSV", pointsCount);
                System.Windows.MessageBox.Show("Points successfully exported to CSV.", "Export Success", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error exporting CSV file: {ex.Message}", "Export Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // Helper method to escape CSV fields containing commas or quotes
        private static string EscapeCsv(string field)
        {
            if (string.IsNullOrEmpty(field))
            {
                return string.Empty;
            }
            if (field.Contains(",") || field.Contains("\""))
            {
                field = field.Replace("\"", "\"\"");
                return $"\"{field}\"";
            }
            return field;
        }


        public static void ExportKMLPoints(IEnumerable<SurveyPoint> points, int zone, bool isNorthernHemisphere)
        {
            string drawingName = DrawingService.GetCurrentDrawingName();
            string datePart = DateTime.Now.ToString("yyyy-MM-dd");
            string defaultFileName = $"{drawingName}_Po_Exp_{datePart}.kml";
            int pointsCount = points.Count(); // Number of points exported

            var saveFileDialog = new Microsoft.Win32.SaveFileDialog
            {
                Title = "Export KML Points",
                Filter = "KML Files (*.kml)|*.kml",
                FileName = defaultFileName
            };

            if (saveFileDialog.ShowDialog() != true)
            {
                return;
            }

            string filePath = saveFileDialog.FileName;

            try
            {
                XNamespace kmlNamespace = "http://www.opengis.net/kml/2.2";
                XNamespace gxNamespace = "http://www.google.com/kml/ext/2.2";
                XNamespace atomNamespace = "http://www.w3.org/2005/Atom";

                var kml = new XElement(kmlNamespace + "kml",
                    new XAttribute(XNamespace.Xmlns + "gx", gxNamespace),
                    new XAttribute(XNamespace.Xmlns + "atom", atomNamespace),
                    new XElement(kmlNamespace + "Document")
                );

                string hemisphere = isNorthernHemisphere ? "North" : "South";

                foreach (var point in points)
                {
                    Debug.WriteLine($"Converting UTM -> Lat/Lon: Easting={point.Easting}, Northing={point.Northing}, Zone={zone}, Hemisphere={hemisphere}");
                    CoordinateConversion.UTMToDecimalDegrees(point.Easting, point.Northing, zone, isNorthernHemisphere, out double latitude, out double longitude);
                    Debug.WriteLine($"Converted: Latitude={latitude}, Longitude={longitude}");

                    var placemark = new XElement(kmlNamespace + "Placemark",
                        new XElement(kmlNamespace + "name", point.PointNumber),
                        new XElement(kmlNamespace + "description", point.Description),
                        new XElement(kmlNamespace + "Point",
                            new XElement(kmlNamespace + "coordinates", $"{longitude},{latitude},{point.Elevation}")
                        )
                    );

                    kml.Element(kmlNamespace + "Document").Add(placemark);
                }

                var xml = new XDocument(new XDeclaration("1.0", "UTF-8", "yes"), kml);
                xml.Save(filePath);

                HistoryManager.Instance.AddRecord(defaultFileName, "Export", "KML", pointsCount);
                System.Windows.MessageBox.Show("KML file exported successfully.", "Export Success", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"An error occurred while exporting KML file: {ex.Message}", "Export Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }


        public static void ExportSDRPoints(IEnumerable<SurveyPoint> points)
        {
            const int LINE_LENGTH = 84;

            // Retrieve the current drawing name and build a descriptive default file name
            string drawingName = DrawingService.GetCurrentDrawingName();
            string datePart = DateTime.Now.ToString("yyyy-MM-dd");
            string defaultFileName = $"{drawingName}_Po_Exp_{datePart}.sdr";
            int pointsCount = points.Count(); // Number of points exported

            // Prompt the user with a SaveFileDialog
            var saveFileDialog = new Microsoft.Win32.SaveFileDialog
            {
                Title = "Export SDR Points",
                Filter = "SDR Files (*.sdr)|*.sdr",
                FileName = defaultFileName
            };

            if (saveFileDialog.ShowDialog() != true)
            {
                return;
            }

            string filePath = saveFileDialog.FileName;

            try
            {
                using (var writer = new StreamWriter(filePath))
                {
                    // Write header lines with dynamic timestamp and job name
                    string timestamp = DateTime.Now.ToString("dd-MMM-yy HH:mm");
                    writer.WriteLine($"00NMSDR33 V04-04.02     {timestamp} 113111");
                    string jobName = drawingName.ToUpper().Substring(0, Math.Min(10, drawingName.Length));
                    writer.WriteLine($"10NM{jobName}     121111");
                    writer.WriteLine("06NM1.00000000");
                    writer.WriteLine("01NM:CX-105               GS3827CX-105               GS382731                                0.000");
                    writer.WriteLine("03NM1.000");

                    // Process each SurveyPoint
                    foreach (var point in points)
                    {
                        try
                        {
                            // Retrieve and format point values
                            string pointNumber = (point.PointNumber.ToString() ?? "").Trim();
                            string easting = point.Easting.ToString("F4");
                            string northing = point.Northing.ToString("F4");
                            string elevation = point.Elevation.ToString("F4");
                            string description = (point.Description ?? "").Trim();

                            // Build the formatted data line using a fixed data code ("08TP") and padding
                            string dataCode = "08TP";
                            string formattedDataLine = dataCode
                                + new string(' ', Math.Max(0, 16 - pointNumber.Length)) + pointNumber
                                + easting + new string(' ', Math.Max(0, 16 - easting.Length))
                                + northing + new string(' ', Math.Max(0, 16 - northing.Length))
                                + elevation + new string(' ', Math.Max(0, 16 - elevation.Length))
                                + description;

                            // Ensure the line is exactly LINE_LENGTH characters long
                            if (formattedDataLine.Length > LINE_LENGTH)
                            {
                                formattedDataLine = formattedDataLine.Substring(0, LINE_LENGTH);
                            }
                            else if (formattedDataLine.Length < LINE_LENGTH)
                            {
                                formattedDataLine = formattedDataLine.PadRight(LINE_LENGTH);
                            }

                            writer.WriteLine(formattedDataLine);
                        }
                        catch (Exception innerEx)
                        {
                            System.Windows.MessageBox.Show($"Error processing point for export: {innerEx.Message}");
                        }
                    }
                }

                HistoryManager.Instance.AddRecord(defaultFileName, "Export", "SDR", pointsCount);
                System.Windows.MessageBox.Show("Points successfully exported to SDR file.", "Export Success", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error exporting SDR file: {ex.Message}", "Export Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }


        public static void ExportIDXPoints(IEnumerable<SurveyPoint> points)
        {
            // Build a descriptive default file name
            string drawingName = DrawingService.GetCurrentDrawingName();
            string datePart = DateTime.Now.ToString("yyyy-MM-dd");
            string defaultFileName = $"{drawingName}_Po_Exp_{datePart}.idx";
            int pointsCount = points.Count(); // Number of points exported

            // Prompt user to choose a destination file
            var saveFileDialog = new Microsoft.Win32.SaveFileDialog
            {
                Title = "Export IDX Points",
                Filter = "IDX Files (*.idx)|*.idx",
                FileName = defaultFileName
            };

            if (saveFileDialog.ShowDialog() != true)
            {
                return;
            }

            string filePath = saveFileDialog.FileName;

            try
            {
                using (var writer = new StreamWriter(filePath))
                {
                    // Write the HEADER section
                    writer.WriteLine("HEADER");
                    writer.WriteLine("\tVERSION 1.20");
                    writer.WriteLine("\tSYSTEM\t\"TS02-7\"\"");
                    writer.WriteLine("\tUNITS");
                    writer.WriteLine("\t\tANGULAR DMS");
                    writer.WriteLine("\t\tLINEAR  METRE");
                    writer.WriteLine("\t\tTEMP    CELSIUS");
                    writer.WriteLine("\t\tPRESS   HPA");
                    writer.WriteLine("\t\tTIME    DMY");
                    writer.WriteLine("\tEND UNITS");
                    writer.WriteLine("\tPROJECT");
                    writer.WriteLine("\t\tNAME\tANGARD\"\"\"");
                    writer.WriteLine("\t\tOPERATOR\tBQ");

                    // Generate the current date and time
                    string creationDate = DateTime.Now.ToString("dd-MM-yyyy/HH:mm:ss.0");
                    writer.WriteLine($"\t\tCREATION_DATE\t{creationDate}");

                    writer.WriteLine("\tEND PROJECT");
                    writer.WriteLine("END HEADER");

                    // Write the DATABASE section
                    writer.WriteLine("DATABASE");
                    writer.WriteLine("\tPOINTS (PointNo, PointID, East, North, Elevation, Code, Date, CLASS)");

                    // Starting point number
                    long pointNumber = 25165837;

                    // Iterate through each SurveyPoint
                    foreach (var point in points)
                    {
                        try
                        {
                            string pointID = point.PointNumber.ToString().Trim();
                            string east = point.Easting.ToString("F4");
                            string north = point.Northing.ToString("F4");
                            string elevation = point.Elevation.ToString("F4");
                            // Using Description as the "code" field
                            string code = (point.Description ?? "").Trim();
                            // Current date for each point
                            string date = DateTime.Now.ToString("dd-MM-yyyy/HH:mm:ss.0");

                            // Form the row
                            string pointRow = $"{pointNumber},\t{pointID},\t{east},\t{north},\t{elevation},\t{code}\t{date}\t,\tFIX;";
                            writer.WriteLine("\t\t" + pointRow);

                            pointNumber++;
                        }
                        catch (Exception innerEx)
                        {
                            System.Windows.MessageBox.Show($"Error processing point for export: {innerEx.Message}",
                                "Processing Error", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }

                    // Write closing tags
                    writer.WriteLine("\tEND POINTS");
                    writer.WriteLine("END DATABASE");
                }

                HistoryManager.Instance.AddRecord(defaultFileName, "Export", "IDX", pointsCount);
                System.Windows.MessageBox.Show("Points successfully exported to IDX file.", "Export Success",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error exporting IDX file: {ex.Message}",
                    "Export Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }


        public static void ExportGSIPoints(IEnumerable<SurveyPoint> points, ComboBoxItem selectedGSIFormat)
        {
            // Build a default file name using the current drawing name and date.
            string drawingName = Services.DrawingService.GetCurrentDrawingName();
            string datePart = DateTime.Now.ToString("yyyy-MM-dd");
            string defaultFileName = $"{drawingName}_Po_Exp_{datePart}.gsi";
            int pointsCount = points.Count(); // Number of points exported

            // Prompt user with a SaveFileDialog.
            var saveFileDialog = new Microsoft.Win32.SaveFileDialog
            {
                Title = "Export GSI Points",
                Filter = "GSI Files (*.gsi)|*.gsi",
                FileName = defaultFileName
            };

            if (saveFileDialog.ShowDialog() != true)
            {
                return;
            }

            string filePath = saveFileDialog.FileName;

            try
            {
                using (var writer = new StreamWriter(filePath))
                {
                    // Determine the GSI variant from the parameter
                    string selectedVariant = selectedGSIFormat?.Content.ToString() ?? "GSI 8";
                    bool isGsi16 = selectedVariant.Equals("GSI 16", StringComparison.OrdinalIgnoreCase);

                    foreach (var point in points)
                    {
                        string line = string.Empty;
                        if (isGsi16)
                        {
                            // --- GSI 16 Format ---
                            string field0 = $"*11....+{(point.PointNumber ?? "").PadLeft(16, '0')}";
                            string eastNumeric = ((long)(point.Easting * 1000)).ToString("D16");
                            string field1 = $"81..40+{eastNumeric}";
                            string northNumeric = ((long)(point.Northing * 1000)).ToString("D16");
                            string field2 = $"82..40+{northNumeric}";
                            string elevNumeric = ((long)(point.Elevation * 1000)).ToString("D16");
                            string field3 = $"83..40+{elevNumeric}";

                            string field4 = string.Empty;
                            if (!string.IsNullOrWhiteSpace(point.Description))
                            {
                                field4 = $" 71....+{point.Description.PadLeft(16, '0')}";
                            }

                            line = $"{field0} {field1} {field2} {field3}{field4}";
                        }
                        else
                        {
                            // --- GSI 8 Format ---
                            string field0 = $"110001+0000{point.PointNumber}";
                            string field1 = $"81..00{(point.Easting < 0 ? "-" : "+")}{((long)(Math.Abs(point.Easting) * 1000)).ToString("D8")}";
                            string field2 = $"82..00{(point.Northing < 0 ? "-" : "+")}{((long)(Math.Abs(point.Northing) * 1000)).ToString("D8")}";
                            string field3 = $"83..00{(point.Elevation < 0 ? "-" : "+")}{((long)(Math.Abs(point.Elevation) * 1000)).ToString("D8")}";

                            string field4 = string.Empty;
                            if (!string.IsNullOrWhiteSpace(point.Description))
                            {
                                field4 = $" 41{point.Description}";
                            }

                            line = $"{field0} {field1} {field2} {field3}{field4}";
                        }

                        writer.WriteLine(line);
                    }
                }

                HistoryManager.Instance.AddRecord(defaultFileName, "Export", "GSI", pointsCount);
                System.Windows.MessageBox.Show("Points successfully exported to GSI file.", "Export Success", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error exporting GSI file: {ex.Message}", "Export Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

    }
}