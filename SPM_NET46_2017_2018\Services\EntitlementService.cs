﻿using System;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
//using Newtonsoft.Json;
using SPM_NET46_2017_2018.Models;

namespace SPM_NET46_2017_2018.Services
{
    public class EntitlementService
    {
        private const string BaseUrl = "https://apps.autodesk.com";
        private const string EndPoint = "webservices/checkentitlement";
        private static readonly HttpClient client = new HttpClient { BaseAddress = new Uri(BaseUrl) };

        public static async Task<EntitlementResponse> CheckEntitlementAsync(string userId, string appId)
        {
            try
            {
                ServicePointManager.ServerCertificateValidationCallback +=
                    (sender, certificate, chain, sslPolicyErrors) => true;

                string url = $"{EndPoint}?userid={Uri.EscapeDataString(userId)}&appid={Uri.EscapeDataString(appId)}";

                HttpResponseMessage response = await client.GetAsync(url);
                if (response.IsSuccessStatusCode)
                {
                    string jsonString = await response.Content.ReadAsStringAsync();
                    //return JsonConvert.DeserializeObject<EntitlementResponse>(jsonString);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error checking entitlement: {ex.Message}");
            }

            return new EntitlementResponse { IsValid = false, Message = "Failed to check entitlement" };
        }

        public static EntitlementResponse CheckEntitlement(string userId, string appId)
        {
            return CheckEntitlementAsync(userId, appId).GetAwaiter().GetResult();
        }
    }
}