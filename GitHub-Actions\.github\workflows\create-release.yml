name: 🎉 Create Release

on:
  push:
    tags:
      - 'v*'  # Triggers on version tags like v1.0.0, v2.1.0, etc.

jobs:
  create-release:
    runs-on: windows-latest
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
      
    - name: 🔧 Setup .NET 8
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '8.0.x'
        
    - name: 🔧 Setup MSBuild
      uses: microsoft/setup-msbuild@v2
      
    - name: 📦 Restore NuGet Packages
      run: nuget restore SurveyPointsManager.sln
        
    - name: 🏗️ Build All Projects
      run: |
        echo "🔨 Building all AutoCAD versions..."
        msbuild SurveyPointsManager.sln /p:Configuration=Release /p:Platform="Any CPU"
        
    - name: 📁 Create Release Bundle
      run: |
        echo "📁 Creating release bundle structure..."
        
        # Create bundle directories
        New-Item -ItemType Directory -Force -Path "Release/SurveyPointsManager.bundle/Contents/Windows/AutoCAD2015-2016_NETFramework45"
        New-Item -ItemType Directory -Force -Path "Release/SurveyPointsManager.bundle/Contents/Windows/AutoCAD2017-2018_NETFramework46"
        New-Item -ItemType Directory -Force -Path "Release/SurveyPointsManager.bundle/Contents/Windows/AutoCAD2019-2020_NETFramework47"
        New-Item -ItemType Directory -Force -Path "Release/SurveyPointsManager.bundle/Contents/Windows/AutoCAD2021-2024_NETFramework48"
        New-Item -ItemType Directory -Force -Path "Release/SurveyPointsManager.bundle/Contents/Windows/AutoCAD2025-2026_NET8"
        New-Item -ItemType Directory -Force -Path "Release/SurveyPointsManager.bundle/Contents/Resources"
        New-Item -ItemType Directory -Force -Path "Release/SurveyPointsManager.bundle/Contents/Help"
        
        # Copy PackageContents.xml
        Copy-Item "SurveyPointsManager.bundle/PackageContents.xml" "Release/SurveyPointsManager.bundle/"
        
        # Copy DLLs and rename to PointFlowCAD.dll
        Copy-Item "SPM_NET45_2015_2016/bin/Release/SPM_NET45_2015_2016.dll" "Release/SurveyPointsManager.bundle/Contents/Windows/AutoCAD2015-2016_NETFramework45/PointFlowCAD.dll"
        Copy-Item "SPM_NET46_2017_2018/bin/Release/SPM_NET46_2017_2018.dll" "Release/SurveyPointsManager.bundle/Contents/Windows/AutoCAD2017-2018_NETFramework46/PointFlowCAD.dll"
        Copy-Item "SPM_NET47_2019_2020/bin/Release/SPM_NET47_2019_2020.dll" "Release/SurveyPointsManager.bundle/Contents/Windows/AutoCAD2019-2020_NETFramework47/PointFlowCAD.dll"
        Copy-Item "SPM_NET48_2021_2024/bin/Release/SPM_NET48_2021_2024.dll" "Release/SurveyPointsManager.bundle/Contents/Windows/AutoCAD2021-2024_NETFramework48/PointFlowCAD.dll"
        Copy-Item "SPM_NET8_2025_2026/bin/Release/net8.0-windows/SPM_NET8_2025_2026.dll" "Release/SurveyPointsManager.bundle/Contents/Windows/AutoCAD2025-2026_NET8/PointFlowCAD.dll"
        
        # Copy dependencies
        Copy-Item "SPM_NET45_2015_2016/bin/Release/Newtonsoft.Json.dll" "Release/SurveyPointsManager.bundle/Contents/Windows/AutoCAD2015-2016_NETFramework45/" -ErrorAction SilentlyContinue
        Copy-Item "SPM_NET46_2017_2018/bin/Release/Newtonsoft.Json.dll" "Release/SurveyPointsManager.bundle/Contents/Windows/AutoCAD2017-2018_NETFramework46/" -ErrorAction SilentlyContinue
        Copy-Item "SPM_NET47_2019_2020/bin/Release/Newtonsoft.Json.dll" "Release/SurveyPointsManager.bundle/Contents/Windows/AutoCAD2019-2020_NETFramework47/" -ErrorAction SilentlyContinue
        Copy-Item "SPM_NET48_2021_2024/bin/Release/Newtonsoft.Json.dll" "Release/SurveyPointsManager.bundle/Contents/Windows/AutoCAD2021-2024_NETFramework48/" -ErrorAction SilentlyContinue
        Copy-Item "SPM_NET8_2025_2026/bin/Release/net8.0-windows/Newtonsoft.Json.dll" "Release/SurveyPointsManager.bundle/Contents/Windows/AutoCAD2025-2026_NET8/" -ErrorAction SilentlyContinue
        
    - name: 📦 Create Release Package
      run: |
        echo "📦 Creating release package..."
        
        # Create the main bundle zip
        Compress-Archive -Path "Release/SurveyPointsManager.bundle" -DestinationPath "SurveyPointsManager-${{ github.ref_name }}.zip" -Force
        
        # Create installation package with documentation
        New-Item -ItemType Directory -Force -Path "Installation"
        Copy-Item "SurveyPointsManager-${{ github.ref_name }}.zip" "Installation/"
        Copy-Item "README.md" "Installation/"
        Copy-Item "INSTALLATION_GUIDE.md" "Installation/"
        Copy-Item "USER_GUIDE.md" "Installation/"
        Copy-Item "CHANGELOG.md" "Installation/"
        Copy-Item "LICENSE" "Installation/"
        
        Compress-Archive -Path "Installation/*" -DestinationPath "SurveyPointsManager-Complete-${{ github.ref_name }}.zip" -Force
        
    - name: 📋 Generate Release Notes
      run: |
        echo "📋 Generating release notes..."
        $version = "${{ github.ref_name }}"
        $releaseNotes = @"
        # 🎉 Survey Points Manager $version
        
        ## 📦 What's Included
        - **SurveyPointsManager.bundle** - Ready-to-install AutoCAD plugin
        - **Complete Documentation** - Installation guide, user guide, changelog
        - **Multi-Version Support** - AutoCAD 2015-2026 compatibility
        
        ## ✅ Supported AutoCAD Versions
        - **AutoCAD 2015-2016** (.NET Framework 4.5)
        - **AutoCAD 2017-2018** (.NET Framework 4.6)
        - **AutoCAD 2019-2020** (.NET Framework 4.7)
        - **AutoCAD 2021-2024** (.NET Framework 4.8)
        - **AutoCAD 2025-2026** (.NET 8.0)
        
        ## 🚀 Installation
        1. Download **SurveyPointsManager-Complete-$version.zip**
        2. Extract the bundle to your AutoCAD plugins folder
        3. Restart AutoCAD
        4. Type **SURVEYPOINTMANAGER** to start
        
        ## 📚 Documentation
        - **README.md** - Overview and features
        - **INSTALLATION_GUIDE.md** - Step-by-step installation
        - **USER_GUIDE.md** - How to use the plugin
        - **CHANGELOG.md** - Version history
        
        ## 🔧 Technical Details
        - **Build Date:** $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
        - **Commit:** ${{ github.sha }}
        - **Automatic Version Detection:** Yes
        - **Bundle Format:** AutoCAD .bundle
        
        ## 📞 Support
        - **Email:** <EMAIL>
        - **Website:** https://ahmedalsayed.work/projects/survey-point-manager
        - **Company:** Pulsar Star
        
        ---
        *Built automatically with GitHub Actions* 🤖
        "@
        $releaseNotes | Out-File -FilePath "release-notes.md" -Encoding UTF8
        
    - name: 🎉 Create GitHub Release
      uses: softprops/action-gh-release@v1
      with:
        name: Survey Points Manager ${{ github.ref_name }}
        body_path: release-notes.md
        files: |
          SurveyPointsManager-${{ github.ref_name }}.zip
          SurveyPointsManager-Complete-${{ github.ref_name }}.zip
        draft: false
        prerelease: false
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        
    - name: 🎊 Success Notification
      run: |
        echo "🎊 SUCCESS! Release ${{ github.ref_name }} created successfully!"
        echo "📦 Bundle: SurveyPointsManager-${{ github.ref_name }}.zip"
        echo "📚 Complete Package: SurveyPointsManager-Complete-${{ github.ref_name }}.zip"
        echo "🚀 Ready for customer distribution!"
