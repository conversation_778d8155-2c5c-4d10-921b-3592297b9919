﻿using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.Colors;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using SPM_NET46_2017_2018.Managers;
using SPM_NET46_2017_2018.Models;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Media;



namespace SPM_NET46_2017_2018.Views
{
    /// <summary>
    /// A UserControl for managing and exporting survey points in various file formats.
    /// Provides functionality for point picking, management, and data export within AutoCAD.
    /// </summary>
    public partial class ExportPalette : UserControl
    {
        private readonly ExportViewModel _viewModel;
        private ICollectionView _pointsView; // WPF view with filter

        #region Constructors
        /// <summary>
        /// Initializes a new instance of the <see cref="ExportPalette"/> class.
        /// </summary>
        public ExportPalette()
        {
            InitializeComponent();
            _viewModel = new ExportViewModel();
            DataContext = _viewModel;


            // wrap the ObservableCollection in a filterable view
            _pointsView = CollectionViewSource.GetDefaultView(_viewModel.Points);
            _pointsView.Filter = FilterPoints;

            // Set up event handlers for point collection changes
            _viewModel.Points.CollectionChanged += Points_CollectionChanged;

            // Initial button state setup
            UpdateButtonStates();

            // Set up keyboard event handlers for the DataGrid
            PointsDataGrid.PreviewKeyDown += PointsDataGrid_PreviewKeyDown;

        }
        #endregion

        #region AutoCAD Properties
        /// <summary>
        /// Gets the currently active AutoCAD document.
        /// </summary>
        private Document ActiveDocument => Autodesk.AutoCAD.ApplicationServices.Application.DocumentManager.MdiActiveDocument;

        /// <summary>
        /// Gets the database of the currently active AutoCAD document.
        /// </summary>
        private Database ActiveDatabase => ActiveDocument?.Database;

        /// <summary>
        /// Gets the editor of the currently active AutoCAD document.
        /// </summary>
        private Editor ActiveEditor => ActiveDocument?.Editor;
        #endregion

        #region Point Counter Management

        /// <summary>
        /// Event handler for when the Points collection changes
        /// </summary>
        private void Points_CollectionChanged(object sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            // Update the point counter in the view model
            _viewModel.UpdatePointCounter();

            // Update button states whenever the collection changes
            UpdateButtonStates();
        }

        /// <summary>
        /// Event handler for keyboard events on the DataGrid to handle deletions
        /// </summary>
        private void PointsDataGrid_PreviewKeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            if (e.Key == System.Windows.Input.Key.Delete)
            {
                // Get selected items
                var selectedItems = PointsDataGrid.SelectedItems;
                if (selectedItems.Count > 0)
                {
                    var itemsToRemove = new List<SurveyPoint>();

                    // Create a list of items to remove (can't modify collection during enumeration)
                    foreach (SurveyPoint point in selectedItems)
                    {
                        itemsToRemove.Add(point);
                    }

                    // Add to undo stack before removing
                    _viewModel.AddToUndoStack(new List<SurveyPoint>(_viewModel.Points));

                    // Remove the items
                    foreach (var point in itemsToRemove)
                    {
                        _viewModel.Points.Remove(point);
                    }

                    // Update button states
                    UpdateButtonStates();
                }
            }
        }

        #endregion

        #region Point Picking Methods
        private bool _sessionStarted = false;


        #region Manual Point Picking
        // Global flag to track the picking state.
        private bool PickingStatus = false;

        private async void ManualPickButton_Checked(object sender, RoutedEventArgs e)
        {
            await Task.Yield(); // Ensures UI updates before proceeding
            Autodesk.AutoCAD.Internal.Utils.SetFocusToDwgView();
            PickingStatus = true;
            StartManualPickingPoints();
        }

        private async void ManualPickButton_Unchecked(object sender, RoutedEventArgs e)
        {
            await Task.Yield(); // Ensures UI updates before proceeding
            PickingStatus = false;
            this.Focus();
        }

        #region Manual Picking Helpers
        private void StartManualPickingPoints()
        {
            // Lock the document for the entire interactive process
            using (DocumentLock docLock = ActiveDocument.LockDocument())
            {
                // Ensure Object snapping is on and that both Point (Node) and Insertion snaps are enabled
                Helpers.DrawingHelper.EnsureOsnapNodeAndInsertion();

                // Create layers if they don’t exist
                using (Transaction trans = ActiveDatabase.TransactionManager.StartTransaction())
                {
                    Managers.LayerManager.CreateLayer(ActiveDatabase, trans, Properties.Settings.Default.PointLayer, Properties.Settings.Default.PointLayerColor);
                    Managers.LayerManager.CreateLayer(ActiveDatabase, trans, Properties.Settings.Default.PointNumberLayer, Properties.Settings.Default.PointNumberLayerColor);
                    trans.Commit();
                }

                // Set the point style once before picking
                Helpers.DrawingHelper.SetPointStyle(Properties.Settings.Default.PointDisplayImage, Properties.Settings.Default.PointSize);

                // Seed the counter once
                if (!_sessionStarted)
                {
                    if (!int.TryParse(StartingNumberTextBox.Text, out int seed) || seed < 1)
                    {
                        seed = 1;
                    }

                    _viewModel.SeedPointCounter(seed);
                    _sessionStarted = true;
                }

                // Continue picking points while the toggle is on
                while (PickingStatus)
                {
                    PromptPointOptions pointOptions = new PromptPointOptions("\nPick a point or press Enter to finish:");
                    PromptPointResult pointResult = ActiveEditor.GetPoint(pointOptions);

                    // Check if picking was cancelled
                    if (!PickingStatus)
                    {
                        break;
                    }

                    // Exit if point picking fails (e.g., Enter pressed)
                    if (pointResult.Status != PromptStatus.OK)
                    {
                        break;
                    }


                    // get the next number
                    int no = _viewModel.NextPointNumber();
                    // update the textbox so it shows the *next* one:
                    StartingNumberTextBox.Text = (no + 1).ToString();

                    // Process the picked point
                    Point3d pickedPoint = pointResult.Value;
                    double roundedX = Math.Round(pickedPoint.X, 3);
                    double roundedY = Math.Round(pickedPoint.Y, 3);
                    double roundedZ = Math.Round(pickedPoint.Z, 3);

                    //int pointNumber = _viewModel.Points.Count + 1;

                    SurveyPoint newPoint = new SurveyPoint(no.ToString(), roundedX, roundedY, roundedZ, "Picked Point");
                    _viewModel.Points.Add(newPoint);

                    Point3d pt = new Point3d(roundedX, roundedY, roundedZ);

                    // Draw the point and its number within a transaction
                    using (Transaction trans = ActiveDatabase.TransactionManager.StartTransaction())
                    {
                        Services.DrawingService.DrawPoint(ActiveDocument, pt, Properties.Settings.Default.PointLayer);
                        Services.DrawingService.DrawPointNumber(ActiveDocument, pt, newPoint.PointNumber, Properties.Settings.Default.PointNumberTextHeight, Properties.Settings.Default.PointNumberLayer);
                        trans.Commit();
                    }
                }
            }

            _sessionStarted = false;  // allow re-seeding next time

            // Reset the toggle button if checked
            if (ManualPickButton.IsChecked == true)
            {
                ManualPickButton.IsChecked = false;
            }
        }

        #endregion

        #endregion

        #region Automatic Point Picking
        private void AutoPickButton_Click(object sender, RoutedEventArgs e)
        {
            // 1. Prevent AutoPick if ManualPick is active.
            if (ManualPickButton.IsChecked == true)
            {
                MessageBox.Show("Manual picking is active. Please disable manual picking before auto picking.",
                                "Auto Pick", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }


            if (ActiveDocument == null)
            {
                MessageBox.Show("No active document found.", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }


            // 2. Ensure necessary layers exist.
            try
            {
                using (DocumentLock docLock = ActiveDocument.LockDocument())
                {
                    using (Transaction trans = ActiveDatabase.TransactionManager.StartTransaction())
                    {
                        Managers.LayerManager.CreateLayer(ActiveDatabase, trans, Properties.Settings.Default.PointLayer, Properties.Settings.Default.PointLayerColor);
                        Managers.LayerManager.CreateLayer(ActiveDatabase, trans, Properties.Settings.Default.PointNumberLayer, Properties.Settings.Default.PointNumberLayerColor);
                        trans.Commit();
                    }
                }
            }
            catch (Exception ex)
            {
                ActiveEditor.WriteMessage($"\nError creating layers: {ex.Message}");
                return;
            }

            // Ensure Object snapping is on and that both Point (Node) and Insertion snaps are enabled
            Helpers.DrawingHelper.EnsureOsnapNodeAndInsertion();

            // ① Seed the counter once
            if (!_sessionStarted)
            {
                if (!int.TryParse(StartingNumberTextBox.Text, out int seed) || seed < 1)
                {
                    seed = 1;
                }

                _viewModel.SeedPointCounter(seed);
                _sessionStarted = true;
            }


            // hide guide and show the options button
            AutoPickGuidance.Visibility = System.Windows.Visibility.Collapsed;
            AutoPickOptionsButton.Visibility = System.Windows.Visibility.Visible;

            // TODO: SET FOCUS AFTER FINISHING THE START POINT LOGIC!!
            // 3. Set focus to drawing model.
            Autodesk.AutoCAD.Internal.Utils.SetFocusToDwgView();

            // 4. Check for base point; if not provided, open the AutoPickOptionsPopup.
            if (string.IsNullOrWhiteSpace(StartingPointTextBox.Text))
            {
                AutoPickOptionsPopup.IsOpen = true;

                // focus on starting point text box
                StartingPointTextBox.Focus();

                ActiveEditor.WriteMessage("\nPlease enter a starting point before auto picking.");
                return;
            }

            // 5. Parse base point from StartingPointTextBox (expected format: "X,Y").
            Point3d basePoint;
            try
            {
                string[] coords = StartingPointTextBox.Text.Split(new[] { ',', ';' }, StringSplitOptions.RemoveEmptyEntries);
                if (coords.Length < 2 ||
                    !double.TryParse(coords[0].Trim(), NumberStyles.Any, CultureInfo.InvariantCulture, out double baseX) ||
                    !double.TryParse(coords[1].Trim(), NumberStyles.Any, CultureInfo.InvariantCulture, out double baseY))
                {
                    MessageBox.Show("Invalid starting point format. Please enter in the format: X,Y", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }
                basePoint = new Point3d(baseX, baseY, 0);
            }
            catch (Exception ex)
            {
                ActiveEditor.WriteMessage($"\nError parsing base point: {ex.Message}");
                return;
            }

            // 6. Prompt the user to select POINT entities.
            PromptSelectionOptions selOpts = new PromptSelectionOptions
            {
                MessageForAdding = "\nSelect points for stakeout: ",
                AllowDuplicates = false,
                SingleOnly = false
            };
            SelectionFilter filter = new SelectionFilter(new TypedValue[] { new TypedValue((int)DxfCode.Start, "POINT") });
            PromptSelectionResult selRes = ActiveEditor.GetSelection(selOpts, filter);
            if (selRes.Status != PromptStatus.OK)
            {
                ActiveEditor.WriteMessage("\nNo points selected.");
                return;
            }

            // 7. Retrieve selected DBPoints.
            List<DBPoint> selectedPoints = new List<DBPoint>();
            using (DocumentLock docLock = ActiveDocument.LockDocument())
            {
                using (Transaction trans = ActiveDatabase.TransactionManager.StartTransaction())
                {
                    foreach (SelectedObject selObj in selRes.Value)
                    {
                        if (selObj != null)
                        {
                            DBPoint pt = trans.GetObject(selObj.ObjectId, OpenMode.ForRead) as DBPoint;
                            if (pt != null)
                            {
                                selectedPoints.Add(pt);
                            }
                        }
                    }
                    trans.Commit();
                }
            }

            if (selectedPoints.Count == 0)
            {
                ActiveEditor.WriteMessage("\nNo points selected.");
                return;
            }

            // 8. Order points using KD-tree, starting from the base point.
            List<DBPoint> orderedPoints = new List<DBPoint>();
            try
            {
                // Initialize KD-tree with the selected points.
                var kdTree = new SPM_NET46_2017_2018.Helpers.KDTree(selectedPoints);
                DBPoint currentPoint = kdTree.NearestNeighbor(basePoint);
                orderedPoints.Add(currentPoint);
                selectedPoints.Remove(currentPoint);

                // Rebuild KD-tree and order remaining points.
                while (selectedPoints.Count > 0)
                {
                    kdTree = new SPM_NET46_2017_2018.Helpers.KDTree(selectedPoints);
                    currentPoint = kdTree.NearestNeighbor(currentPoint.Position);
                    if (currentPoint == null)
                    {
                        break;
                    }

                    orderedPoints.Add(currentPoint);
                    selectedPoints.Remove(currentPoint);
                }
            }
            catch (Exception ex)
            {
                ActiveEditor.WriteMessage($"\nError ordering points: {ex.Message}");
                return;
            }

            // 9. Apply point style.
            Helpers.DrawingHelper.SetPointStyle(Properties.Settings.Default.PointDisplayImage, Properties.Settings.Default.PointSize);
            Debug.WriteLine($"_pointStyle: {Properties.Settings.Default.PointDisplayImage}, _pointSize: {Properties.Settings.Default.PointSize}");

            // 10. Create new DBPoints and DBText entities and update the view model.
            try
            {
                // 10. Create new points and labels using helper methods and update the view model.
                using (DocumentLock docLock = ActiveDocument.LockDocument())
                {
                    using (Transaction trans = ActiveDatabase.TransactionManager.StartTransaction())
                    {
                        //int pointNumber = 1;

                        foreach (DBPoint pt in orderedPoints)
                        {
                            int no = _viewModel.NextPointNumber();
                            StartingNumberTextBox.Text = (no + 1).ToString();

                            Point3d pos = pt.Position;
                            double roundedX = Math.Round(pos.X, 3);
                            double roundedY = Math.Round(pos.Y, 3);
                            double roundedZ = Math.Round(pos.Z, 3);

                            // Draw the point on the specified layer using your helper.
                            Services.DrawingService.DrawPoint(ActiveDocument, new Point3d(roundedX, roundedY, roundedZ), Properties.Settings.Default.PointLayer);

                            // Draw the point number text using your helper.
                            Services.DrawingService.DrawPointNumber(ActiveDocument, new Point3d(roundedX, roundedY, roundedZ), no.ToString(), Properties.Settings.Default.PointNumberTextHeight, Properties.Settings.Default.PointNumberLayer);

                            // Update the view model so the DataGrid reflects the new point.
                            SurveyPoint sp = new SurveyPoint(no.ToString(), roundedX, roundedY, roundedZ, "Auto Picked");
                            _viewModel.Points.Add(sp);

                            //pointNumber++;
                        }

                        trans.Commit();
                    }

                    // ④ End session
                    _sessionStarted = false;
                }

                ActiveEditor.WriteMessage($"\nPoints successfully added: {orderedPoints.Count}");
                AutoPickOptionsPopup.IsOpen = false;
            }
            catch (Exception ex)
            {
                ActiveEditor.WriteMessage($"\nError creating points: {ex.Message}");
            }
        }

        private void AutoPickOptionsButton_Click(object sender, RoutedEventArgs e)
        {
            AutoPickOptionsPopup.IsOpen = true;
        }

        private async void PickStartingPointButton_Click(object sender, RoutedEventArgs e)
        {
            // Check if ActiveDocument is available
            if (ActiveDocument == null)
            {
                return;
            }

            // hide guide and show the options button
            AutoPickGuidance.Visibility = System.Windows.Visibility.Collapsed;
            AutoPickOptionsButton.Visibility = System.Windows.Visibility.Visible;

            // Close the popup
            AutoPickOptionsPopup.IsOpen = false;

            // Set focus to the drawing area
            Autodesk.AutoCAD.Internal.Utils.SetFocusToDwgView();

            // Set up the prompt options
            PromptPointOptions pointOpts = new PromptPointOptions("\nPick a starting point: ");
            PromptPointResult pointRes = ActiveEditor.GetPoint(pointOpts);

            // Handle the result of the point picking
            if (pointRes.Status == PromptStatus.OK)
            {
                Point3d pickedPoint = pointRes.Value;
                // Format the point coordinates as "X,Y" with three decimal places
                string formattedPoint = $"{Math.Round(pickedPoint.X, 3)},{Math.Round(pickedPoint.Y, 3)}";
                StartingPointTextBox.Text = formattedPoint;
            }
            else
            {
                ActiveEditor.WriteMessage("\nPoint picking cancelled or failed.");
            }

            // Wait briefly to let the UI settle
            await Task.Delay(100); // 100 milliseconds delay

            // Reopen the popup
            AutoPickOptionsPopup.IsOpen = true;
        }

        private void AutoPickPopupCancelButton_Click(object sender, RoutedEventArgs e)
        {
            AutoPickOptionsPopup.IsOpen = false;
        }
        #endregion

        #region Smart Point Picking

        private Point3d _startingPoint = new Point3d();
        private List<DBObject> _selectedBlocks = new List<DBObject>();
        private List<DBObject> _selectedTexts = new List<DBObject>();
        private const string HIDDEN_LAYER_NAME = "blocks1";


        /// <summary>
        /// Handles the click event for the Smart Pick button
        /// </summary>
        private void SmartPickButton_Click(object sender, RoutedEventArgs e)
        {
            // Ensure manual picking is not active
            if (ManualPickButton.IsChecked == true)
            {
                MessageBox.Show("Manual picking is active. Please disable manual picking before smart picking.", "Smart Pick", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // Ensure an active document exists
            if (ActiveDocument == null)
            {
                MessageBox.Show("No active document found. Please open a drawing first.", "Smart Pick", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // Hide guidance text and display the options button
            if (SmartPickGuidance != null)
            {
                SmartPickGuidance.Visibility = System.Windows.Visibility.Collapsed;
            }

            if (SmartPickOptionsButton != null)
            {
                SmartPickOptionsButton.Visibility = System.Windows.Visibility.Visible;
            }

            // Check the content of SmartStartingPointTextBox
            string input = SmartStartingPointTextBox.Text;
            if (string.IsNullOrWhiteSpace(input) || !TryParsePoint(input, out Point3d point))
            {
                // If empty or invalid, set the TextBox background to pale red and open the popup
                SmartStartingPointTextBox.Background = new SolidColorBrush(Colors.MediumVioletRed);
                SmartPickOptionsPopup.IsOpen = true;
            }
            else
            {
                // ① remember the picked base location
                _startingPoint = point;

                // ② seed the shared point‐counter once
                if (!_sessionStarted)
                {
                    if (!int.TryParse(StartingNumberTextBox.Text, out int seed) || seed < 1)
                    {
                        seed = 1;
                    }

                    _viewModel.SeedPointCounter(seed);
                    _sessionStarted = true;
                }

                // Ensure Object snapping is on and that both Point (Node) and Insertion snaps are enabled
                Helpers.DrawingHelper.EnsureOsnapNodeAndInsertion();

                SmartPickOptionsPopup.IsOpen = false;
                PromptForObjectSelection();
            }




        }

        private bool TryParsePoint(string input, out Point3d point)
        {
            point = new Point3d(); // Default value
            var parts = input.Split(new[] { ',', ' ' }, StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length == 2 &&
                double.TryParse(parts[0], out double x) &&
                double.TryParse(parts[1], out double y))
            {
                point = new Point3d(x, y, 0); // Corrected to include Z=0
                return true;
            }
            return false;
        }

        private void SmartPickOptionsButton_Click(object sender, RoutedEventArgs e)
        {
            SmartPickOptionsPopup.IsOpen = true;
        }

        private void SmartPickPopupCloseButton_Click(object sender, RoutedEventArgs e)
        {
            SmartPickOptionsPopup.IsOpen = false;
        }

        private async void SmartPickStartingPointButton_Click(object sender, RoutedEventArgs e)
        {
            if (ActiveDocument == null)
            {
                return;
            }

            // Close the popup during point picking.
            SmartPickOptionsPopup.IsOpen = false;

            // Set focus to the drawing
            Autodesk.AutoCAD.Internal.Utils.SetFocusToDwgView();

            // Prompt user to get the point (x,y)
            PromptPointOptions pointOpts = new PromptPointOptions("\nPick a starting point: ");
            PromptPointResult pointRes = ActiveEditor.GetPoint(pointOpts);
            if (pointRes.Status == PromptStatus.OK)
            {
                Point3d pickedPoint = pointRes.Value;
                string formattedPoint = $"{Math.Round(pickedPoint.X, 3)},{Math.Round(pickedPoint.Y, 3)}";

                // After pick the point put its value in the textbox
                SmartStartingPointTextBox.Text = formattedPoint;
            }
            else
            {
                ActiveEditor.WriteMessage("\nPoint picking cancelled or failed.");
            }

            // Brief delay to allow UI to settle, then reopen the popup.
            await Task.Delay(100);

            // Open the popup again
            SmartPickOptionsPopup.IsOpen = true;

        }



        private void SmartInsertPointsButton_Click(object sender, RoutedEventArgs e)
        {
            // close the popup
            SmartPickOptionsPopup.IsOpen = false;

            if (ActiveDocument == null)
            {
                return;
            }

            // Get the selection options (blocks, texts, or both)
            bool processBlocks = chkBlockSelection.IsChecked == true && _selectedBlocks.Count > 0;
            bool processTexts = chkTextSelection.IsChecked == true && _selectedTexts.Count > 0;

            if (!processBlocks && !processTexts)
            {
                return;
            }

            // Get the action to perform
            if (ObjectActionComboBox.SelectedItem == null)
            {
                MessageBox.Show("Please select an action to perform.", "Smart Pick", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // Get the action tag from the ComboBoxItem
            ComboBoxItem selectedItem = ObjectActionComboBox.SelectedItem as ComboBoxItem;
            string action = selectedItem != null ? selectedItem.Tag.ToString() : "keep";


            // TODO: Enable object snap for node/point
            using (Transaction tr = ActiveDocument.Database.TransactionManager.StartTransaction())
            {

                tr.Commit();
            }

            // Process the selected items
            try
            {
                // Set the point style to ensure consistency with manual picking
                Helpers.DrawingHelper.SetPointStyle(Properties.Settings.Default.PointDisplayImage, Properties.Settings.Default.PointSize);

                // Create a list to hold point locations
                List<Point3d> pointLocations = new List<Point3d>();
                List<ObjectId> pointIds = new List<ObjectId>();

                // Use one document lock for the entire operation
                using (DocumentLock docLock = ActiveDocument.LockDocument())
                {
                    // Use a single transaction for creating layers and points
                    using (Transaction trans = ActiveDatabase.TransactionManager.StartTransaction())
                    {
                        // Create or verify the point and hidden layers
                        Managers.LayerManager.CreateLayer(ActiveDatabase, trans, Properties.Settings.Default.PointLayer, Properties.Settings.Default.PointLayerColor);
                        Managers.LayerManager.CreateLayer(ActiveDatabase, trans, Properties.Settings.Default.PointNumberLayer, Properties.Settings.Default.PointNumberLayerColor);
                        Managers.LayerManager.CreateLayer(ActiveDatabase, trans, HIDDEN_LAYER_NAME, 1); // Hidden layer, color 1 (red)

                        // Get the model space block table record
                        BlockTable blockTable = trans.GetObject(ActiveDatabase.BlockTableId, OpenMode.ForRead) as BlockTable;
                        BlockTableRecord modelSpace = trans.GetObject(blockTable[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                        // Process blocks if selected
                        if (processBlocks)
                        {
                            foreach (DBObject obj in _selectedBlocks)
                            {
                                // Get fresh copy of the object from database - open for write directly
                                DBObject freshObj = trans.GetObject(obj.ObjectId, OpenMode.ForWrite);

                                if (freshObj is BlockReference blockRef)
                                {
                                    // Get the insertion point
                                    Point3d insertionPoint = blockRef.Position;
                                    pointLocations.Add(insertionPoint);

                                    // Create a point at the insertion point
                                    DBPoint dbPoint = new DBPoint(insertionPoint)
                                    {
                                        Layer = Properties.Settings.Default.PointLayer
                                    };
                                    modelSpace.AppendEntity(dbPoint);
                                    trans.AddNewlyCreatedDBObject(dbPoint, true);
                                    pointIds.Add(dbPoint.ObjectId);

                                    // Process the block based on selected action
                                    ProcessObject(blockRef, action);
                                }
                            }
                        }

                        // Process texts if selected
                        if (processTexts)
                        {
                            foreach (DBObject obj in _selectedTexts)
                            {
                                // Get fresh copy of the object from database - open for write directly
                                DBObject freshObj = trans.GetObject(obj.ObjectId, OpenMode.ForWrite);
                                Point3d insertionPoint;

                                if (freshObj is DBText text)
                                {
                                    insertionPoint = text.Position;
                                }
                                else if (freshObj is MText mtext)
                                {
                                    insertionPoint = mtext.Location;
                                }
                                else
                                {
                                    continue;
                                }

                                pointLocations.Add(insertionPoint);

                                // Create a point at the insertion point
                                DBPoint dbPoint = new DBPoint(insertionPoint)
                                {
                                    Layer = Properties.Settings.Default.PointLayer
                                };
                                modelSpace.AppendEntity(dbPoint);
                                trans.AddNewlyCreatedDBObject(dbPoint, true);
                                pointIds.Add(dbPoint.ObjectId);

                                // Process the text object based on selected action
                                ProcessObject(freshObj, action);
                            }
                        }

                        // Commit the transaction to save the created points
                        trans.Commit();
                    }

                    // Number the points with a new transaction
                    if (pointLocations.Count > 0)
                    {
                        using (Transaction numberTrans = ActiveDatabase.TransactionManager.StartTransaction())
                        {
                            // Number the points using KDTree with locations instead of DBPoint objects
                            NumberPointsUsingKDTree(numberTrans, pointLocations, pointIds);
                            numberTrans.Commit();
                        }
                    }
                }

                // 3) Reset the session flag so next call re-seeds from the TextBox
                _sessionStarted = false;

                // Refresh the drawing
                ActiveDocument.Editor.Regen();

                MessageBox.Show("Smart Pick completed successfully!", "Smart Pick", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"Error during Smart Pick: {ex.Message}", "Smart Pick", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        // Modified ProcessObject method to properly handle layer changes and explode blocks
        private void ProcessObject(DBObject obj, string actionTag)
        {
            try
            {
                Debug.WriteLine($"Processing object: {obj.GetType().Name}");
                Debug.WriteLine($"Action Tag: {actionTag}");

                switch (actionTag)
                {
                    case "delete":
                        obj.Erase(true);
                        break;

                    case "move_hidden":
                        // Ensure hidden layer exists
                        using (Transaction layerTrans = ActiveDatabase.TransactionManager.StartTransaction())
                        {
                            // Get or create the hidden layer
                            LayerTable lt = layerTrans.GetObject(ActiveDatabase.LayerTableId, OpenMode.ForRead) as LayerTable;
                            ObjectId layerId;
                            LayerTableRecord ltr;

                            if (!lt.Has(HIDDEN_LAYER_NAME))
                            {
                                // Create the layer if it doesn't exist
                                lt.UpgradeOpen();
                                ltr = new LayerTableRecord();
                                ltr.Name = HIDDEN_LAYER_NAME;
                                ltr.Color = Autodesk.AutoCAD.Colors.Color.FromColorIndex(ColorMethod.ByAci, 1); // Red

                                layerId = lt.Add(ltr);
                                layerTrans.AddNewlyCreatedDBObject(ltr, true);
                            }
                            else
                            {
                                layerId = lt[HIDDEN_LAYER_NAME];
                            }

                            BlockTableRecord btr = layerTrans.GetObject(ActiveDatabase.CurrentSpaceId, OpenMode.ForWrite) as BlockTableRecord;

                            if (obj is BlockReference blockRef)
                            {
                                // Explode the block
                                DBObjectCollection explodedEntities = new DBObjectCollection();
                                blockRef.Explode(explodedEntities);

                                // Add exploded entities to the database on the hidden layer
                                foreach (Entity explEnt in explodedEntities)
                                {
                                    explEnt.LayerId = layerId;
                                    btr.AppendEntity(explEnt);
                                    layerTrans.AddNewlyCreatedDBObject(explEnt, true);
                                }

                                // Erase the original block reference
                                blockRef.Erase();
                            }
                            else if (obj is Entity entity)
                            {
                                // For non-block entities, just change the layer
                                entity.LayerId = layerId;
                            }

                            layerTrans.Commit();
                        }
                        break;

                    case "keep":
                        // Do nothing
                        break;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error processing object: {ex.Message}");
                MessageBox.Show($"Error in ProcessObject: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }


        // Modified NumberPointsUsingKDTree to use Point3d and work within a transaction
        private void NumberPointsUsingKDTree(Transaction trans, List<Point3d> pointLocations, List<ObjectId> pointIds)
        {
            if (pointLocations == null || pointLocations.Count == 0)
            {
                return;
            }

            try
            {
                // Create a new ObservableCollection for the points
                var pointsCollection = new ObservableCollection<SurveyPoint>();

                // Get the block table record for adding text
                BlockTable blockTable = trans.GetObject(ActiveDatabase.BlockTableId, OpenMode.ForRead) as BlockTable;
                BlockTableRecord btr = trans.GetObject(blockTable[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

                // Build a simple KDTree-like structure for finding nearest points
                List<Point3d> remainingPoints = new List<Point3d>(pointLocations);
                List<ObjectId> remainingIds = new List<ObjectId>(pointIds);
                Point3d currentPoint = _startingPoint;

                //int pointNumber = 1;



                while (remainingPoints.Count > 0)
                {
                    // Find the index of the nearest point
                    int nearestIdx = 0;
                    double minDist = currentPoint.DistanceTo(remainingPoints[0]);

                    for (int i = 1; i < remainingPoints.Count; i++)
                    {
                        double dist = currentPoint.DistanceTo(remainingPoints[i]);
                        if (dist < minDist)
                        {
                            minDist = dist;
                            nearestIdx = i;
                        }
                    }

                    // Get the nearest point
                    Point3d nearestPoint = remainingPoints[nearestIdx];
                    ObjectId pointId = remainingIds[nearestIdx];

                    // ③ get next point number from the VM
                    int no = _viewModel.NextPointNumber();

                    StartingNumberTextBox.Text = (no + 1).ToString();

                    // Create and add the SurveyPoint directly to the collection
                    pointsCollection.Add(new SurveyPoint
                    {
                        PointNumber = no.ToString(),
                        Easting = Math.Round(nearestPoint.X, 3),
                        Northing = Math.Round(nearestPoint.Y, 3),
                        Elevation = Math.Round(nearestPoint.Z, 3),
                        Description = "Smart Pick"
                    });

                    // Draw point number
                    DBText text = new DBText
                    {
                        Position = nearestPoint,
                        TextString = no.ToString(),
                        Height = Properties.Settings.Default.PointNumberTextHeight,
                        Layer = Properties.Settings.Default.PointNumberLayer
                    };

                    btr.AppendEntity(text);
                    trans.AddNewlyCreatedDBObject(text, true);

                    // Remove the processed point and move to next
                    remainingPoints.RemoveAt(nearestIdx);
                    remainingIds.RemoveAt(nearestIdx);
                    currentPoint = nearestPoint;
                    //pointNumber++;
                }

                // Update the ViewModel with all points at once
                foreach (var point in pointsCollection)
                {
                    _viewModel.Points.Add(point);
                }

                _sessionStarted = false;
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"Error numbering points: {ex.Message}", "Smart Pick", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // Helper method to draw point number within a transaction
        private void DrawPointNumber(Document doc, Transaction trans, Point3d position, string number, double textHeight, string layerName)
        {
            BlockTable blockTable = trans.GetObject(doc.Database.BlockTableId, OpenMode.ForRead) as BlockTable;
            BlockTableRecord btr = trans.GetObject(blockTable[BlockTableRecord.ModelSpace], OpenMode.ForWrite) as BlockTableRecord;

            DBText text = new DBText();
            text.Position = position;
            text.TextString = number;
            text.Height = textHeight;
            text.Layer = layerName;

            btr.AppendEntity(text);
            trans.AddNewlyCreatedDBObject(text, true);
        }


        /// <summary>
        /// Prompts the user to select objects for Smart Pick
        /// </summary>
        private void PromptForObjectSelection()
        {
            if (ActiveDocument == null)
            {
                return;
            }

            // Clear previous selections
            _selectedBlocks.Clear();
            _selectedTexts.Clear();

            try
            {
                // Set up selection options
                PromptSelectionOptions opts = new PromptSelectionOptions();
                opts.MessageForAdding = "\nSelect blocks and text objects: ";
                opts.AllowDuplicates = false;

                // Get the selection from user
                PromptSelectionResult result = ActiveEditor.GetSelection(opts);

                if (result.Status != PromptStatus.OK)
                {
                    return;
                }

                SelectionSet selSet = result.Value;

                using (Transaction trans = ActiveDatabase.TransactionManager.StartTransaction())
                {
                    // Process the selected objects
                    foreach (SelectedObject selObj in selSet)
                    {
                        DBObject obj = trans.GetObject(selObj.ObjectId, OpenMode.ForRead);

                        if (obj is BlockReference)
                        {
                            _selectedBlocks.Add(obj);
                        }
                        else if (obj is DBText || obj is MText)
                        {
                            _selectedTexts.Add(obj);
                        }
                    }

                    trans.Commit();
                }


                SelectionResultSection.Visibility = System.Windows.Visibility.Visible;

                // Set checkbox visibility based on list counts
                chkBlockSelection.Visibility = _selectedBlocks.Count > 0 ? System.Windows.Visibility.Visible : System.Windows.Visibility.Collapsed;
                chkTextSelection.Visibility = _selectedTexts.Count > 0 ? System.Windows.Visibility.Visible : System.Windows.Visibility.Collapsed;

                // Check the boxes by default
                chkBlockSelection.IsChecked = _selectedBlocks.Count > 0;
                chkTextSelection.IsChecked = _selectedTexts.Count > 0;

                // Show counts
                // Set checkbox text with counts
                chkBlockSelection.Content = $"{_selectedBlocks.Count} Blocks";
                chkTextSelection.Content = $"{_selectedTexts.Count} Texts";

                // Open the options popup
                SmartPickOptionsPopup.IsOpen = true;

                // Enable the insert points button if any items were selected
                SmartInsertPointsButton.IsEnabled = (_selectedBlocks.Count > 0 || _selectedTexts.Count > 0) && (chkBlockSelection.IsChecked == true || chkTextSelection.IsChecked == true);



            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"Error during selection: {ex.Message}", "Smart Pick", MessageBoxButton.OK, MessageBoxImage.Error);
            }

        }

        #endregion

        #endregion


        #region Export Methods
        /*

        Recommended Flow for Large Exports:

        - User selects format.
        - User selects output folder & filename.
        - Export process runs and saves directly.
        - Optionally, open the file after export.

        */

        private void ExportFileFormatComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (ExportFileFormatComboBox.SelectedItem is ComboBoxItem selectedItem && selectedItem.Tag != null)
            {
                string selectedExtension = selectedItem.Tag.ToString();
                ToggleExportSettingsVisibility(selectedExtension);
            }
        }

        /// <summary>
        /// Shows or hides specific setting panels based on the selected export file format.
        /// </summary>
        private void ToggleExportSettingsVisibility(string extension)
        {
            if (ExEmptySettings != null)
            {
                ExEmptySettings.Visibility = System.Windows.Visibility.Collapsed;
            }

            if (ExCSVSettings != null)
            {
                ExCSVSettings.Visibility = System.Windows.Visibility.Collapsed;
            }

            if (ExKMLSettings != null)
            {
                ExKMLSettings.Visibility = System.Windows.Visibility.Collapsed;
            }

            if (ExGSISettings != null)
            {
                ExGSISettings.Visibility = System.Windows.Visibility.Collapsed;
            }

            switch (extension.ToLower())
            {
                case ".csv":
                case ".txt":
                    if (ExCSVSettings != null)
                    {
                        ExCSVSettings.Visibility = System.Windows.Visibility.Visible;
                    }

                    break;
                case ".kml":
                    if (ExKMLSettings != null)
                    {
                        ExKMLSettings.Visibility = System.Windows.Visibility.Visible;
                    }

                    break;
                case ".gsi":
                    if (ExGSISettings != null)
                    {
                        ExGSISettings.Visibility = System.Windows.Visibility.Visible;
                    }

                    break;
                default:
                    if (ExEmptySettings != null)
                    {
                        ExEmptySettings.Visibility = System.Windows.Visibility.Visible;
                    }

                    break;
            }
        }

        private void CloseExportPopupButton_Click(object sender, RoutedEventArgs e)
        {
            ExportDialogPopup.IsOpen = false;
        }

        private void OpenExportDialogButton_Click(object sender, RoutedEventArgs e)
        {
            ExportDialogPopup.IsOpen = true;
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            if (ExportFileFormatComboBox.SelectedItem is ComboBoxItem selectedItem && selectedItem.Tag != null)
            {
                string fileFormat = selectedItem.Tag.ToString().ToLower();
                HandleExport(fileFormat);
            }
            else
            {
                System.Windows.MessageBox.Show("Please select an export file format.", "Export Error", MessageBoxButton.OK, MessageBoxImage.Warning);
            }

        }

        private void HandleExport(string fileFormat)
        {
            switch (fileFormat)
            {
                case ".csv":
                    ExportCSVPoints();
                    break;
                case ".txt":
                    ExportTXTPoints();
                    break;
                case ".kml":
                    ExportKMLPoints();
                    break;
                case ".sdr":
                    ExportSDRPoints();
                    break;
                case ".idx":
                    ExportIDXPoints();
                    break;
                case ".gsi":
                case ".gsi-8":
                case ".gsi-16":
                    ExportGSIPoints();
                    break;
                default:
                    System.Windows.MessageBox.Show("Unsupported file type selected.", "Export Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    break;
            }
        }

        private void ExportTXTPoints()
        {
            bool includeHeaders = FileHasHeadersCheckBox.IsChecked == true;
            bool includePointNumber = PointsHaveNumberCheckBox.IsChecked == true;
            bool isXYOrder = XYRadioButton.IsChecked == true;

            Services.ExportService.ExportTXTPoints(
                _viewModel.Points,
                includeHeaders,
                includePointNumber,
                isXYOrder);
        }

        private void ExportCSVPoints()
        {
            bool includeHeaders = FileHasHeadersCheckBox.IsChecked == true;
            bool includePointNumber = PointsHaveNumberCheckBox.IsChecked == true;
            bool isXYOrder = XYRadioButton.IsChecked == true;

            Services.ExportService.ExportCSVPoints(
                _viewModel.Points,
                includeHeaders,
                includePointNumber,
                isXYOrder);
        }

        private void ExportKMLPoints()
        {
            // Retrieve selected zone and hemisphere safely
            string selectedZone = (ZoneCB.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "Not Selected";
            int zone = int.TryParse(selectedZone, out int parsedZone) ? parsedZone : 0;
            string selectedHemisphere = (HemisphereCB.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "North";
            bool isNorthernHemisphere = selectedHemisphere == "North";

            Services.ExportService.ExportKMLPoints(
                _viewModel.Points,
                zone,
                isNorthernHemisphere);
        }

        private void ExportSDRPoints()
        {
            Services.ExportService.ExportSDRPoints(_viewModel.Points);
        }

        private void ExportIDXPoints()
        {
            Services.ExportService.ExportIDXPoints(_viewModel.Points);
        }

        private void ExportGSIPoints()
        {
            Services.ExportService.ExportGSIPoints(_viewModel.Points, GSIFormatCB.SelectedItem as ComboBoxItem);
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Validates that the active document and editor are available.
        /// </summary>
        /// <returns>True if both are available; otherwise, false.</returns>
        private bool IsAutoCadReady()
        {
            if (ActiveDocument == null || ActiveEditor == null)
            {
                MessageBox.Show("No active AutoCAD document found.", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
            return true;
        }













        #endregion

        #region Undo/Redo Functions

        /// <summary>
        /// Updates the enabled states of Undo, Redo, and Delete All buttons based on current state
        /// </summary>
        private void UpdateButtonStates()
        {
            // Enable/disable undo button based on stack state
            UndoBTN.IsEnabled = _viewModel.CanUndo;

            // Enable/disable redo button based on stack state
            RedoBTN.IsEnabled = _viewModel.CanRedo;

            // Enable/disable delete all button based on if there are points
            DeleteAllPointsBTN.IsEnabled = _viewModel.Points.Count > 0;
        }

        /// <summary>
        /// Handles the delete all points button click event
        /// </summary>
        private void DeleteAllPointsBTN_Click(object sender, RoutedEventArgs e)
        {
            if (_viewModel.Points.Count > 0)
            {
                // Store current state for undo
                _viewModel.AddToUndoStack(new List<SurveyPoint>(_viewModel.Points));

                // Clear all points
                _viewModel.Points.Clear();

                // Update button states
                UpdateButtonStates();
            }
        }

        // Fix for UndoBTN_Click method
        private void UndoBTN_Click(object sender, RoutedEventArgs e)
        {
            if (_viewModel.CanUndo)  // Remove the parentheses
            {
                // Store current state for redo
                _viewModel.AddToRedoStack(new List<SurveyPoint>(_viewModel.Points));

                // Restore points from undo stack
                var previousPoints = _viewModel.Undo();

                _viewModel.Points.Clear();
                foreach (var point in previousPoints)
                {
                    _viewModel.Points.Add(point);
                }

                // Update button states
                UpdateButtonStates();
            }
        }

        // Fix for RedoBTN_Click method
        private void RedoBTN_Click(object sender, RoutedEventArgs e)
        {
            if (_viewModel.CanRedo)  // Remove the parentheses
            {
                // Store current state for undo
                _viewModel.AddToUndoStack(new List<SurveyPoint>(_viewModel.Points));

                // Restore points from redo stack
                var nextPoints = _viewModel.Redo();

                _viewModel.Points.Clear();
                foreach (var point in nextPoints)
                {
                    _viewModel.Points.Add(point);
                }

                // Update button states
                UpdateButtonStates();
            }
        }

        #endregion

        #region Drawing Methods
        /// <summary>
        /// Handles drawing the points table in the AutoCAD model space.
        /// Retrieves both the header texts and point data directly from the DataGrid.
        /// </summary>
        private void DrawPointsTableBTN_Click(object sender, RoutedEventArgs e)
        {
            // Build a list of header strings from the DataGrid's columns.
            var headers = new List<string>();
            foreach (DataGridColumn column in PointsDataGrid.Columns)
            {
                if (column.Header != null)
                {
                    headers.Add(column.Header.ToString());
                }
            }

            // Build a list of row strings from the DataGrid's items.
            var pointsList = new List<string>();
            foreach (var item in PointsDataGrid.Items)
            {
                if (item is SurveyPoint point)
                {
                    // Format each SurveyPoint as a comma-separated string.
                    string rowData = $"{point.PointNumber}, {point.Easting}, {point.Northing}, {point.Elevation}, {point.Description}";
                    pointsList.Add(rowData);
                }
            }

            if (pointsList.Count == 0)
            {
                MessageBox.Show("No points to draw. Please import points first.");
                return;
            }

            // Create an instance of TableManager
            TableManager tableManager = new TableManager();

            // Call the method to create the AutoCAD table
            tableManager.CreateAutoCADTable(pointsList, headers, maxRowsPerTable: Properties.Settings.Default.MaxRowPerTable);
        }


        #endregion

        #region Search Points
        /// <summary>
        /// Called on every change in the search box.
        /// Refreshes the view filter.
        /// </summary>
        private void SearchPointsTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            _pointsView.Refresh();
        }

        /// <summary>
        /// Handles changes to the StartingNumberTextBox to update the point counter.
        /// This ensures that when the user manually changes the starting number,
        /// the next picked points will continue from that number.
        /// </summary>
        private void StartingNumberTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // Only update if we have a valid integer and the session has started
            if (int.TryParse(StartingNumberTextBox.Text, out int newStartingNumber) && newStartingNumber >= 1)
            {
                // Update the point counter to reflect the new starting number
                // Subtract 1 because NextPointNumber() will increment it
                _viewModel.PointCounter = newStartingNumber - 1;
            }
        }

        /// <summary>
        /// The actual predicate used by the CollectionView.
        /// Returns true if the point matches the search term or if no term specified.
        /// </summary>
        private bool FilterPoints(object obj)
        {
            if (obj is SurveyPoint pt)
            {
                var text = SearchPointsTextBox.Text?.Trim();
                if (string.IsNullOrEmpty(text))
                {
                    return true;
                }

                text = text.ToLowerInvariant();

                return pt.PointNumber?.ToLowerInvariant().Contains(text) == true
                    || pt.Description?.ToLowerInvariant().Contains(text) == true
                    || pt.Easting.ToString(CultureInfo.InvariantCulture).Contains(text)
                    || pt.Northing.ToString(CultureInfo.InvariantCulture).Contains(text)
                    || pt.Elevation.ToString(CultureInfo.InvariantCulture).Contains(text);
            }
            return false;
        }
        #endregion

    }


    /// <summary>
    /// ViewModel for managing the exported survey points collection.
    /// Contains properties for the exported points, an up‑to‑date point counter,
    /// and undo/redo functionality specific to export operations.
    /// </summary>
    public class ExportViewModel : INotifyPropertyChanged
    {
        private int _pointCounter;
        private readonly Stack<List<SurveyPoint>> _undoStack;
        private readonly Stack<List<SurveyPoint>> _redoStack;

        /// <summary>
        /// Gets the collection of exported survey points.
        /// </summary>
        public ObservableCollection<SurveyPoint> Points { get; }

        /// <summary>
        /// Gets or sets the current point counter.
        /// </summary>
        public int PointCounter
        {
            get => _pointCounter;
            set
            {
                if (_pointCounter != value)
                {
                    _pointCounter = value;
                    OnPropertyChanged(nameof(PointCounter));
                }
            }
        }


        /// <summary>
        /// Initialize the counter from some starting value.
        /// Call this exactly once at the start of a picking session.
        /// </summary>
        public void SeedPointCounter(int startValue)
        {
            PointCounter = startValue - 1;
        }

        /// <summary>
        /// Advance the counter by 1 and return the new value.
        /// </summary>
        public int NextPointNumber()
        {
            return ++PointCounter;
        }

        /// <summary>
        /// Gets a value indicating whether undo operations are available.
        /// </summary>
        public bool CanUndo
        {
            get => _undoStack.Count > 0;
        }

        /// <summary>
        /// Gets a value indicating whether redo operations are available.
        /// </summary>
        public bool CanRedo
        {
            get => _redoStack.Count > 0;
        }

        /// <summary>
        /// Initializes a new instance of the ExportViewModel class.
        /// </summary>
        public ExportViewModel()
        {
            Points = new ObservableCollection<SurveyPoint>();
            _undoStack = new Stack<List<SurveyPoint>>();
            _redoStack = new Stack<List<SurveyPoint>>();
            _pointCounter = Points.Count;

            // Automatically update the point counter when the Points collection changes.
            Points.CollectionChanged += (s, e) => UpdatePointCounter();
        }

        /// <summary>
        /// Updates the point counter based on the number of items in the Points collection.
        /// </summary>
        public void UpdatePointCounter()
        {
            PointCounter = Points.Count;
        }

        #region Undo/Redo Operations

        /// <summary>
        /// Adds the current list of points to the undo stack.
        /// </summary>
        /// <param name="points">The current state of exported points.</param>
        public void AddToUndoStack(List<SurveyPoint> points)
        {
            _undoStack.Push(points);
            // Clear the redo stack when a new action is performed
            _redoStack.Clear();

            // Notify that CanUndo and CanRedo properties have changed
            OnPropertyChanged(nameof(CanUndo));
            OnPropertyChanged(nameof(CanRedo));
        }

        /// <summary>
        /// Adds the current list of points to the redo stack.
        /// </summary>
        /// <param name="points">The current state of exported points.</param>
        public void AddToRedoStack(List<SurveyPoint> points)
        {
            _redoStack.Push(points);
            OnPropertyChanged(nameof(CanRedo));
        }

        /// <summary>
        /// Retrieves the previous state from the undo stack.
        /// </summary>
        /// <returns>A list of SurveyPoint objects representing the previous state.</returns>
        public List<SurveyPoint> Undo()
        {
            if (_undoStack.Any())
            {
                OnPropertyChanged(nameof(CanUndo));
                return _undoStack.Pop();

            }
            return new List<SurveyPoint>();
        }

        /// <summary>
        /// Retrieves the next state from the redo stack.
        /// </summary>
        /// <returns>A list of SurveyPoint objects representing the next state.</returns>
        public List<SurveyPoint> Redo()
        {
            if (_redoStack.Any())
            {
                OnPropertyChanged(nameof(CanRedo));
                return _redoStack.Pop();
            }
            return new List<SurveyPoint>();
        }


        /// <summary>
        /// Clears the redo stack; should be called when a new export operation is performed.
        /// </summary>
        public void ClearRedoStack()
        {
            _redoStack.Clear();
            OnPropertyChanged(nameof(CanRedo));
        }

        #endregion

        #region INotifyPropertyChanged Implementation

        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

}
