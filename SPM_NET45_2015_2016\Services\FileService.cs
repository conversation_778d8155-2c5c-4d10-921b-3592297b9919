﻿using SPM_NET45_2015_2016.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;

namespace SPM_NET45_2015_2016.Services
{
    public class FileService
    {
        public async Task<List<SurveyPoint>> ImportPointsAsync(string filePath, ImportSettings settings)
        {
            if (!File.Exists(filePath))
                throw new FileNotFoundException("The specified file was not found.", filePath);

            string extension = Path.GetExtension(filePath).ToLower();

            try
            {
                switch (extension)
                {
                    case ".csv":
                        return await ImportCsvAsync(filePath, settings);
                    // Add other formats here later
                   
                    default:
                        throw new NotSupportedException($"File format {extension} is not supported.");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error importing file: {ex.Message}", "Import Error", MessageBoxButton.OK, MessageBoxImage.Error);
                return new List<SurveyPoint>();
            }
        }

        private async Task<List<SurveyPoint>> ImportCsvAsync(string filePath, ImportSettings settings)
        {
            return await Task.Run(() =>
            {
                var points = new List<SurveyPoint>();
                var lines = File.ReadAllLines(filePath);

                if (settings.HasHeaders && lines.Any())
                    lines = lines.Skip(1).ToArray();

                foreach (var line in lines)
                {
                    if (string.IsNullOrWhiteSpace(line)) continue;

                    var values = line.Split(settings.Delimiter);
                    if (values.Length < 2) continue;

                    try
                    {
                        var point = new SurveyPoint
                        {
                            PointNumber = settings.HasPointNumbers && values.Length > settings.PointNumberIndex
                                ? values[settings.PointNumberIndex].Trim()
                                : string.Empty,
                            Easting = settings.IsYXOrder
                                ? Convert.ToDouble(values[settings.NorthingIndex])
                                : Convert.ToDouble(values[settings.EastingIndex]),
                            Northing = settings.IsYXOrder
                                ? Convert.ToDouble(values[settings.EastingIndex])
                                : Convert.ToDouble(values[settings.NorthingIndex]),
                            Elevation = values.Length > settings.ElevationIndex
                                ? Convert.ToDouble(values[settings.ElevationIndex])
                                : 0,
                            Description = values.Length > settings.DescriptionIndex
                                ? values[settings.DescriptionIndex].Trim()
                                : string.Empty
                        };
                        points.Add(point);
                    }
                    catch
                    {
                        // Skip invalid lines
                        continue;
                    }
                }
                return points;
            });
        }
    }

    public class ImportSettings
    {
        public bool HasHeaders { get; set; }
        public bool HasPointNumbers { get; set; }
        public bool IsYXOrder { get; set; }
        public char Delimiter { get; set; } = ',';
        public int PointNumberIndex { get; set; } = 0;
        public int EastingIndex { get; set; } = 1;
        public int NorthingIndex { get; set; } = 2;
        public int ElevationIndex { get; set; } = 3;
        public int DescriptionIndex { get; set; } = 4;
    }
}