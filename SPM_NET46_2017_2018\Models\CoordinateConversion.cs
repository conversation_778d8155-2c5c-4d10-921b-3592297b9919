using System;

namespace SPM_NET46_2017_2018.Models
{
    public static class CoordinateConversion
    {
        /// <summary>
        /// Convert UTM to Decimal Degrees
        /// </summary>
        /// <param name="latitude"></param>
        /// <param name="longitude"></param>
        /// <param name="easting"></param>
        /// <param name="northing"></param>
        /// <param name="zone"></param>
        public static void DecimalDegreesToUTM(double latitude, double longitude, out double easting, out double northing, out int zone)
        {
            // Constants
            double a = 6378137.0;  // WGS84 major axis
            double f = 1 / 298.257223563;  // WGS84 flattening
            double k0 = 0.9996;  // scale factor
            double e = Math.Sqrt(f * (2 - f));  // eccentricity

            // Calculate UTM Zone
            zone = (int)((longitude + 180) / 6) + 1;

            // Calculate the central meridian for the zone
            double lambda0 = (zone - 1) * 6 - 180 + 3;  // degrees
            lambda0 = DegreesToRadians(lambda0);  // convert to radians

            // Convert latitude and longitude to radians
            double phi = DegreesToRadians(latitude);
            double lambda = DegreesToRadians(longitude);

            // Calculate the parameters
            double N = a / Math.Sqrt(1 - e * e * Math.Sin(phi) * Math.Sin(phi));
            double T = Math.Tan(phi) * Math.Tan(phi);
            double C = (e * e / (1 - e * e)) * Math.Cos(phi) * Math.Cos(phi);
            double A = Math.Cos(phi) * (lambda - lambda0);

            // UTM Coordinates (Easting, Northing)
            double M = a * ((1 - e * e / 4 - 3 * e * e * e * e / 64 - 5 * e * e * e * e * e * e / 256) * phi
                 - (3 * e * e / 8 + 3 * e * e * e * e / 32 + 45 * e * e * e * e * e * e / 1024) * Math.Sin(2 * phi)
                 + (15 * e * e * e * e / 256 + 45 * e * e * e * e * e * e / 1024) * Math.Sin(4 * phi)
                 - (35 * e * e * e * e * e * e / 3072) * Math.Sin(6 * phi));

            easting = k0 * N * (A + (1 - T + C) * Math.Pow(A, 3) / 6 + (5 - 18 * T + T * T + 72 * C - 58 * e * e) * Math.Pow(A, 5) / 120) + 500000;
            northing = k0 * (M + N * Math.Tan(phi) * (Math.Pow(A, 2) / 2 + (5 - T + 9 * C + 4 * C * C) * Math.Pow(A, 4) / 24 + (61 - 58 * T + T * T + 600 * C - 330 * e * e) * Math.Pow(A, 6) / 720));

            if (latitude < 0)
            {
                northing += 10000000;  // 10000000 meter offset for southern hemisphere
            }

            // Round to two decimal places
            easting = Math.Round(easting, 2);
            northing = Math.Round(northing, 2);
        }

        /// <summary>
        /// Convert Decimal Degrees to UTM
        /// </summary>
        /// <param name="easting"></param>
        /// <param name="northing"></param>
        /// <param name="zone"></param>
        /// <param name="isNorthernHemisphere"></param>
        /// <param name="latitude"></param>
        /// <param name="longitude"></param>
        public static void UTMToDecimalDegrees(double easting, double northing, int zone, bool isNorthernHemisphere, out double latitude, out double longitude)
        {
            // Constants
            double a = 6378137.0;  // WGS84 major axis
            double f = 1 / 298.257223563;  // WGS84 flattening
            double k0 = 0.9996;  // scale factor
            double e = Math.Sqrt(f * (2 - f));  // eccentricity
            double e1sq = e * e / (1 - e * e);

            // Calculate the central meridian of the UTM zone
            double lambda0 = (zone - 1) * 6 - 180 + 3;  // degrees
            lambda0 = DegreesToRadians(lambda0);  // convert to radians

            // Remove the 500,000 meter offset for the central meridian
            double x = easting - 500000.0;
            double y = northing;

            // If in southern hemisphere, remove 10,000,000 meter offset
            if (!isNorthernHemisphere)
            {
                y -= 10000000.0;
            }

            // Calculate footpoint latitude
            double M = y / k0;
            double mu = M / (a * (1 - e * e / 4 - 3 * e * e * e * e / 64 - 5 * e * e * e * e * e * e / 256));

            double e1 = (1 - Math.Sqrt(1 - e * e)) / (1 + Math.Sqrt(1 - e * e));

            double J1 = (3 * e1 / 2 - 27 * e1 * e1 * e1 / 32);
            double J2 = (21 * e1 * e1 / 16 - 55 * e1 * e1 * e1 * e1 / 32);
            double J3 = (151 * e1 * e1 * e1 / 96);
            double J4 = (1097 * e1 * e1 * e1 * e1 / 512);

            double fp = mu + J1 * Math.Sin(2 * mu) + J2 * Math.Sin(4 * mu) + J3 * Math.Sin(6 * mu) + J4 * Math.Sin(8 * mu);

            // Calculate latitude and longitude
            double C1 = e1sq * Math.Cos(fp) * Math.Cos(fp);
            double T1 = Math.Tan(fp) * Math.Tan(fp);
            double R1 = a * (1 - e * e) / Math.Pow(1 - e * e * Math.Sin(fp) * Math.Sin(fp), 1.5);
            double N1 = a / Math.Sqrt(1 - e * e * Math.Sin(fp) * Math.Sin(fp));
            double D = x / (N1 * k0);

            latitude = fp - (N1 * Math.Tan(fp) / R1) * (D * D / 2 - (5 + 3 * T1 + 10 * C1 - 4 * C1 * C1 - 9 * e1sq) * Math.Pow(D, 4) / 24
                  + (61 + 90 * T1 + 298 * C1 + 45 * T1 * T1 - 252 * e1sq - 3 * C1 * C1) * Math.Pow(D, 6) / 720);
            latitude = RadiansToDegrees(latitude);

            longitude = lambda0 + (D - (1 + 2 * T1 + C1) * Math.Pow(D, 3) / 6
                  + (5 - 2 * C1 + 28 * T1 - 3 * C1 * C1 + 8 * e1sq + 24 * T1 * T1) * Math.Pow(D, 5) / 120) / Math.Cos(fp);
            longitude = RadiansToDegrees(longitude);

            // Round to six decimal places
            latitude = Math.Round(latitude, 6);
            longitude = Math.Round(longitude, 6);
        }

        private static double DegreesToRadians(double degrees)
        {
            return degrees * Math.PI / 180.0;
        }

        private static double RadiansToDegrees(double radians)
        {
            return radians * 180.0 / Math.PI;
        }


    }
}
