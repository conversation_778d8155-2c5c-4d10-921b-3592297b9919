﻿using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.Colors;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SPM_NET8_2025_2026.Managers
{
    public class TableManager
    {
        #region AutoCAD Shortcut Properties
        /// <summary>
        /// Gets the currently active AutoCAD document.
        /// </summary>
        private Document ActiveDocument => Autodesk.AutoCAD.ApplicationServices.Application.DocumentManager.MdiActiveDocument;

        /// <summary>
        /// Gets the database of the active document.
        /// </summary>
        private Database ActiveDatabase => ActiveDocument?.Database;

        /// <summary>
        /// Gets the editor of the active document.
        /// </summary>
        private Editor ActiveEditor => ActiveDocument?.Editor;
        #endregion

        /// <summary>
        /// Creates an AutoCAD table in the drawing based on the provided headers and point data.
        /// The point data is assumed to be a list of comma-separated strings (data rows only),
        /// while headers are provided separately. Table columns to include are determined by the
        /// app settings (ShowPointNumber, ShowElevation, ShowDescription).
        /// </summary>
        /// <param name="pointsData">List of data rows (no header row)</param>
        /// <param name="headers">List of header strings (expected order: PointNumber, Easting, Northing, Elevation, Description)</param>
        /// <param name="maxRowsPerTable">Maximum data rows per table</param>
        public void CreateAutoCADTable(List<string> pointsData, List<string> headers, int maxRowsPerTable = 10)
        {
            PromptPointResult promptResult = ActiveEditor.GetPoint("\nEnter table insertion point: ");
            if (promptResult.Status != PromptStatus.OK)
            {
                return;
            }

            Point3d insertionPoint = promptResult.Value;
            bool showTitle = Properties.Settings.Default.ShowTableTitle; // Boolean flag

            // Determine which columns to include.
            List<int> indicesToInclude = new List<int>();
            if (Properties.Settings.Default.ShowPointNumber)
            {
                indicesToInclude.Add(0);
            }

            indicesToInclude.Add(1); // Easting is always included
            indicesToInclude.Add(2); // Northing is always included
            if (Properties.Settings.Default.ShowElevation)
            {
                indicesToInclude.Add(3);
            }

            if (Properties.Settings.Default.ShowDescription)
            {
                indicesToInclude.Add(4);
            }

            // Filter headers accordingly.
            List<string> filteredHeaders = indicesToInclude
                .Select(idx => idx < headers.Count ? headers[idx] : "")
                .ToList();

            // Adjust each point data row to include only the columns from indicesToInclude.
            List<string> filteredPointsData = new List<string>();
            foreach (string data in pointsData)
            {
                string[] fields = data.Split(new[] { ", " }, StringSplitOptions.None);
                List<string> filteredFields = new List<string>();
                foreach (int idx in indicesToInclude)
                {
                    filteredFields.Add(idx < fields.Length ? fields[idx] : "");
                }
                filteredPointsData.Add(string.Join(", ", filteredFields));
            }

            int dataRowCount = filteredPointsData.Count;
            // Calculate total tables needed if splitting is required.
            int totalTables = (int)Math.Ceiling((double)dataRowCount / maxRowsPerTable);
            double tableSpacing = 4.0; // Horizontal spacing between tables

            using (ActiveDocument.LockDocument())
            using (Transaction tr = ActiveDatabase.TransactionManager.StartTransaction())
            {
                try
                {
                    // Ensure the table layer exists.
                    EnsureLayerExists(ActiveDatabase, tr, Properties.Settings.Default.TableLayer, Properties.Settings.Default.TableLayerColor);

                    // Get the model space.
                    BlockTable bt = (BlockTable)tr.GetObject(ActiveDatabase.BlockTableId, OpenMode.ForRead);
                    BlockTableRecord modelSpace = (BlockTableRecord)tr.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite);

                    // Ensure table style exists.
                    ObjectId styleId = EnsureTableStyle(ActiveDatabase, tr, Properties.Settings.Default.TableStyleName);


                    // Generate and insert each table.
                    for (int tableIndex = 0; tableIndex < totalTables; tableIndex++)
                    {
                        Table table = GenerateTable(
                            filteredHeaders,
                            filteredPointsData,
                            tableIndex,
                            maxRowsPerTable,
                            styleId,
                            insertionPoint,
                            tableSpacing,
                            showTitle);

                        modelSpace.AppendEntity(table);
                        tr.AddNewlyCreatedDBObject(table, true);
                    }

                    tr.Commit();
                }
                catch (Exception ex)
                {
                    ActiveEditor.WriteMessage($"\nError creating table: {ex.Message}");
                    tr.Abort();
                }
            }
        }

        /// <summary>
        /// Ensures the specified layer exists in the database; if not, creates it.
        /// </summary>
        private void EnsureLayerExists(Database db, Transaction tr, string layerName, short colorIndex)
        {
            LayerTable lt = (LayerTable)tr.GetObject(db.LayerTableId, OpenMode.ForRead);
            if (!lt.Has(layerName))
            {
                lt.UpgradeOpen();
                LayerTableRecord newLayer = new LayerTableRecord
                {
                    Name = layerName,
                    Color = Autodesk.AutoCAD.Colors.Color.FromColorIndex(Autodesk.AutoCAD.Colors.ColorMethod.ByAci, colorIndex)
                };
                lt.Add(newLayer);
                tr.AddNewlyCreatedDBObject(newLayer, true);
            }
        }

        /// <summary>
        /// Ensures the table style with the given name exists in the database; if not, creates it.
        /// </summary>
        private ObjectId EnsureTableStyle(Database db, Transaction tr, string styleName)
        {
            DBDictionary tableStyles = (DBDictionary)tr.GetObject(db.TableStyleDictionaryId, OpenMode.ForRead);
            if (!tableStyles.Contains(styleName))
            {
                tableStyles.UpgradeOpen();
                TableStyle newStyle = new TableStyle();
                newStyle.SetTextHeight(0.26, (int)RowType.TitleRow);
                newStyle.SetTextHeight(0.2, (int)RowType.HeaderRow);
                newStyle.SetTextHeight(0.15, (int)RowType.DataRow);
                newStyle.SetAlignment(CellAlignment.MiddleCenter, (int)(RowType.TitleRow | RowType.HeaderRow | RowType.DataRow));
                newStyle.HorizontalCellMargin = 0.05;
                newStyle.VerticalCellMargin = 0.05;


                ObjectId styleId = newStyle.PostTableStyleToDatabase(db, styleName);
                tr.AddNewlyCreatedDBObject(newStyle, true);
                return styleId;
            }
            return tableStyles.GetAt(styleName);
        }

        /// <summary>
        /// Generates a single table based on the provided headers and point data.
        /// If showTitle is true, creates an extra title row at row 0 and shifts headers to row 1; otherwise, headers are at row 0.
        /// </summary>
        private Table GenerateTable(List<string> headers, List<string> pointsData, int tableIndex, int maxRowsPerTable, ObjectId styleId, Point3d insertionPoint, double tableSpacing, bool showTitle)
        {
            int startIndex = tableIndex * maxRowsPerTable;
            int endIndex = Math.Min(startIndex + maxRowsPerTable - 1, pointsData.Count - 1);
            int dataRows = endIndex - startIndex + 1;

            // Determine row indices for headers and data.
            int headerRowIndex = showTitle ? 1 : 0;
            int dataStartRow = headerRowIndex + 1;
            int totalRows = dataRows + dataStartRow;  // total rows = title (optional) + header + data rows

            Table table = new Table
            {
                TableStyle = styleId,
                Layer = Properties.Settings.Default.TableLayer
            };

            table.SetSize(totalRows, headers.Count);
            table.Width = 8;
            // Use a fixed row height (you may adjust if needed)
            table.SetRowHeight(0.3);
            table.Position = new Point3d(
                insertionPoint.X + (tableIndex * (table.Width + tableSpacing)),
                insertionPoint.Y,
                insertionPoint.Z
            );


            if (showTitle)
            {
                // Title row (row 0): merge cells and set title.
                table.Cells[0, 0].TextString = Properties.Settings.Default.TableTitle;

                table.Rows[0].Height = 0.5;
                table.MergeCells(CellRange.Create(table, 0, 0, 0, headers.Count - 1));

                // Header row (row 1).
                for (int col = 0; col < headers.Count; col++)
                {
                    table.Cells[1, col].TextString = headers[col];
                }

                // Data rows start at row 2.
                for (int row = startIndex; row <= endIndex; row++)
                {
                    string[] dataFields = pointsData[row].Split(new[] { ", " }, StringSplitOptions.None);
                    for (int col = 0; col < dataFields.Length && col < headers.Count; col++)
                    {
                        table.Cells[row - startIndex + 2, col].TextString = dataFields[col];
                    }
                }
            }
            else
            {
                // Replace the following line:
                table.Rows[0].Style = "Header";

                // Without title: header row is row 0.
                for (int col = 0; col < headers.Count; col++)
                {
                    table.Cells[0, col].TextString = headers[col];
                }

                table.Rows[1].Style = "Data";

                // Data rows start at row 1.
                for (int row = startIndex; row <= endIndex; row++)
                {
                    string[] dataFields = pointsData[row].Split(new[] { ", " }, StringSplitOptions.None);
                    for (int col = 0; col < dataFields.Length && col < headers.Count; col++)
                    {
                        table.Cells[row - startIndex + 1, col].TextString = dataFields[col];
                    }
                }
            }

            table.GenerateLayout();
            return table;
        }
    }

}
