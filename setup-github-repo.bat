@echo off
echo ========================================
echo Survey Points Manager - GitHub Setup
echo ========================================
echo.

REM Check if Git is installed
git --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Git is not installed or not in PATH
    echo Please install Git from: https://git-scm.com/download/win
    pause
    exit /b 1
)

echo Git is installed. Proceeding with setup...
echo.

REM Get repository URL from user
set /p REPO_URL="Enter your GitHub repository URL (e.g., https://github.com/username/survey-points-manager.git): "

if "%REPO_URL%"=="" (
    echo ERROR: Repository URL is required
    pause
    exit /b 1
)

echo.
echo Repository URL: %REPO_URL%
echo.

REM Initialize Git repository
echo Initializing Git repository...
git init

REM Add all files
echo Adding all files to Git...
git add .

REM Check if there are files to commit
git diff --cached --quiet
if errorlevel 1 (
    echo Creating initial commit...
    git commit -m "Initial commit: Survey Points Manager complete solution

- Added SPM_NET8_2025_2026 (.NET 8 for AutoCAD 2025/2026)
- Added legacy versions for AutoCAD 2016-2024
- Complete documentation suite (README, USER_GUIDE, INSTALLATION_GUIDE, etc.)
- Professional PackageContents.xml with automatic version detection
- Bundle structure for automatic AutoCAD version loading
- Commercial licensing and customer support documentation
- Deployment guide for developers
- Roadmap for future development"
) else (
    echo No files to commit. Please check if files are in the directory.
    pause
    exit /b 1
)

REM Set main branch
echo Setting main branch...
git branch -M main

REM Add remote origin
echo Adding remote origin...
git remote add origin %REPO_URL%

REM Push to GitHub
echo Pushing to GitHub...
git push -u origin main

if errorlevel 1 (
    echo.
    echo ERROR: Failed to push to GitHub
    echo This might be due to:
    echo 1. Authentication issues (need to login to GitHub)
    echo 2. Repository doesn't exist
    echo 3. Network connectivity issues
    echo.
    echo Please check your GitHub authentication and try again.
    pause
    exit /b 1
)

echo.
echo ========================================
echo SUCCESS! Repository created successfully!
echo ========================================
echo.
echo Your Survey Points Manager repository is now available at:
echo %REPO_URL%
echo.
echo Next steps:
echo 1. Go to your GitHub repository
echo 2. Check that all files are uploaded correctly
echo 3. Review the README.md file
echo 4. Set up repository settings (if needed)
echo.
pause
