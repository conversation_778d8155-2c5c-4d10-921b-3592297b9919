﻿using SPM_NET8_2025_2026.Managers;
using SPM_NET8_2025_2026.Models;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Media;

namespace SPM_NET8_2025_2026.Views
{
    /// <summary>
    /// Interaction logic for HistoryPalette.xaml.
    /// This control displays the history records and allows filtering, clearing, and undoing deletions.
    /// </summary>
    public partial class HistoryPalette : System.Windows.Controls.UserControl
    {
        private HistoryManager _historyManager;
        private CollectionViewSource _historyViewSource;

        public HistoryPalette()
        {
            InitializeComponent();

            // Initialize the history manager instance and set as DataContext.
            _historyManager = HistoryManager.Instance;
            DataContext = _historyManager;

            // Set up the CollectionViewSource for filtering the history records.
            _historyViewSource = new CollectionViewSource { Source = _historyManager.HistoryRecords };
            _historyViewSource.Filter += HistoryViewSource_Filter;

            // Bind the ListView (or DataGrid if you prefer) to the view.
            HistoryListView.ItemsSource = _historyViewSource.View;

            // Set up the search TextBox events for placeholder behavior.
            SearchTextBox.GotFocus += SearchTextBox_GotFocus;
            SearchTextBox.LostFocus += SearchTextBox_LostFocus;

            SearchTextBox.TextChanged += SearchTextBox_TextChanged;

        }

        /// <summary>
        /// Filter event for the history records.
        /// Accepts only records that match the search text (ignoring case).
        /// </summary>
        private void HistoryViewSource_Filter(object sender, FilterEventArgs e)
        {
            if (e.Item is HistoryRecord record)
            {
                string searchText = SearchTextBox.Text;
                if (string.IsNullOrWhiteSpace(searchText) || searchText == "Search...")
                {
                    e.Accepted = true;
                    return;
                }

                searchText = searchText.ToLower();

                // Check if any property contains the search text.
                if (record.FileName.ToLower().Contains(searchText) ||
                    record.OperationType.ToLower().Contains(searchText) ||
                    record.FileType.ToLower().Contains(searchText) ||
                    record.PointCount.ToString().Contains(searchText) ||
                    record.DateTime.ToString().ToLower().Contains(searchText))
                {
                    e.Accepted = true;
                }
                else
                {
                    e.Accepted = false;
                }
            }
        }

        /// <summary>
        /// Refreshes the history view when the Search button is clicked.
        /// </summary>
        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            _historyViewSource.View.Refresh();
        }

        /// <summary>
        /// Clears the placeholder text when the search box receives focus.
        /// </summary>
        private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (SearchTextBox.Text == "Search...")
            {
                SearchTextBox.Text = "";
                SearchTextBox.Foreground = System.Windows.Media.Brushes.Black;
            }
        }

        /// <summary>
        /// Restores the placeholder text if the search box is left empty.
        /// </summary>
        private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                SearchTextBox.Text = "Search...";
                SearchTextBox.Foreground = new SolidColorBrush((System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString("#FF687A99"));
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // Refresh the view so that when the text is cleared, all records show.
            _historyViewSource.View.Refresh();
        }


        /// <summary>
        /// Clears all history records after user confirmation.
        /// </summary>
        private void ClearHistoryRecordsDataGridButton_Click(object sender, RoutedEventArgs e)
        {
            if (System.Windows.MessageBox.Show("Are you sure you want to clear all history?", "Confirm",
                                System.Windows.MessageBoxButton.YesNo, System.Windows.MessageBoxImage.Warning) == System.Windows.MessageBoxResult.Yes)
            {
                _historyManager.ClearHistory();
                _historyViewSource.View.Refresh();
            }
        }

        /// <summary>
        /// Undoes the last clear or delete operation by restoring the backup history.
        /// </summary>
        private void UndoClearHistoryRecordsDataGridButton_Click(object sender, RoutedEventArgs e)
        {
            _historyManager.RestoreBackupHistory();
            _historyViewSource.View.Refresh();
        }
    }

}
