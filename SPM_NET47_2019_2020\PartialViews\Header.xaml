﻿<UserControl x:Class="SPM_NET47_2019_2020.PartialViews.Header"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:SPM_NET47_2019_2020.PartialViews"
              Width="400"
 Height="68"
 Background="#ECECEC"
 mc:Ignorable="d">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/SPM_NET47_2019_2020;component/ResourceDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="33" />
            <RowDefinition Height="33" />
            <RowDefinition Height="2" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="70" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>

        <!--  Logo  -->
        <Border
         Grid.RowSpan="2"
         Grid.Column="0"
         Margin="15,0,0,0"
         VerticalAlignment="Center"
         Background="Transparent"
         Cursor="Hand"
         MouseLeftButtonDown="Viewbox_MouseLeftButtonDown"
         ToolTip="Go to Home"
         ToolTipService.InitialShowDelay="200">

            <ContentControl Template="{StaticResource PageLogoTemplate}" />

        </Border>


        <!--  Three buttons in first row  -->
        <StackPanel
         Grid.Row="0"
         Grid.Column="1"
         Margin="0,0,7,0"
         HorizontalAlignment="Right"
         VerticalAlignment="Center"
         Orientation="Horizontal">

            <!--  Settings Button  -->
            <Button
             x:Name="NavSettingsBTN"
             Grid.Column="0"
             Width="25"
             Height="25"
             Margin="0,0,5,0"
             Click="NavSettingsBTN_Click"
             Cursor="Hand"
             Style="{StaticResource IconButtonStyle}"
             ToolTip="Settings for import/export, layers, points, tables, and history.">
                <Viewbox
                 Width="25"
                 Height="25"
                 HorizontalAlignment="Center"
                 VerticalAlignment="Center"
                 Stretch="Uniform">
                    <Canvas Width="23" Height="23">
                        <Canvas>
                            <Ellipse
                             Canvas.Left="8"
                             Canvas.Top="8"
                             Width="8"
                             Height="8"
                             Opacity="0.7"
                             Stroke="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Button}}"
                             StrokeThickness="1.5" />
                            <Path
                             Stroke="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Button}}"
                             StrokeEndLineCap="Round"
                             StrokeStartLineCap="Round"
                             StrokeThickness="1.5">
                                <Path.Data>
                                    <PathGeometry Figures="M13.765 2.152C13.398 2 12.932 2 12 2s-1.398 0-1.765.152a2 2 0 0 0-1.083 1.083c-.092.223-.129.484-.143.863a1.62 1.62 0 0 1-.79 1.353a1.62 1.62 0 0 1-1.567.008c-.336-.178-.579-.276-.82-.308a2 2 0 0 0-1.478.396C4.04 5.79 3.806 6.193 3.34 7s-.7 1.21-.751 1.605a2 2 0 0 0 .396 1.479c.148.192.355.353.676.555c.473.297.777.803.777 1.361s-.304 1.064-.777 1.36c-.321.203-.529.364-.676.556a2 2 0 0 0-.396 1.479c.052.394.285.798.75 1.605c.467.807.7 1.21 1.015 1.453a2 2 0 0 0 1.479.396c.24-.032.483-.13.819-.308a1.62 1.62 0 0 1 1.567.008c.483.28.77.795.79 1.353c.014.38.05.64.143.863a2 2 0 0 0 1.083 1.083C10.602 22 11.068 22 12 22s1.398 0 1.765-.152a2 2 0 0 0 1.083-1.083c.092-.223.129-.483.143-.863c.02-.558.307-1.074.79-1.353a1.62 1.62 0 0 1 1.567-.008c.336.178.579.276.819.308a2 2 0 0 0 1.479-.396c.315-.242.548-.646 1.014-1.453s.7-1.21.751-1.605a2 2 0 0 0-.396-1.479c-.148-.192-.355-.353-.676-.555A1.62 1.62 0 0 1 19.562 12c0-.558.304-1.064.777-1.36c.321-.203.529-.364.676-.556a2 2 0 0 0 .396-1.479c-.052-.394-.285-.798-.75-1.605c-.467-.807-.7-1.21-1.015-1.453a2 2 0 0 0-1.479-.396c-.24.032-.483.13-.82.308a1.62 1.62 0 0 1-1.566-.008a1.62 1.62 0 0 1-.79-1.353c-.014-.38-.05-.64-.143-.863a2 2 0 0 0-1.083-1.083Z" />
                                </Path.Data>
                            </Path>
                        </Canvas>
                    </Canvas>
                </Viewbox>
            </Button>

            <!--  History Button  -->
            <Button
             x:Name="NavHistoryBTN"
             Grid.Column="1"
             Width="25"
             Height="25"
             Margin="0,0,5,0"
             Click="NavHistoryBTN_Click"
             Cursor="Hand"
             Style="{StaticResource IconButtonStyle}"
             ToolTip="View history of import and export operations.">
                <Viewbox
                 Width="25"
                 Height="25"
                 HorizontalAlignment="Center"
                 VerticalAlignment="Center"
                 Stretch="Uniform">
                    <Canvas Width="23" Height="23">
                        <Path
                         Opacity="0.7"
                         Stroke="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Button}}"
                         StrokeEndLineCap="Round"
                         StrokeLineJoin="Round"
                         StrokeStartLineCap="Round"
                         StrokeThickness="1.5">
                            <Path.Data>
                                <PathGeometry Figures="M12 8v4l2.5 2.5" />
                            </Path.Data>
                        </Path>
                        <Path Fill="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Button}}" StrokeThickness="1.5">
                            <Path.Data>
                                <PathGeometry Figures="m5.604 5.604l-.53-.53zM4.338 6.871l-.75.003a.75.75 0 0 0 .746.747zm2.542.762a.75.75 0 1 0 .007-1.5zM5.075 4.321a.75.75 0 1 0-1.5.008zm-1.248 6.464a.75.75 0 1 0-1.486-.204zm15.035-5.647c-3.82-3.82-9.993-3.86-13.788-.064l1.06 1.06c3.2-3.199 8.423-3.18 11.668.064zM5.138 18.862c3.82 3.82 9.993 3.86 13.788.064l-1.06-1.06c-3.2 3.199-8.423 3.18-11.668-.064zm13.788.064c3.795-3.795 3.756-9.968-.064-13.788l-1.06 1.06c3.244 3.245 3.263 8.468.064 11.668zM5.074 5.074L3.807 6.34L4.868 7.4l1.266-1.266zm-.74 2.547l2.546.012l.007-1.5l-2.545-.012zm.754-.754L5.075 4.32l-1.5.008l.013 2.545zM2.34 10.58a9.81 9.81 0 0 0 2.797 8.281l1.06-1.06a8.31 8.31 0 0 1-2.371-7.017z" FillRule="Nonzero" />
                            </Path.Data>
                        </Path>
                    </Canvas>
                </Viewbox>
            </Button>

            <!--  Help Button  -->
            <Button
             x:Name="NavHelpBTN"
             Grid.Column="2"
             Width="25"
             Height="25"
             Click="NavHelpBTN_Click"
             Cursor="Hand"
             Style="{StaticResource IconButtonStyle}"
             ToolTip="Access plugin documentation and guides.">
                <Viewbox
                 Width="25"
                 Height="25"
                 HorizontalAlignment="Center"
                 VerticalAlignment="Center"
                 Stretch="Uniform">
                    <Canvas Width="16" Height="16">
                        <Path Fill="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Button}}">
                            <Path.Data>
                                <PathGeometry Figures="M8 15c-3.86 0-7-3.14-7-7s3.14-7 7-7s7 3.14 7 7s-3.14 7-7 7M8 2C4.69 2 2 4.69 2 8s2.69 6 6 6s6-2.69 6-6s-2.69-6-6-6" />
                            </Path.Data>
                        </Path>
                        <Path Fill="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Button}}">
                            <Path.Data>
                                <PathGeometry Figures="M8 4.5c-1.11 0-2 .89-2 2h1c0-.55.45-1 1-1s1 .45 1 1c0 1-1.5.88-1.5 2.5h1c0-1.12 1.5-1.25 1.5-2.5c0-1.11-.89-2-2-2" />
                            </Path.Data>
                        </Path>
                        <Ellipse
                         Canvas.Left="7.38"
                         Canvas.Top="10.38"
                         Width="1.24"
                         Height="1.24"
                         Fill="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Button}}" />
                        <Ellipse
                         Canvas.Left="6"
                         Canvas.Top="6"
                         Width="1"
                         Height="1"
                         Fill="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Button}}" />
                        <Ellipse
                         Canvas.Left="7.5"
                         Canvas.Top="8.5"
                         Width="1"
                         Height="1"
                         Fill="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Button}}" />
                    </Canvas>
                </Viewbox>
            </Button>
        </StackPanel>

        <!--  Separator (centered in logo)  -->
        <Rectangle
         Grid.Row="0"
         Grid.Column="1"
         Width="310"
         Height="1.5"
         Margin="0,0,5,0"
         HorizontalAlignment="Right"
         VerticalAlignment="Bottom"
         Fill="#B7A6A6A6" />

        <!--  Two buttons in second row  -->
        <StackPanel
         Grid.Row="1"
         Grid.Column="1"
         Margin="0,0,7,0"
         HorizontalAlignment="Right"
         VerticalAlignment="Center"
         Orientation="Horizontal">

            <!--  Export Button  -->
            <Button
             x:Name="NavExportBTN"
             Grid.Column="4"
             Margin="0,0,5,0"
             Click="NavExportBTN_Click"
             Cursor="Hand"
             Style="{StaticResource IconButtonStyle}"
             ToolTip="Export data to CSV, KML, GSI, SDR, TXT, or IDX files.">
                <Viewbox
                 Width="25"
                 Height="25"
                 HorizontalAlignment="Center"
                 VerticalAlignment="Center"
                 Stretch="Uniform">
                    <Canvas Width="22" Height="22">
                        <Path Fill="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Button}}" Opacity="0.4">
                            <Path.Data>
                                <PathGeometry Figures="M4 12a8 8 0 1 0 16 0z" />
                            </Path.Data>
                        </Path>
                        <Path Fill="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Button}}">
                            <Path.Data>
                                <PathGeometry Figures="M15.53 7.53a.75.75 0 0 1-1.06 0l-1.72-1.72V14a.75.75 0 0 1-1.5 0V5.81L9.53 7.53a.75.75 0 0 1-1.06-1.06l3-3a.75.75 0 0 1 1.06 0l3 3a.75.75 0 0 1 0 1.06" />
                            </Path.Data>
                        </Path>
                    </Canvas>
                </Viewbox>
            </Button>


            <!--  Import Button  -->
            <Button
             x:Name="NavImportBTN"
             Grid.Column="3"
             Click="NavImportBTN_Click"
             Cursor="Hand"
             Style="{StaticResource IconButtonStyle}"
             ToolTip="Import data from CSV, KML, GSI, SDR, TXT, or IDX files.">
                <Viewbox
                 Width="25"
                 Height="25"
                 HorizontalAlignment="Center"
                 VerticalAlignment="Center"
                 Stretch="Uniform">
                    <Canvas Width="22" Height="22">
                        <Path Fill="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Button}}" Opacity="0.4">
                            <Path.Data>
                                <PathGeometry Figures="M4 12a8 8 0 1 0 16 0z" />
                            </Path.Data>
                        </Path>
                        <Path Fill="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Button}}">
                            <Path.Data>
                                <PathGeometry Figures="M15.53 10.47a.75.75 0 0 0-1.06 0l-1.72 1.72V4a.75.75 0 0 0-1.5 0v8.19l-1.72-1.72a.75.75 0 0 0-1.06 1.06l3 3a.75.75 0 0 0 1.06 0l3-3a.75.75 0 0 0 0-1.06" />
                            </Path.Data>
                        </Path>
                    </Canvas>
                </Viewbox>
            </Button>

        </StackPanel>

        <!--  Breadcrumb  -->
        <TextBlock
         Grid.Row="1"
         Grid.Column="1"
         Margin="0,0,47,0"
         HorizontalAlignment="Center"
         VerticalAlignment="Center"
         FontFamily="{StaticResource PoppinsFont}"
         FontSize="14"
         FontWeight="Bold"
         Foreground="#FF687A99"
         Text="{Binding BreadcrumbText, RelativeSource={RelativeSource AncestorType=UserControl}}" />

        <Rectangle
         Grid.Row="2"
         Grid.ColumnSpan="2"
         Width="380"
         Height="1.5"
         HorizontalAlignment="Center"
         VerticalAlignment="Bottom"
         Fill="#B7A6A6A6" />

    </Grid>
</UserControl>
