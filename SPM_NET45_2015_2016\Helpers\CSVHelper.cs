﻿using SPM_NET45_2015_2016.Models;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace SPM_NET45_2015_2016.Helpers
{
    public static class CSVHelper
    {
        /// <summary>
        /// Detects the format of the CSV file based on the first five lines and selects the appropriate option in the ComboBox.
        /// </summary>
        public static void DetectCSVFormat(string filePath, ComboBox csvFormatComboBox)
        {
            try
            {
                using (var reader = new StreamReader(filePath))
                {
                    int maxLinesToCheck = 5;
                    List<string[]> parsedLines = new List<string[]>();

                    while (!reader.EndOfStream && parsedLines.Count < maxLinesToCheck)
                    {
                        string line = reader.ReadLine();
                        if (!string.IsNullOrWhiteSpace(line))
                        {
                            string[] columns = line.Split(new[] { ',', ';', '\t' }, StringSplitOptions.RemoveEmptyEntries);
                            parsedLines.Add(columns);
                        }
                    }

                    if (parsedLines.Count == 0)
                    {
                        return;
                    }

                    int maxColumns = parsedLines.Max(cols => cols.Length);

                    // Prioritize formats with Point Number (P) first, then Easting (E), then Northing (N)
                    string selectedTag = null;

                    if (maxColumns == 2)
                    {
                        selectedTag = "EN"; // Default to EN for 2 columns
                    }
                    else if (maxColumns == 3)
                    {
                        selectedTag = "PEN"; // Default to PEN for 3 columns
                    }
                    else if (maxColumns == 4)
                    {
                        selectedTag = "PENZ"; // Default to PENZ for 4 columns
                    }
                    else if (maxColumns == 5)
                    {
                        selectedTag = "PENZD"; // Default to PENZD for 5 columns
                    }

                    // Select the corresponding ComboBoxItem
                    foreach (ComboBoxItem item in csvFormatComboBox.Items)
                    {
                        if (item.Tag?.ToString() == selectedTag)
                        {
                            csvFormatComboBox.SelectedItem = item;
                            break;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error detecting CSV format: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Detects the number of columns in the CSV file based on the first few lines.
        /// </summary>
        public static int DetectCSVColumnsCount(string filePath)
        {
            try
            {
                using (var reader = new StreamReader(filePath))
                {
                    int maxColumns = 0;
                    int maxLinesToCheck = 5;
                    int linesChecked = 0;

                    while (!reader.EndOfStream && linesChecked < maxLinesToCheck)
                    {
                        string line = reader.ReadLine();
                        if (!string.IsNullOrWhiteSpace(line))
                        {
                            string[] columns = line.Split(new[] { ',', ';', '\t' }, StringSplitOptions.RemoveEmptyEntries);
                            maxColumns = Math.Max(maxColumns, columns.Length);
                            linesChecked++;
                        }
                    }
                    return maxColumns;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error detecting CSV columns: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                return 0;
            }
        }

        /// <summary>
        /// Returns the expected number of columns based on the format tag.
        /// </summary>

        /*
        public static int GetColumnsFromFormatTag(string formatTag)
        {
            switch (formatTag)
            {
                case "EN":
                case "NE":
                    return 2;
                case "PEN":
                case "PNE":
                case "ENZ":
                case "NEZ":
                    return 3;
                case "PENZ":
                case "PNEZ":
                case "ENZD":
                case "NEZD":
                    return 4;
                case "PENZD":
                case "PNEZD":
                    return 5;
                default:
                    return 0; // Invalid format tag
            }
        }
        */

        /// <summary>
        /// Parses a single CSV line into a SurveyPoint based on the format tag.
        /// </summary>

        /*
        public static SurveyPoint ParseCSVLine(string[] values, string formatTag)
        {
            string pointNumber = "";
            double easting = 0, northing = 0, elevation = 0;
            string description = "";

            switch (formatTag)
            {
                // Formats with Easting first
                case "EN":
                    if (values.Length >= 2)
                    {
                        if (!TryParseCoordinate(values[0], out easting) ||
                            !TryParseCoordinate(values[1], out northing))
                        {
                            return null;
                        }
                    }
                    break;

                case "ENZ":
                    if (values.Length >= 3)
                    {
                        if (!TryParseCoordinate(values[0], out easting) ||
                            !TryParseCoordinate(values[1], out northing) ||
                            !TryParseCoordinate(values[2], out elevation))
                        {
                            return null;
                        }
                    }
                    break;

                case "ENZD":
                    if (values.Length >= 4)
                    {
                        if (!TryParseCoordinate(values[0], out easting) ||
                            !TryParseCoordinate(values[1], out northing) ||
                            !TryParseCoordinate(values[2], out elevation))
                        {
                            return null;
                        }
                        description = values.Length > 3 ? values[3] : "";
                    }
                    break;

                // Formats with Northing first
                case "NE":
                    if (values.Length >= 2)
                    {
                        if (!TryParseCoordinate(values[0], out northing) ||
                            !TryParseCoordinate(values[1], out easting))
                        {
                            return null;
                        }
                    }
                    break;

                case "NEZ":
                    if (values.Length >= 3)
                    {
                        if (!TryParseCoordinate(values[0], out northing) ||
                            !TryParseCoordinate(values[1], out easting) ||
                            !TryParseCoordinate(values[2], out elevation))
                        {
                            return null;
                        }
                    }
                    break;

                case "NEZD":
                    if (values.Length >= 4)
                    {
                        if (!TryParseCoordinate(values[0], out northing) ||
                            !TryParseCoordinate(values[1], out easting) ||
                            !TryParseCoordinate(values[2], out elevation))
                        {
                            return null;
                        }
                        description = values.Length > 3 ? values[3] : "";
                    }
                    break;

                // Formats with Point Number first, Easting before Northing
                case "PEN":
                    if (values.Length >= 3)
                    {
                        pointNumber = values[0];
                        if (!TryParseCoordinate(values[1], out easting) ||
                            !TryParseCoordinate(values[2], out northing))
                        {
                            return null;
                        }
                    }
                    break;

                case "PENZ":
                    if (values.Length >= 4)
                    {
                        pointNumber = values[0];
                        if (!TryParseCoordinate(values[1], out easting) ||
                            !TryParseCoordinate(values[2], out northing) ||
                            !TryParseCoordinate(values[3], out elevation))
                        {
                            return null;
                        }
                    }
                    break;

                case "PENZD":
                    if (values.Length >= 5)
                    {
                        pointNumber = values[0];
                        if (!TryParseCoordinate(values[1], out easting) ||
                            !TryParseCoordinate(values[2], out northing) ||
                            !TryParseCoordinate(values[3], out elevation))
                        {
                            return null;
                        }
                        description = values[4];
                    }
                    break;

                // Formats with Point Number first, Northing before Easting
                case "PNE":
                    if (values.Length >= 3)
                    {
                        pointNumber = values[0];
                        if (!TryParseCoordinate(values[1], out northing) ||
                            !TryParseCoordinate(values[2], out easting))
                        {
                            return null;
                        }
                    }
                    break;

                case "PNEZ":
                    if (values.Length >= 4)
                    {
                        pointNumber = values[0];
                        if (!TryParseCoordinate(values[1], out northing) ||
                            !TryParseCoordinate(values[2], out easting) ||
                            !TryParseCoordinate(values[3], out elevation))
                        {
                            return null;
                        }
                    }
                    break;

                case "PNEZD":
                    if (values.Length >= 5)
                    {
                        pointNumber = values[0];
                        if (!TryParseCoordinate(values[1], out northing) ||
                            !TryParseCoordinate(values[2], out easting) ||
                            !TryParseCoordinate(values[3], out elevation))
                        {
                            return null;
                        }
                        description = values[4];
                    }
                    break;

                default:
                    return null; // Invalid format tag
            }

            return new SurveyPoint(pointNumber, easting, northing, elevation, description);
        }
        */



        /// <summary>
        /// Tries to parse a coordinate value from a string.
        /// </summary>
        /*
        private static bool TryParseCoordinate(string input, out double result)
        {
            if (double.TryParse(input, NumberStyles.Any, CultureInfo.InvariantCulture, out result))
            {
                if (double.IsNaN(result) || double.IsInfinity(result))
                {
                    result = 0;
                    return false;
                }
                return true;
            }

            result = 0;
            return false;
        }
        */


        public static int GetColumnsFromFormatTag(string formatTag)
        {
            switch (formatTag)
            {
                case "EN": case "NE": return 2;
                case "PEN": case "PNE": case "ENZ": case "NEZ": return 3;
                case "PENZ": case "PNEZ": case "ENZD": case "NEZD": return 4;
                case "PENZD": case "PNEZD": return 5;
                default: return 0; // Invalid format
            }
        }

        public static SurveyPoint ParseCSVLine(string[] values, string formatTag)
        {
            string pointNumber = "";
            double easting = 0, northing = 0, elevation = 0;
            string description = "";

            switch (formatTag)
            {
                case "EN":
                    if (values.Length < 2 || !TryParseCoordinate(values[0], out easting) || !TryParseCoordinate(values[1], out northing))
                        return null;
                    break;

                case "ENZ":
                    if (values.Length < 3 || !TryParseCoordinate(values[0], out easting) || !TryParseCoordinate(values[1], out northing) || !TryParseCoordinate(values[2], out elevation))
                        return null;
                    break;

                case "ENZD":
                    if (values.Length < 3 || !TryParseCoordinate(values[0], out easting) || !TryParseCoordinate(values[1], out northing) || !TryParseCoordinate(values[2], out elevation))
                        return null;
                    description = values.Length > 3 ? values[3] : "";
                    break;

                case "NE":
                    if (values.Length < 2 || !TryParseCoordinate(values[0], out northing) || !TryParseCoordinate(values[1], out easting))
                        return null;
                    break;

                case "NEZ":
                    if (values.Length < 3 || !TryParseCoordinate(values[0], out northing) || !TryParseCoordinate(values[1], out easting) || !TryParseCoordinate(values[2], out elevation))
                        return null;
                    break;

                case "NEZD":
                    if (values.Length < 3 || !TryParseCoordinate(values[0], out northing) || !TryParseCoordinate(values[1], out easting) || !TryParseCoordinate(values[2], out elevation))
                        return null;
                    description = values.Length > 3 ? values[3] : "";
                    break;

                case "PEN":
                    if (values.Length < 3 || !TryParseCoordinate(values[1], out easting) || !TryParseCoordinate(values[2], out northing))
                        return null;
                    pointNumber = values[0];
                    break;

                case "PENZ":
                    if (values.Length < 4 || !TryParseCoordinate(values[1], out easting) || !TryParseCoordinate(values[2], out northing) || !TryParseCoordinate(values[3], out elevation))
                        return null;
                    pointNumber = values[0];
                    break;

                case "PENZD":
                    if (values.Length < 4 || !TryParseCoordinate(values[1], out easting) || !TryParseCoordinate(values[2], out northing) || !TryParseCoordinate(values[3], out elevation))
                        return null;
                    pointNumber = values[0];
                    description = values.Length > 4 ? values[4] : "";
                    break;

                case "PNE":
                    if (values.Length < 3 || !TryParseCoordinate(values[1], out northing) || !TryParseCoordinate(values[2], out easting))
                        return null;
                    pointNumber = values[0];
                    break;

                case "PNEZ":
                    if (values.Length < 4 || !TryParseCoordinate(values[1], out northing) || !TryParseCoordinate(values[2], out easting) || !TryParseCoordinate(values[3], out elevation))
                        return null;
                    pointNumber = values[0];
                    break;

                case "PNEZD":
                    if (values.Length < 4 || !TryParseCoordinate(values[1], out northing) || !TryParseCoordinate(values[2], out easting) || !TryParseCoordinate(values[3], out elevation))
                        return null;
                    pointNumber = values[0];
                    description = values.Length > 4 ? values[4] : "";
                    break;

                default:
                    return null;
            }

            return new SurveyPoint(pointNumber, easting, northing, elevation, description);
        }

        private static bool TryParseCoordinate(string input, out double result)
        {
            if (double.TryParse(input, NumberStyles.Any, CultureInfo.InvariantCulture, out result))
            {
                if (double.IsNaN(result) || double.IsInfinity(result))
                {
                    result = 0;
                    return false;
                }
                return true;
            }
            result = 0;
            return false;
        }


    }
}