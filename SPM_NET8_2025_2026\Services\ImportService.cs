﻿using SPM_NET8_2025_2026.Helpers;
using SPM_NET8_2025_2026.Models;
using System;
using System.Collections.ObjectModel;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Windows;
using System.Xml;

namespace SPM_NET8_2025_2026.Services
{
    public static class ImportService
    {
        /// <summary>
        /// Gets a safe encoding for file reading. Tries Windows-1252 first, falls back to UTF-8.
        /// This fixes .NET 8 encoding issues where legacy encodings aren't available by default.
        /// </summary>
        /// <returns>A safe encoding for file reading</returns>
        private static Encoding GetSafeEncoding()
        {
            try
            {
                // Try to get Windows-1252 encoding (common for CSV/TXT files)
                return Encoding.GetEncoding(1252);
            }
            catch (ArgumentException)
            {
                // If 1252 is not available (shouldn't happen after registering CodePagesEncodingProvider),
                // fall back to UTF-8 which is always available
                return Encoding.UTF8;
            }
        }
        /// <summary>
        /// Processes a KML file and returns a collection of SurveyPoint objects.
        /// Supports KML 2.2, KML 2.1, and KML files using a default namespace.
        /// </summary>
        /// <param name="filePath">Path to the KML file</param>
        /// <param name="hemisphere">Hemisphere setting ("North" or "South")</param>
        /// <param name="zone">UTM zone as a string</param>
        /// <returns>A collection of SurveyPoint objects parsed from the file</returns>
        public static ObservableCollection<SurveyPoint> ProcessKMLPoints(string filePath, string hemisphere, string zone)
        {
            var points = new ObservableCollection<SurveyPoint>();

            try
            {
                if (string.IsNullOrEmpty(filePath))
                {
                    throw new Exception("No file selected.");
                }

                bool isNorthernHemisphere = hemisphere.Equals("North", StringComparison.OrdinalIgnoreCase);
                int zoneNumber = int.Parse(zone, CultureInfo.InvariantCulture);

                XmlDocument kmlDoc = new XmlDocument();
                kmlDoc.Load(filePath);

                // Set up namespace manager for multiple namespaces.
                var xmlnsManager = new XmlNamespaceManager(kmlDoc.NameTable);
                xmlnsManager.AddNamespace("kml2_2", "http://www.opengis.net/kml/2.2");
                xmlnsManager.AddNamespace("kml2_1", "http://www.opengis.net/kml/2.1");
                // Map the default namespace to "kml"
                xmlnsManager.AddNamespace("kml", "http://www.opengis.net/kml/2.1");
                xmlnsManager.AddNamespace("gx", "http://www.google.com/kml/ext/2.2");
                xmlnsManager.AddNamespace("atom", "http://www.w3.org/2005/Atom");

                // Select Placemark nodes from any valid namespace.
                XmlNodeList placemarkNodes = kmlDoc.SelectNodes(
                    "//kml2_2:Placemark | //kml2_1:Placemark | //kml:Placemark | //Placemark",
                    xmlnsManager);

                if (placemarkNodes == null || placemarkNodes.Count == 0)
                {
                    throw new Exception("No valid Placemark elements found in the KML file.");
                }

                foreach (XmlNode placemarkNode in placemarkNodes)
                {
                    // Get the coordinates node using union of queries.
                    XmlNode pointNode = placemarkNode.SelectSingleNode(
                        ".//kml2_2:Point/kml2_2:coordinates | " +
                        ".//kml2_1:Point/kml2_1:coordinates | " +
                        ".//kml:Point/kml:coordinates | " +
                        ".//Point/coordinates",
                        xmlnsManager);

                    if (pointNode == null || string.IsNullOrWhiteSpace(pointNode.InnerText))
                    {
                        continue;
                    }

                    string[] coordinates = pointNode.InnerText.Trim().Split(',');
                    if (coordinates.Length < 2)
                    {
                        continue;
                    }

                    if (!double.TryParse(coordinates[0].Trim(), NumberStyles.Any, CultureInfo.InvariantCulture, out double longitude) ||
                        !double.TryParse(coordinates[1].Trim(), NumberStyles.Any, CultureInfo.InvariantCulture, out double latitude) ||
                        double.IsNaN(longitude) || double.IsNaN(latitude))
                    {
                        continue;
                    }

                    // Convert geographic coordinates to UTM.
                    CoordinateConversion.DecimalDegreesToUTM(
                        latitude,
                        longitude,
                        out double easting,
                        out double northing,
                        out int calculatedZone);

                    // If the user is in the southern hemisphere, adjust northing.
                    if (!isNorthernHemisphere)
                    {
                        northing += 10000000;
                    }

                    // Parse elevation if available.
                    double elevation = 0;
                    if (coordinates.Length > 2 &&
                        double.TryParse(coordinates[2].Trim(), NumberStyles.Any, CultureInfo.InvariantCulture, out double elev) &&
                        !double.IsNaN(elev))
                    {
                        elevation = elev;
                    }

                    // Extract point name (using union query with our defined prefixes).
                    XmlNode nameNode = placemarkNode.SelectSingleNode(
                        ".//kml2_2:name | .//kml2_1:name | .//kml:name",
                        xmlnsManager);
                    string pointNumber = nameNode?.InnerText?.Trim() ?? string.Empty;

                    // Extract description.
                    XmlNode descNode = placemarkNode.SelectSingleNode(
                        ".//kml2_2:description | .//kml2_1:description | .//kml:description",
                        xmlnsManager);
                    string description = descNode?.InnerText?.Trim() ?? string.Empty;

                    points.Add(new SurveyPoint(pointNumber, easting, northing, elevation, description));
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error importing KML file: {ex.Message}", "Import Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }

            return points;
        }



        /// <summary>
        /// Processes an SDR file and returns an SDRFileData object that includes both survey points and file metadata.
        /// Data extracted: Job Name, Instrument Model, Target Height, and Scale.
        /// </summary>
        /// <param name="filePath">The full path to the SDR file.</param>
        /// <returns>An SDRFileData object containing survey points and file metadata.</returns>
        public static SDRFileData ProcessSDRFile(string filePath)
        {
            var sdrData = new SDRFileData();

            try
            {
                if (string.IsNullOrEmpty(filePath))
                {
                    throw new ArgumentException("File path is empty.", nameof(filePath));
                }

                string[] lines = File.ReadAllLines(filePath);

                foreach (string line in lines)
                {
                    if (line.Length < 4)
                    {
                        continue;
                    }

                    string recordType = line.Substring(0, 4).Trim();

                    // Job record: "10??" → Job Name
                    if (recordType.StartsWith("10"))
                    {
                        sdrData.FileInfo.JobName = line.Length >= 20 ? line.Substring(4, 16).Trim() : string.Empty;
                    }
                    // Instrument record: "01??" → Instrument Model
                    else if (recordType.StartsWith("01"))
                    {
                        string instrumentLine = line.Length > 4 ? line.Substring(4).Trim() : string.Empty;
                        if (!string.IsNullOrWhiteSpace(instrumentLine))
                        {
                            // Split the line into words (removing extra spaces) and take the first word.
                            var parts = instrumentLine.Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                            string modelText = parts.Length > 0 ? parts[0] : instrumentLine;
                            sdrData.FileInfo.InstrumentModel = modelText.Replace(":", "");
                        }
                    }
                    // Target record: "03??" → Target Height
                    else if (recordType.StartsWith("03"))
                    {
                        string targetStr = line.Length >= 20 ? line.Substring(4, 16).Trim() : string.Empty;
                        if (double.TryParse(targetStr, NumberStyles.Any, CultureInfo.InvariantCulture, out double targetHeight))
                        {
                            sdrData.FileInfo.TargetHeight = targetHeight;
                        }
                    }
                    // Scale record: "06??" → Scale
                    else if (recordType.StartsWith("06"))
                    {
                        string scaleStr = line.Length >= 20 ? line.Substring(4, 16).Trim() : string.Empty;
                        if (double.TryParse(scaleStr, NumberStyles.Any, CultureInfo.InvariantCulture, out double scaleValue))
                        {
                            sdrData.FileInfo.Scale = scaleValue;
                        }
                    }
                    // Measurement points: "08TP" or "08KI"
                    else if (recordType == "08TP" || recordType == "08KI")
                    {
                        if (line.Length >= 67)
                        {
                            string pointNumber = line.Substring(4, 16).Trim();
                            if (double.TryParse(line.Substring(20, 15).Trim(), NumberStyles.Any, CultureInfo.InvariantCulture, out double northing) &&
                                double.TryParse(line.Substring(35, 15).Trim(), NumberStyles.Any, CultureInfo.InvariantCulture, out double easting) &&
                                double.TryParse(line.Substring(51, 15).Trim(), NumberStyles.Any, CultureInfo.InvariantCulture, out double elevation))
                            {
                                string description = line.Length > 67 ? line.Substring(67).Trim() : string.Empty;
                                sdrData.SurveyPoints.Add(new SurveyPoint(pointNumber, easting, northing, elevation, description));
                            }
                        }
                    }
                    // Base station points: "02TP"
                    else if (recordType == "02TP")
                    {
                        if (line.Length >= 67)
                        {
                            string pointNumber = line.Substring(4, 16).Trim();
                            if (double.TryParse(line.Substring(20, 15).Trim(), NumberStyles.Any, CultureInfo.InvariantCulture, out double northing) &&
                                double.TryParse(line.Substring(35, 15).Trim(), NumberStyles.Any, CultureInfo.InvariantCulture, out double easting) &&
                                double.TryParse(line.Substring(51, 15).Trim(), NumberStyles.Any, CultureInfo.InvariantCulture, out double elevation))
                            {
                                sdrData.SurveyPoints.Add(new SurveyPoint(pointNumber, easting, northing, elevation, "Base Station"));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error importing SDR file: {ex.Message}", ex);
            }

            return sdrData;
        }



        /// <summary>
        /// Legacy compatibility method: Extracts only survey points from an SDR file.
        /// </summary>
        /// <param name="filePath">The full path to the SDR file.</param>
        /// <returns>An ObservableCollection of SurveyPoint objects.</returns>
        public static ObservableCollection<SurveyPoint> ProcessSDRPoints(string filePath)
        {
            return ProcessSDRFile(filePath).SurveyPoints;
        }


        /// <summary>
        /// Processes an IDX file and returns an IDXFileData object that contains both
        /// a collection of SurveyPoint objects and the extracted file metadata.
        /// Extracted metadata: InstrumentModel, LinearUnit, ProjectName, Operator, CreationDate.
        /// </summary>
        /// <param name="filePath">The full path to the IDX file.</param>
        /// <returns>An IDXFileData object with survey points and file metadata.</returns>
        public static IDXFileData ProcessIDXFile(string filePath)
        {
            var idxData = new IDXFileData();

            try
            {
                if (string.IsNullOrEmpty(filePath))
                {
                    throw new ArgumentException("File path is empty.", nameof(filePath));
                }

                // Read all lines from the file.
                string[] lines = File.ReadAllLines(filePath);

                // --- Process the Header Section ---
                int headerStart = Array.IndexOf(lines, lines.FirstOrDefault(l => l.Trim().Equals("HEADER", StringComparison.OrdinalIgnoreCase)));
                int headerEnd = Array.IndexOf(lines, lines.FirstOrDefault(l => l.Trim().Equals("END HEADER", StringComparison.OrdinalIgnoreCase)));

                if (headerStart != -1 && headerEnd != -1 && headerEnd > headerStart)
                {
                    // Process each header line.
                    for (int i = headerStart + 1; i < headerEnd; i++)
                    {
                        string line = lines[i].Trim();
                        if (line.StartsWith("SYSTEM", StringComparison.OrdinalIgnoreCase))
                        {
                            // Remove the "SYSTEM" keyword and any quotes
                            idxData.FileInfo.InstrumentModel = CleanValue(line.Substring("SYSTEM".Length));
                            idxData.FileInfo.InstrumentModel = Regex.Replace(idxData.FileInfo.InstrumentModel, @"\\+$", "").Trim();
                        }
                        else if (line.StartsWith("LINEAR", StringComparison.OrdinalIgnoreCase))
                        {
                            // Example: LINEAR  METRE
                            idxData.FileInfo.LinearUnit = CleanValue(line.Substring("LINEAR".Length));
                        }
                        else if (line.StartsWith("PROJECT", StringComparison.OrdinalIgnoreCase))
                        {
                            // Project block: read until "END PROJECT"
                            int projectStart = i;
                            int projectEnd = Array.IndexOf(lines, lines.Skip(i).FirstOrDefault(l => l.Trim().Equals("END PROJECT", StringComparison.OrdinalIgnoreCase)));
                            if (projectEnd == -1)
                            {
                                projectEnd = headerEnd; // fallback
                            }
                            for (int j = projectStart + 1; j < projectEnd; j++)
                            {
                                string projLine = lines[j].Trim();
                                if (projLine.StartsWith("NAME", StringComparison.OrdinalIgnoreCase))
                                {
                                    idxData.FileInfo.ProjectName = CleanValue(projLine.Substring("NAME".Length));
                                }
                                else if (projLine.StartsWith("OPERATOR", StringComparison.OrdinalIgnoreCase))
                                {
                                    idxData.FileInfo.Operator = CleanValue(projLine.Substring("OPERATOR".Length));
                                }
                                else if (projLine.StartsWith("CREATION_DATE", StringComparison.OrdinalIgnoreCase))
                                {
                                    idxData.FileInfo.CreationDate = CleanValue(projLine.Substring("CREATION_DATE".Length));
                                }
                            }
                            // Skip past project block
                            i = projectEnd;
                        }
                    }
                }

                // --- Process the Points Section ---
                int pointsStartIndex = -1;
                int pointsEndIndex = -1;
                for (int i = 0; i < lines.Length; i++)
                {
                    string trimmedLine = lines[i].Trim();
                    if (trimmedLine.Equals("POINTS (PointNo, PointID, East, North, Elevation, Code, Date, CLASS)", StringComparison.OrdinalIgnoreCase))
                    {
                        pointsStartIndex = i + 1;
                    }
                    else if (pointsStartIndex != -1 && trimmedLine.Equals("END POINTS", StringComparison.OrdinalIgnoreCase))
                    {
                        pointsEndIndex = i;
                        break;
                    }
                }

                if (pointsStartIndex != -1 && pointsEndIndex != -1)
                {
                    for (int i = pointsStartIndex; i < pointsEndIndex; i++)
                    {
                        string line = lines[i].Trim();
                        // Split by comma or tab.
                        string[] pointInfo = line.Split(new[] { ',', '\t' }, StringSplitOptions.RemoveEmptyEntries);
                        if (pointInfo.Length >= 8 &&
                            !string.IsNullOrWhiteSpace(pointInfo[2]) &&
                            !string.IsNullOrWhiteSpace(pointInfo[3]) &&
                            !string.IsNullOrWhiteSpace(pointInfo[1]))
                        {
                            string pointID = pointInfo[1].Trim();
                            double east = double.TryParse(pointInfo[2].Trim(), NumberStyles.Any, CultureInfo.InvariantCulture, out double eastValue) ? eastValue : 0;
                            double north = double.TryParse(pointInfo[3].Trim(), NumberStyles.Any, CultureInfo.InvariantCulture, out double northValue) ? northValue : 0;
                            double elevation = double.TryParse(pointInfo[4].Trim(), NumberStyles.Any, CultureInfo.InvariantCulture, out double elevationValue) ? elevationValue : 0;

                            // Skip invalid coordinates (if any are NaN)
                            if (double.IsNaN(east) || double.IsNaN(north) || double.IsNaN(elevation))
                            {
                                continue;
                            }

                            string code = pointInfo[5].Trim();
                            string date = pointInfo[6].Trim();
                            string description = $"{code} {date}".Trim();

                            idxData.SurveyPoints.Add(new SurveyPoint(pointID, east, north, elevation, description));
                        }
                    }
                }
                else
                {
                    throw new Exception("Could not locate the points section in the IDX file.");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error importing IDX file: {ex.Message}", ex);
            }

            return idxData;
        }

        /// <summary>
        /// Cleans the input string by trimming, removing extra quotes, and removing leading punctuation like ':'.
        /// </summary>
        private static string CleanValue(string value)
        {
            if (string.IsNullOrWhiteSpace(value))
            {
                return string.Empty;
            }

            // Remove leading punctuation such as ':' and trim spaces and quotes.
            return Regex.Replace(value.Trim(), @"^[:\s]+", "").Trim('"').Trim();
        }


        /// <summary>
        /// Processes a GSI file and returns a collection of SurveyPoint objects.
        /// It uses the current variant: if isGsi16 is true, it uses fixed-field parsing for GSI 16;
        /// otherwise, it uses prefix-based parsing for GSI 8.
        /// </summary>
        /// <param name="filePath">The full path of the GSI file.</param>
        /// <param name="isGsi16">True for GSI 16 parsing; false for GSI 8 parsing.</param>
        /// <returns>An ObservableCollection of SurveyPoint objects.</returns>
        public static ObservableCollection<SurveyPoint> ProcessGSIPoints(string filePath, bool isGsi16)
        {
            var points = new ObservableCollection<SurveyPoint>();

            try
            {
                if (string.IsNullOrEmpty(filePath))
                {
                    throw new ArgumentException("Please select a file first.", nameof(filePath));
                }

                using (StreamReader reader = new StreamReader(filePath))
                {
                    string line;
                    while ((line = reader.ReadLine()) != null)
                    {
                        if (string.IsNullOrWhiteSpace(line))
                        {
                            continue;
                        }

                        if (isGsi16)
                        {
                            // GSI 16 parsing: fixed field order.
                            // Expected fields:
                            // words[0]: point number (e.g. "*11....+00000000000000A1")
                            // words[1]: east coordinate (e.g. "21.024+0000000003545100")
                            // words[2]: north coordinate (e.g. "22.024+0000000009117510")
                            // words[3]: elevation (e.g. "31.100+0000000000010250")
                            // words[4] (optional): description (e.g. "71....+000000000000DESC")
                            string[] words = line.Split((char[])null, StringSplitOptions.RemoveEmptyEntries);
                            if (words.Length < 4)
                            {
                                continue;
                            }

                            var point = new SurveyPoint();
                            point.PointNumber = GSIHelper.ExtractNumericPart(words[0]);
                            point.Easting = GSIHelper.ParseGsi16Coordinate(words[1]);
                            point.Northing = GSIHelper.ParseGsi16Coordinate(words[2]);
                            point.Elevation = GSIHelper.ParseGsi16Coordinate(words[3]);

                            // Process description if present and if it starts with "71"
                            if (words.Length >= 5 && words[4].StartsWith("71"))
                            {
                                int plusIndex = words[4].IndexOf('+');
                                if (plusIndex >= 0 && plusIndex < words[4].Length - 1)
                                {
                                    string desc = words[4].Substring(plusIndex + 1).Trim();
                                    // Optionally trim leading zeros
                                    desc = desc.TrimStart('0');
                                    point.Description = desc;
                                }
                            }

                            // Add point if valid (avoid adding points with zero coordinates or point number "0")
                            if (point.Easting != 0 && point.Northing != 0 && point.PointNumber != "0")
                            {
                                points.Add(point);
                            }
                        }
                        else
                        {
                            // GSI 8 parsing: use prefix-based logic.
                            string[] words = line.Split((char[])null, StringSplitOptions.RemoveEmptyEntries);
                            if (words.Length == 0)
                            {
                                continue;
                            }

                            var point = new SurveyPoint();
                            foreach (string word in words)
                            {
                                if (word.StartsWith("11") || word.StartsWith("*11"))
                                {
                                    point.PointNumber = GSIHelper.ExtractNumericPart(word);
                                }
                                else if (word.StartsWith("81") || word.StartsWith("84"))
                                {
                                    string cleaned = GSIHelper.ExtractNumericPart(word);
                                    if (double.TryParse(cleaned, NumberStyles.Any, CultureInfo.InvariantCulture, out double east))
                                    {
                                        point.Easting = east / 1000.0;
                                    }
                                }
                                else if (word.StartsWith("82") || word.StartsWith("85"))
                                {
                                    string cleaned = GSIHelper.ExtractNumericPart(word);
                                    if (double.TryParse(cleaned, NumberStyles.Any, CultureInfo.InvariantCulture, out double north))
                                    {
                                        point.Northing = north / 1000.0;
                                    }
                                }
                                else if (word.StartsWith("83") || word.StartsWith("86"))
                                {
                                    string cleaned = GSIHelper.ExtractNumericPart(word);
                                    if (double.TryParse(cleaned, NumberStyles.Any, CultureInfo.InvariantCulture, out double elevation))
                                    {
                                        point.Elevation = elevation / 1000.0;
                                    }
                                }
                                else if (word.StartsWith("41"))
                                {
                                    point.Description = GSIHelper.ExtractNumericPart(word);
                                }
                            }
                            if (point.Easting != 0 && point.Northing != 0 && point.PointNumber != "0")
                            {
                                points.Add(point);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception("Error importing GSI file: " + ex.Message, ex);
            }

            return points;
        }


        /// <summary>
        /// Processes CSV file import with specified options.
        /// Adjusts each line to match the expected number of columns and skips rows where required numeric fields cannot be parsed.
        /// </summary>
        /// <param name="filePath">The full path of the CSV file.</param>
        /// <param name="selectedFormat">The CSV format tag (e.g., "ENZ", "PENZ", "PENZD", "PNE", "PNEZ", "PNEZD").</param>
        /// <returns>An ObservableCollection of SurveyPoint objects.</returns>
        /*
        public static ObservableCollection<SurveyPoint> ProcessCSVPoints(string filePath, string selectedFormat)
        {
            var points = new ObservableCollection<SurveyPoint>();

            try
            {
                using (var reader = new StreamReader(filePath, GetSafeEncoding()))
                {
                    while (!reader.EndOfStream)
                    {
                        string line = reader.ReadLine();
                        if (string.IsNullOrWhiteSpace(line))
                        {
                            continue;
                        }

                        // Split line by comma, semicolon, or tab; do not remove empty entries.
                        string[] values = line.Split(new[] { ',', ';', '\t' }, StringSplitOptions.None);

                        int requiredColumns = CSVHelper.GetColumnsFromFormatTag(selectedFormat);

                        // If fewer columns than required, resize array (missing entries become empty strings)
                        if (values.Length < requiredColumns)
                        {
                            Array.Resize(ref values, requiredColumns);
                            // Fill missing entries with empty strings.
                            for (int j = 0; j < values.Length; j++)
                            {
                                if (values[j] == null)
                                {
                                    values[j] = "";
                                }
                            }
                        }
                        // If extra columns exist, take only the first requiredColumns.
                        else if (values.Length > requiredColumns)
                        {
                            values = values.Take(requiredColumns).ToArray();
                        }

                        // Parse the row into a SurveyPoint.
                        SurveyPoint point = CSVHelper.ParseCSVLine(values, selectedFormat);

                        // Only add valid points (i.e. non-null and with all required coordinates parsed).
                        if (point != null)
                        {
                            points.Add(point);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error importing CSV file: {ex.Message}", "Import Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }

            return points;
        }
        */

        public static ObservableCollection<SurveyPoint> ProcessCSVPoints(string filePath, string selectedFormat)
        {
            var points = new ObservableCollection<SurveyPoint>();

            try
            {
                using (var reader = new StreamReader(filePath, GetSafeEncoding()))
                {
                    int lineNumber = 0;
                    while (!reader.EndOfStream)
                    {
                        string line = reader.ReadLine();
                        lineNumber++;
                        if (string.IsNullOrWhiteSpace(line))
                        {
                            continue;
                        }

                        string[] values = line.Split(new[] { ',', ';', '\t' }, StringSplitOptions.None);
                        int requiredColumns = CSVHelper.GetColumnsFromFormatTag(selectedFormat);

                        if (values.Length < requiredColumns)
                        {
                            // Log or skip silently; could add debug output here
                            continue;
                        }
                        values = values.Take(requiredColumns).ToArray();

                        SurveyPoint point = CSVHelper.ParseCSVLine(values, selectedFormat);
                        if (point != null)
                        {
                            points.Add(point);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error importing CSV file: {ex.Message}", "Import Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }

            return points;
        }



        /// <summary>
        /// Processes a tab-delimited TXT file and returns a collection of SurveyPoint objects.
        /// It uses TXTHelper.DetectHeader to determine if the file has a header row,
        /// and uses CSVHelper methods to determine the expected column count and parse each line.
        /// </summary>
        /// <param name="filePath">The full path to the TXT file.</param>
        /// <param name="formatTag">The selected format tag (e.g., "PNE", "PNEZ", "PNEZD", "ENZ", "NEZ", etc.).</param>
        /// <returns>An ObservableCollection of SurveyPoint objects.</returns>
        public static ObservableCollection<SurveyPoint> ProcessTXTPoints(string filePath, string formatTag)
        {
            var points = new ObservableCollection<SurveyPoint>();

            try
            {
                if (string.IsNullOrEmpty(filePath))
                {
                    throw new ArgumentException("File path is empty.", nameof(filePath));
                }

                // Read all lines using the proper encoding.
                string[] lines = File.ReadAllLines(filePath, GetSafeEncoding());

                // Use TXTHelper to detect if the file has a header row.
                bool hasHeader = TXTHelper.DetectHeader(lines);
                int startLine = hasHeader ? 1 : 0;

                // Determine the number of required columns for the selected format.
                int requiredColumns = CSVHelper.GetColumnsFromFormatTag(formatTag);

                for (int i = startLine; i < lines.Length; i++)
                {
                    string line = lines[i];
                    if (string.IsNullOrWhiteSpace(line))
                    {
                        continue;
                    }

                    // Split by tab (keeping empty entries).
                    string[] fields = line.Split('\t');

                    // For formats expecting 5 columns (PNEZD/PENZD) with only 4 fields, add an empty description.
                    if ((formatTag == "PNEZD" || formatTag == "PENZD") && fields.Length == 4)
                    {
                        string[] newFields = new string[5];
                        Array.Copy(fields, newFields, 4);
                        newFields[4] = "";
                        fields = newFields;
                    }
                    // If the line has fewer fields than required, skip it.
                    else if (fields.Length < requiredColumns)
                    {
                        continue;
                    }
                    // If the line has more fields than required, take only the required columns.
                    else if (fields.Length > requiredColumns)
                    {
                        fields = fields.Take(requiredColumns).ToArray();
                    }

                    // Parse the fields into a SurveyPoint.
                    SurveyPoint point = CSVHelper.ParseCSVLine(fields, formatTag);
                    if (point != null)
                    {
                        points.Add(point);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception("Error importing TXT file: " + ex.Message, ex);
            }

            return points;
        }


    }
}
