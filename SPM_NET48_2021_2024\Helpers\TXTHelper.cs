﻿using System;
using System.Globalization;
using System.IO;
using System.Windows;
using System.Windows.Controls;

namespace SPM_NET48_2021_2024.Helpers
{
    public static class TXTHelper
    {
        /// <summary>
        /// Detects the format of a TXT file and selects the appropriate ComboBox item
        /// </summary>
        public static void DetectTXTFormat(string filePath, ComboBox formatComboBox)
        {
            try
            {
                string[] lines = File.ReadAllLines(filePath);
                if (lines.Length == 0)
                {
                    return;
                }

                // Find the maximum number of columns in the first few data lines (skipping headers)
                int maxColumns = 0;
                bool hasHeader = DetectHeader(lines);
                int startLine = hasHeader ? 1 : 0;

                int linesToCheck = Math.Min(5, lines.Length - startLine);
                for (int i = startLine; i < startLine + linesToCheck; i++)
                {
                    if (i >= lines.Length || string.IsNullOrWhiteSpace(lines[i]))
                    {
                        continue;
                    }

                    string[] fields = lines[i].Split('\t');
                    maxColumns = Math.Max(maxColumns, fields.Length);
                }

                // Check if first column appears to be a point ID
                bool hasPointNumber = false;
                if (lines.Length > startLine)
                {
                    string[] firstDataRow = lines[startLine].Split('\t');
                    if (firstDataRow.Length > 0)
                    {
                        // If first field is not a valid double or is a small numeric value, it's likely a point ID
                        if (!double.TryParse(firstDataRow[0], NumberStyles.Any, CultureInfo.InvariantCulture, out _) ||
                            (double.TryParse(firstDataRow[0], NumberStyles.Any, CultureInfo.InvariantCulture, out double val) &&
                             val < 1000 && val > -1000))
                        {
                            hasPointNumber = true;
                        }
                    }
                }

                // Set the most appropriate format based on column count and point number detection

                foreach (ComboBoxItem item in formatComboBox.Items)
                {
                    if (item.Tag == null)
                    {
                        continue;
                    }

                    string formatTag = item.Tag.ToString();

                    if (hasPointNumber)
                    {
                        switch (maxColumns)
                        {
                            case 3 when formatTag == "PNE":
                                formatComboBox.SelectedItem = item;
                                return;
                            case 4:
                                // Prefer the 4-column format if available.
                                if (formatTag == "PNEZ" || formatTag == "PENZ")
                                {
                                    formatComboBox.SelectedItem = item;
                                    return;
                                }
                                // If no 4-column format is available, then you might allow the 5-column variant
                                // but only if the header or sample data indicates a non-empty description field.
                                break;
                            case 5 when formatTag == "PNEZD" || formatTag == "PENZD":
                                formatComboBox.SelectedItem = item;
                                return;
                        }
                    }
                    else
                    {
                        switch (maxColumns)
                        {
                            case 2:
                            case 3:
                                if (formatTag == "NEZ" || formatTag == "ENZ")
                                {
                                    formatComboBox.SelectedItem = item;
                                    return;
                                }
                                break;
                        }
                    }
                }


                // Default to the second item if no format was detected
                if (formatComboBox.Items.Count > 1)
                {
                    formatComboBox.SelectedIndex = 1;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error detecting TXT format: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Detects if the file has a header row
        /// </summary>
        public static bool DetectHeader(string[] lines)
        {
            if (lines.Length == 0)
            {
                return false;
            }

            string firstLine = lines[0];
            if (string.IsNullOrWhiteSpace(firstLine))
            {
                return false;
            }

            string[] firstRowFields = firstLine.Split('\t');

            // Check if we have at least a second line to compare with
            if (lines.Length < 2)
            {
                return false;
            }

            string secondLine = lines[1];
            if (string.IsNullOrWhiteSpace(secondLine))
            {
                return false;
            }

            string[] secondRowFields = secondLine.Split('\t');

            // If field counts differ, first line might be a header
            if (firstRowFields.Length != secondRowFields.Length)
            {
                return true;
            }

            // Check if first row contains any header-like text that can't be parsed as numbers
            // For example, fields like "P", "E", "N", "Z", "D", "Point", "Easting", etc.
            bool firstRowHasText = false;
            bool secondRowHasNumbers = false;

            for (int i = 0; i < firstRowFields.Length; i++)
            {
                string field1 = firstRowFields[i].Trim();

                // Check if field is one character (like P, E, N, Z, D) or a common header name
                // Replace the condition with this:
                if ((field1.Length == 1 && !double.TryParse(field1, out _)) ||
                    field1.Equals("Point", StringComparison.OrdinalIgnoreCase) ||
                    field1.Equals("Easting", StringComparison.OrdinalIgnoreCase) ||
                    field1.Equals("Northing", StringComparison.OrdinalIgnoreCase) ||
                    field1.Equals("Elevation", StringComparison.OrdinalIgnoreCase) ||
                    field1.Equals("Description", StringComparison.OrdinalIgnoreCase))
                {
                    firstRowHasText = true;
                }

                // Check if the second row has parseable numbers (at least for coordinates)
                if (i > 0 && i <= 3 && secondRowFields.Length > i)
                {
                    if (double.TryParse(secondRowFields[i], NumberStyles.Any, CultureInfo.InvariantCulture, out _))
                    {
                        secondRowHasNumbers = true;
                    }
                }
            }

            return firstRowHasText && secondRowHasNumbers;
        }
    }
}