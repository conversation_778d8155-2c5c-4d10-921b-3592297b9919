﻿using System;
using System.Text.RegularExpressions;

namespace SPM_NET46_2017_2018.Utils
{
    public static class LayerNameValidator
    {
        /// <summary>
        /// Validates and cleans a layer name according to AutoCAD's naming rules.
        /// Allowed characters: letters (A-Z, a-z), digits (0-9), hyphens (-), underscores (_), dollar signs ($).
        /// Spaces are replaced with underscores. The name is limited to 255 characters.
        /// </summary>
        /// <param name="input">The input layer name.</param>
        /// <returns>A valid layer name adhering to AutoCAD's rules.</returns>
        public static string Validate(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            try
            {
                // Replace any whitespace with underscores
                string result = Regex.Replace(input, @"\s+", "_");
                // Remove any disallowed characters
                result = Regex.Replace(result, @"[^A-Za-z0-9\-_\$]", "");
                // Ensure the layer name does not exceed 255 characters
                if (result.Length > 255)
                    result = result.Substring(0, 255);

                return result;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Error validating layer name.", ex);
            }
        }
    }
}
