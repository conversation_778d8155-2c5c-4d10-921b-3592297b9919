﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <configSections>
    </configSections>
    <runtime>
        <AppContextSwitchOverrides value="Switch.System.Net.DontEnableSchUseStrongCrypto=false" />
        <!-- Fix for .NET Framework security policy - allows loading from network locations -->
        <loadFromRemoteSources enabled="true" />
        <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
            <dependentAssembly>
                <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.3.0" />
            </dependentAssembly>
        </assemblyBinding>
    </runtime>
    <system.net>
        <settings>
            <servicePointManager expect100Continue="false" />
        </settings>
    </system.net>
</configuration>