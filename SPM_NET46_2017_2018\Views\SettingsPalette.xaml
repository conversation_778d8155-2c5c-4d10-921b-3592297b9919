﻿<UserControl x:Class="SPM_NET46_2017_2018.Views.SettingsPalette"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:SPM_NET46_2017_2018.Views"
             xmlns:partialViews="clr-namespace:SPM_NET46_2017_2018.PartialViews"
                Width="400"
    Height="600"
    MinWidth="400"
    MinHeight="600"
    mc:Ignorable="d">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/SPM_NET46_2017_2018;component/ResourceDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid Background="#FFECECEC">

        <!--  Main Grid Definition  -->
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>


        <!--#region Header-->
        <partialViews:Header
            Grid.Row="0"
            HorizontalAlignment="Stretch"
            VerticalAlignment="Top"
            BreadcrumbText="SETTING" />
        <!--#endregion-->


        <!--#region Content-->
        <TabControl
            Grid.Row="1"
            Margin="0,20,0,0"
            HorizontalAlignment="Center">

            <!--#region Layers and Points-->
            <TabItem
                Width="105"
                Header="Layers &amp; Points"
                ToolTip="Configure point layer settings">
                <Grid Margin="5">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>

                    <!--#region Layer Settings Section-->
                    <GroupBox
                        Grid.Row="0"
                        FontFamily="{StaticResource PoppinsFont}"
                        Header="Layer Settings"
                        Style="{StaticResource RoundedGroupBox}"
                        ToolTip="Configure layer names and colors for points and tables">
                        <Grid Margin="5">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="30" />
                                <RowDefinition Height="30" />
                                <RowDefinition Height="30" />
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="117" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="16" />
                            </Grid.ColumnDefinitions>



                            <!--  Point Layer Settings  -->
                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="0"
                                VerticalAlignment="Center"
                                FontSize="12"
                                Text="Point Layer:"
                                ToolTip="Layer name for point geometry" />
                            <TextBox
                                x:Name="txtPointLayer"
                                Grid.Row="0"
                                Grid.Column="1"
                                Width="116"
                                FontSize="11.6"
                                Foreground="#FF364BA4"
                                Style="{StaticResource RoundedTextBox}"
                                ToolTip="Enter the AutoCAD layer name for point geometry" />

                            <Rectangle
                                x:Name="rectPointLayerColor"
                                Grid.Row="0"
                                Grid.Column="2"
                                Width="15"
                                Height="15"
                                Cursor="Hand"
                                Fill="White"
                                MouseLeftButtonDown="ChangeColorSetting"
                                RadiusX="4"
                                RadiusY="4"
                                Stroke="#FFC9C8D6"
                                ToolTip="Click to change the point layer color" />

                            <!--  Point Number Layer Settings  -->
                            <TextBlock
                                Grid.Row="1"
                                Grid.Column="0"
                                VerticalAlignment="Center"
                                FontSize="11.5"
                                Text="Point Number Layer:"
                                ToolTip="Layer name for point number text" />
                            <TextBox
                                x:Name="txtPointNumberLayer"
                                Grid.Row="1"
                                Grid.Column="1"
                                Width="116"
                                FontSize="11"
                                Foreground="#FF364BA4"
                                Style="{StaticResource RoundedTextBox}"
                                ToolTip="Enter the AutoCAD layer name for point number labels" />

                            <Rectangle
                                x:Name="rectPointNumberLayerColor"
                                Grid.Row="1"
                                Grid.Column="2"
                                Width="15"
                                Height="15"
                                Cursor="Hand"
                                Fill="White"
                                MouseLeftButtonDown="ChangeColorSetting"
                                RadiusX="4"
                                RadiusY="4"
                                Stroke="#FFC9C8D6"
                                ToolTip="Click to change the point number layer color" />

                            <!--  Table Layer Settings  -->
                            <TextBlock
                                Grid.Row="2"
                                Grid.Column="0"
                                VerticalAlignment="Center"
                                FontSize="12"
                                Text="Table Layer:"
                                ToolTip="Layer name for data tables" />
                            <TextBox
                                x:Name="txtTableLayer"
                                Grid.Row="2"
                                Grid.Column="1"
                                Width="116"
                                FontSize="11.6"
                                Foreground="#FF364BA4"
                                Style="{StaticResource RoundedTextBox}"
                                ToolTip="Enter the AutoCAD layer name for data tables" />

                            <Rectangle
                                x:Name="rectTableLayerColor"
                                Grid.Row="2"
                                Grid.Column="2"
                                Width="15"
                                Height="15"
                                Cursor="Hand"
                                Fill="White"
                                MouseLeftButtonDown="ChangeColorSetting"
                                RadiusX="4"
                                RadiusY="4"
                                Stroke="#FFC9C8D6"
                                ToolTip="Click to change the table layer color" />

                        </Grid>
                    </GroupBox>
                    <!--#endregion-->

                    <!--#region Point Options-->
                    <GroupBox
                        Grid.Row="1"
                        Height="100"
                        VerticalAlignment="Top"
                        Header="Point Options"
                        Style="{StaticResource RoundedGroupBox}">
                        <Grid Height="52" Margin="5">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <StackPanel
                                x:Name="borPointStylePreview"
                                Width="25"
                                Height="25"
                                Margin="0,-77,-165,0"
                                HorizontalAlignment="Right"
                                Cursor="Hand"
                                ToolTip="Click to open AutoCAD point style dialog"
                                Visibility="Collapsed">

                                <Viewbox
                                    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                                    Width="15"
                                    Height="15"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Stretch="Uniform">

                                    <Canvas Width="30" Height="30">
                                        <Ellipse
                                            Canvas.Left="24.169999999999998"
                                            Canvas.Top="28.27"
                                            Width="5.1"
                                            Height="5.1"
                                            Fill="#FFAACAE6" />

                                        <Path
                                            Stroke="#FF6D7C90"
                                            StrokeEndLineCap="Round"
                                            StrokeStartLineCap="Round"
                                            StrokeThickness="2px">
                                            <Path.Data>
                                                <PathGeometry Figures="M28.22 22.46a4.2 4.2 0 0 0-1.5-.13 4.2 4.2 0 0 0-1.49.13 1.73 1.73 0 0 0-.92.92 2 2 0 0 0-.12.73 1.39 1.39 0 0 1-.68 1.15 1.35 1.35 0 0 1-1.32 0 2.3 2.3 0 0 0-.7-.26 1.68 1.68 0 0 0-1.25.33 4.3 4.3 0 0 0-.87 1.24 4.1 4.1 0 0 0-.63 1.36 1.68 1.68 0 0 0 .33 1.25 2 2 0 0 0 .58.47 1.4 1.4 0 0 1 .66 1.16 1.37 1.37 0 0 1-.66 1.19 2.1 2.1 0 0 0-.58.48 1.68 1.68 0 0 0-.33 1.25 4.2 4.2 0 0 0 .63 1.36 4.1 4.1 0 0 0 .87 1.23 1.65 1.65 0 0 0 1.25.34 2.3 2.3 0 0 0 .7-.26 1.35 1.35 0 0 1 1.32 0 1.37 1.37 0 0 1 .68 1.14 2 2 0 0 0 .12.74 1.73 1.73 0 0 0 .92.92 4.5 4.5 0 0 0 1.49.12 4.6 4.6 0 0 0 1.5-.12 1.73 1.73 0 0 0 .92-.92 2 2 0 0 0 .12-.74 1.34 1.34 0 0 1 2-1.15 2.2 2.2 0 0 0 .7.26 1.67 1.67 0 0 0 1.25-.34 3.9 3.9 0 0 0 .86-1.23 4 4 0 0 0 .64-1.36 1.65 1.65 0 0 0-.34-1.25 2.1 2.1 0 0 0-.57-.47 1.33 1.33 0 0 1 0-2.3 2.1 2.1 0 0 0 .57-.48 1.65 1.65 0 0 0 .34-1.25 4.2 4.2 0 0 0-.64-1.36 4.2 4.2 0 0 0-.86-1.24A1.7 1.7 0 0 0 32 25a2.2 2.2 0 0 0-.7.26 1.34 1.34 0 0 1-2-1.16 2 2 0 0 0-.12-.73 1.73 1.73 0 0 0-.96-.91Z" />
                                            </Path.Data>
                                        </Path>

                                        <Path
                                            Stroke="#FF6D7C90"
                                            StrokeEndLineCap="Round"
                                            StrokeStartLineCap="Round"
                                            StrokeThickness="2px">
                                            <Path.Data>
                                                <PathGeometry Figures="M17.9 36.41a6.22 6.22 0 0 1-5.3 0C5.38 33 .75 23.6.75 15.51A14.63 14.63 0 0 1 15.25.75a14.63 14.63 0 0 1 14.5 14.76 23.8 23.8 0 0 1-.59 5.18" />
                                            </Path.Data>
                                        </Path>

                                        <Ellipse
                                            Canvas.Left="9.809999999999999"
                                            Canvas.Top="9.809999999999999"
                                            Width="10.88"
                                            Height="10.88"
                                            Fill="#FFAACAE6" />
                                    </Canvas>
                                </Viewbox>
                            </StackPanel>

                            <!--  Display Points Options here  -->
                            <Border
                                x:Name="borderPointDisplayImage"
                                Grid.Row="0"
                                Grid.Column="2"
                                Width="40"
                                Height="40"
                                Background="#FFF0EFF1"
                                BorderBrush="#FFF0EFF1"
                                BorderThickness="1"
                                CornerRadius="4"
                                MouseLeftButtonDown="borderPointDisplayImage_MouseLeftButtonDown"
                                ToolTip="Click to change point style">

                                <Border.Resources>
                                    <Storyboard x:Key="MouseOverAnimation">
                                        <ColorAnimation
                                            Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                            To="#E0E9FF"
                                            Duration="0:0:0.3" />
                                        <ColorAnimation
                                            Storyboard.TargetProperty="(Border.BorderBrush).(SolidColorBrush.Color)"
                                            To="#6495ED"
                                            Duration="0:0:0.3" />
                                    </Storyboard>

                                    <Storyboard x:Key="MouseLeaveAnimation">
                                        <ColorAnimation
                                            Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                            To="#FFF0EFF1"
                                            Duration="0:0:0.3" />
                                        <ColorAnimation
                                            Storyboard.TargetProperty="(Border.BorderBrush).(SolidColorBrush.Color)"
                                            To="#FFF0EFF1"
                                            Duration="0:0:0.3" />
                                    </Storyboard>

                                    <Storyboard x:Key="PressedAnimation">
                                        <DoubleAnimation
                                            Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                            To="0.92"
                                            Duration="0:0:0.1" />
                                        <DoubleAnimation
                                            Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                            To="0.92"
                                            Duration="0:0:0.1" />
                                        <ColorAnimation
                                            Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                            To="#B0C4DE"
                                            Duration="0:0:0.1" />
                                    </Storyboard>

                                    <Storyboard x:Key="ReleasedAnimation">
                                        <DoubleAnimation
                                            Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                            To="1.0"
                                            Duration="0:0:0.2" />
                                        <DoubleAnimation
                                            Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                            To="1.0"
                                            Duration="0:0:0.2" />
                                    </Storyboard>
                                </Border.Resources>

                                <Border.RenderTransform>
                                    <ScaleTransform ScaleX="1" ScaleY="1" />
                                </Border.RenderTransform>

                                <Border.Triggers>
                                    <EventTrigger RoutedEvent="MouseEnter">
                                        <BeginStoryboard Storyboard="{StaticResource MouseOverAnimation}" />
                                    </EventTrigger>
                                    <EventTrigger RoutedEvent="MouseLeave">
                                        <BeginStoryboard Storyboard="{StaticResource MouseLeaveAnimation}" />
                                    </EventTrigger>
                                    <EventTrigger RoutedEvent="PreviewMouseLeftButtonDown">
                                        <BeginStoryboard Storyboard="{StaticResource PressedAnimation}" />
                                    </EventTrigger>
                                    <EventTrigger RoutedEvent="PreviewMouseLeftButtonUp">
                                        <BeginStoryboard Storyboard="{StaticResource ReleasedAnimation}" />
                                    </EventTrigger>
                                </Border.Triggers>

                                <Grid>
                                    <!--  0 - Default Point (Small dot)  -->
                                    <Viewbox
                                        x:Name="Index0"
                                        Width="5"
                                        Height="5"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Stretch="Uniform"
                                        ToolTip="Default point marker: Simple 2x2 dot"
                                        Visibility="Collapsed">
                                        <Canvas Width="2" Height="2">
                                            <Ellipse
                                                Canvas.Left="0.75"
                                                Canvas.Top="0.75"
                                                Width="2"
                                                Height="2"
                                                Stroke="#FF7C7E9A"
                                                StrokeThickness="1.5" />
                                        </Canvas>
                                    </Viewbox>

                                    <!--  1 - Invisible Point  -->
                                    <TextBlock
                                        x:Name="Index1"
                                        Height="15"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        FontSize="12"
                                        Text="Nothing"
                                        TextAlignment="Center"
                                        ToolTip="Invisible point marker (no visual representation)"
                                        Visibility="Collapsed" />

                                    <!--  2 - Cross Symbol (+)  -->
                                    <Viewbox
                                        x:Name="Index2"
                                        Width="28"
                                        Height="28"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Stretch="Uniform"
                                        ToolTip="Cross symbol (+) point marker"
                                        Visibility="Collapsed">
                                        <Canvas Width="39" Height="39">
                                            <Path
                                                Stroke="#FF7C7E9A"
                                                StrokeEndLineCap="Round"
                                                StrokeStartLineCap="Round"
                                                StrokeThickness="1.5">
                                                <Path.Data>
                                                    <PathGeometry Figures="M19.75.75v38m-19-19.5h38" />
                                                </Path.Data>
                                            </Path>
                                        </Canvas>
                                    </Viewbox>

                                    <!--  3 - X Symbol (×)  -->
                                    <Viewbox
                                        x:Name="Index3"
                                        Width="28"
                                        Height="28"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Stretch="Uniform"
                                        ToolTip="X symbol (×) point marker"
                                        Visibility="Collapsed">
                                        <Canvas Width="28.5" Height="28.5">
                                            <Path
                                                Stroke="#FF7C7E9A"
                                                StrokeEndLineCap="Round"
                                                StrokeStartLineCap="Round"
                                                StrokeThickness="1.5">
                                                <Path.Data>
                                                    <PathGeometry Figures="m27.75.75-27 27m0-27 27 27" />
                                                </Path.Data>
                                            </Path>
                                        </Canvas>
                                    </Viewbox>

                                    <!--  4 - Vertical Tick Mark  -->
                                    <Viewbox
                                        x:Name="Index4"
                                        Width="15"
                                        Height="15"
                                        Margin="0,5,0,0"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Top"
                                        Stretch="Uniform"
                                        ToolTip="Vertical tick mark point indicator"
                                        Visibility="Collapsed">
                                        <Canvas Width="1.5" Height="15.5">
                                            <Path
                                                Stroke="#FF7C7E9A"
                                                StrokeEndLineCap="Round"
                                                StrokeStartLineCap="Round"
                                                StrokeThickness="1.5">
                                                <Path.Data>
                                                    <PathGeometry Figures="M.75.75v14" />
                                                </Path.Data>
                                            </Path>
                                        </Canvas>
                                    </Viewbox>

                                    <!--  32 - Dot Inside Circle  -->
                                    <Viewbox
                                        x:Name="Index32"
                                        Width="28"
                                        Height="28"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Stretch="Uniform"
                                        ToolTip="Circular point marker with central dot"
                                        Visibility="Collapsed">
                                        <Canvas Width="29.5" Height="29.5">
                                            <Ellipse
                                                Canvas.Left="0.75"
                                                Canvas.Top="0.75"
                                                Width="28"
                                                Height="28"
                                                Stroke="#FF7C7E9A"
                                                StrokeThickness="1.5" />
                                            <Ellipse
                                                Canvas.Left="13.5"
                                                Canvas.Top="13.5"
                                                Width="3"
                                                Height="3"
                                                Stroke="#FF7C7E9A"
                                                StrokeThickness="1.5" />
                                        </Canvas>
                                    </Viewbox>

                                    <!--  33 - Solid Circle  -->
                                    <Viewbox
                                        x:Name="Index33"
                                        Width="28"
                                        Height="28"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Stretch="Uniform"
                                        ToolTip="Solid circular point marker"
                                        Visibility="Collapsed">
                                        <Canvas Width="29.5" Height="29.5">
                                            <Ellipse
                                                Canvas.Left="0.75"
                                                Canvas.Top="0.75"
                                                Width="28"
                                                Height="28"
                                                Stroke="#FF7C7E9A"
                                                StrokeThickness="1.5" />
                                        </Canvas>
                                    </Viewbox>

                                    <!--  34 - Circle with Cross  -->
                                    <Viewbox
                                        x:Name="Index34"
                                        Width="28"
                                        Height="28"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Stretch="Uniform"
                                        ToolTip="Circular marker with crosshair (+)"
                                        Visibility="Collapsed">
                                        <Canvas Width="39.5" Height="39.5">
                                            <Ellipse
                                                Canvas.Left="5.65"
                                                Canvas.Top="5.12"
                                                Width="28"
                                                Height="28"
                                                Stroke="#FF7C7E9A"
                                                StrokeThickness="1.5" />
                                            <Path
                                                Stroke="#FF7C7E9A"
                                                StrokeEndLineCap="Round"
                                                StrokeStartLineCap="Round"
                                                StrokeThickness="1.5">
                                                <Path.Data>
                                                    <PathGeometry Figures="M19.75.75v38m-19-19.5h38" />
                                                </Path.Data>
                                            </Path>
                                        </Canvas>
                                    </Viewbox>

                                    <!--  35 - Circle with X  -->
                                    <Viewbox
                                        x:Name="Index35"
                                        Width="28"
                                        Height="28"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Stretch="Uniform"
                                        ToolTip="Circular marker with X symbol (×)"
                                        Visibility="Collapsed">
                                        <Canvas Width="28.5" Height="28.5">
                                            <Path Fill="#FF7C7E9A">
                                                <Path.Data>
                                                    <PathGeometry Figures="M14.25 1.75a12.5 12.5 0 1 1-12.5 12.5 12.52 12.52 0 0 1 12.5-12.5m0-1.5a14 14 0 1 0 14 14 14 14 0 0 0-14-14" />
                                                </Path.Data>
                                            </Path>
                                            <Path
                                                Stroke="#FF7C7E9A"
                                                StrokeEndLineCap="Round"
                                                StrokeStartLineCap="Round"
                                                StrokeThickness="1.5">
                                                <Path.Data>
                                                    <PathGeometry Figures="m27.75.75-27 27m0-27 27 27" />
                                                </Path.Data>
                                            </Path>
                                        </Canvas>
                                    </Viewbox>

                                    <!--  36 - Circle with Tick  -->
                                    <Viewbox
                                        x:Name="Index36"
                                        Width="28"
                                        Height="28"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Stretch="Uniform"
                                        ToolTip="Circular marker with vertical tick"
                                        Visibility="Collapsed">
                                        <Canvas Width="28" Height="28">
                                            <Path Fill="#FF7C7E9A">
                                                <Path.Data>
                                                    <PathGeometry Figures="M14 1.5A12.5 12.5 0 1 1 1.5 14 12.52 12.52 0 0 1 14 1.5M14 0a14 14 0 1 0 14 14A14 14 0 0 0 14 0" />
                                                </Path.Data>
                                            </Path>
                                            <Path
                                                Stroke="#FF7C7E9A"
                                                StrokeEndLineCap="Round"
                                                StrokeStartLineCap="Round"
                                                StrokeThickness="1.5">
                                                <Path.Data>
                                                    <PathGeometry Figures="M14 1v13" />
                                                </Path.Data>
                                            </Path>
                                        </Canvas>
                                    </Viewbox>

                                    <!--  64 - Square with Dot  -->
                                    <Viewbox
                                        x:Name="Index64"
                                        Width="28"
                                        Height="28"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Stretch="Uniform"
                                        ToolTip="Square marker with central dot"
                                        Visibility="Collapsed">
                                        <Canvas Width="28" Height="28">
                                            <Path Fill="#FF7C7E9A">
                                                <Path.Data>
                                                    <PathGeometry Figures="M26.5 1.5v25h-25v-25zM28 0H0v28h28z" />
                                                </Path.Data>
                                            </Path>
                                            <Ellipse
                                                Canvas.Left="12.5"
                                                Canvas.Top="12.5"
                                                Width="3"
                                                Height="3"
                                                Stroke="#FF7C7E9A"
                                                StrokeThickness="1.5" />
                                        </Canvas>
                                    </Viewbox>

                                    <!--  65 - Solid Square  -->
                                    <Viewbox
                                        x:Name="Index65"
                                        Width="28"
                                        Height="28"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Stretch="Uniform"
                                        ToolTip="Solid square point marker"
                                        Visibility="Collapsed">
                                        <Canvas Width="28" Height="28">
                                            <Path Fill="#FF7C7E9A">
                                                <Path.Data>
                                                    <PathGeometry Figures="M26.5 1.5v25h-25v-25zM28 0H0v28h28z" />
                                                </Path.Data>
                                            </Path>
                                        </Canvas>
                                    </Viewbox>

                                    <!--  66 - Square with Cross  -->
                                    <Viewbox
                                        x:Name="Index66"
                                        Width="28"
                                        Height="28"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Stretch="Uniform"
                                        ToolTip="Square marker with crosshair (+)"
                                        Visibility="Collapsed">
                                        <Canvas Width="39.5" Height="39.5">
                                            <Path Fill="#FF7C7E9A">
                                                <Path.Data>
                                                    <PathGeometry Figures="M32.25 7.25v25h-25v-25zm1.5-1.5h-28v28h28z" />
                                                </Path.Data>
                                            </Path>
                                            <Path
                                                Stroke="#FF7C7E9A"
                                                StrokeEndLineCap="Round"
                                                StrokeStartLineCap="Round"
                                                StrokeThickness="1.5">
                                                <Path.Data>
                                                    <PathGeometry Figures="M19.75.75v38m-19-19h38" />
                                                </Path.Data>
                                            </Path>
                                        </Canvas>
                                    </Viewbox>

                                    <!--  67 - Square with X  -->
                                    <Viewbox
                                        x:Name="Index67"
                                        Width="28"
                                        Height="28"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Stretch="Uniform"
                                        ToolTip="Square marker with X symbol (×)"
                                        Visibility="Collapsed">
                                        <Canvas Width="28.06" Height="28.06">
                                            <Path Fill="#FF7C7E9A">
                                                <Path.Data>
                                                    <PathGeometry Figures="M26.53 1.53v25h-25v-25zM28 0H0v28h28z" />
                                                </Path.Data>
                                            </Path>
                                            <Path
                                                Stroke="#FF7C7E9A"
                                                StrokeEndLineCap="Round"
                                                StrokeStartLineCap="Round"
                                                StrokeThickness="1.5">
                                                <Path.Data>
                                                    <PathGeometry Figures="m27.53.53-27 27m0-27 27 27" />
                                                </Path.Data>
                                            </Path>
                                        </Canvas>
                                    </Viewbox>

                                    <!--  68 - Square with Vertical Slash  -->
                                    <Viewbox
                                        x:Name="Index68"
                                        Width="28"
                                        Height="28"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Stretch="Uniform"
                                        ToolTip="Square marker with vertical line"
                                        Visibility="Collapsed">
                                        <Canvas Width="28" Height="28">
                                            <Path Fill="#FF7C7E9A">
                                                <Path.Data>
                                                    <PathGeometry Figures="M26.5 1.5v25h-25v-25zM28 0H0v28h28z" />
                                                </Path.Data>
                                            </Path>
                                            <Path
                                                Stroke="#FF7C7E9A"
                                                StrokeEndLineCap="Round"
                                                StrokeStartLineCap="Round"
                                                StrokeThickness="1.5">
                                                <Path.Data>
                                                    <PathGeometry Figures="M14 15V1" />
                                                </Path.Data>
                                            </Path>
                                        </Canvas>
                                    </Viewbox>

                                    <!--  96 - Square with Dot Inside Circle  -->
                                    <Viewbox
                                        x:Name="Index96"
                                        Width="28"
                                        Height="28"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Stretch="Uniform"
                                        ToolTip="Composite marker: Square + Circle + Dot"
                                        Visibility="Collapsed">
                                        <Canvas Width="28" Height="28">
                                            <Path Fill="#FF7C7E9A">
                                                <Path.Data>
                                                    <PathGeometry Figures="M14 1.5A12.5 12.5 0 1 1 1.5 14 12.52 12.52 0 0 1 14 1.5M14 0a14 14 0 1 0 14 14A14 14 0 0 0 14 0" />
                                                </Path.Data>
                                            </Path>
                                            <Path Fill="#FF7C7E9A">
                                                <Path.Data>
                                                    <PathGeometry Figures="M26.5 1.5v25h-25v-25zM28 0H0v28h28z" />
                                                </Path.Data>
                                            </Path>
                                            <Ellipse
                                                Canvas.Left="12.7"
                                                Canvas.Top="12.5"
                                                Width="3"
                                                Height="3"
                                                Stroke="#FF7C7E9A"
                                                StrokeThickness="1.5" />
                                        </Canvas>
                                    </Viewbox>

                                    <!--  97 - Square with Circle Inside  -->
                                    <Viewbox
                                        x:Name="Index97"
                                        Width="28"
                                        Height="28"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Stretch="Uniform"
                                        ToolTip="Composite marker: Square + Circle"
                                        Visibility="Collapsed">
                                        <Canvas Width="28" Height="28">
                                            <Path Fill="#FF7C7E9A">
                                                <Path.Data>
                                                    <PathGeometry Figures="M14 1.5A12.5 12.5 0 1 1 1.5 14 12.52 12.52 0 0 1 14 1.5M14 0a14 14 0 1 0 14 14A14 14 0 0 0 14 0" />
                                                </Path.Data>
                                            </Path>
                                            <Path Fill="#FF7C7E9A">
                                                <Path.Data>
                                                    <PathGeometry Figures="M26.5 1.5v25h-25v-25zM28 0H0v28h28z" />
                                                </Path.Data>
                                            </Path>
                                        </Canvas>
                                    </Viewbox>

                                    <!--  98 - Square with Cross Inside Circle  -->
                                    <Viewbox
                                        x:Name="Index98"
                                        Width="28"
                                        Height="28"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Stretch="Uniform"
                                        ToolTip="Composite marker: Square + Circle + Cross"
                                        Visibility="Collapsed">
                                        <Canvas Width="39.5" Height="39.5">
                                            <Path Fill="#FF7C7E9A">
                                                <Path.Data>
                                                    <PathGeometry Figures="M32.25 6.89v25h-25v-25zm1.5-1.5h-28v28h28z" />
                                                </Path.Data>
                                            </Path>
                                            <Path
                                                Stroke="#FF7C7E9A"
                                                StrokeEndLineCap="Round"
                                                StrokeStartLineCap="Round"
                                                StrokeThickness="1.5">
                                                <Path.Data>
                                                    <PathGeometry Figures="M19.75.75v38m-19-19.5h38" />
                                                </Path.Data>
                                            </Path>
                                            <Ellipse
                                                Canvas.Left="6.79"
                                                Canvas.Top="6.29"
                                                Width="26"
                                                Height="26"
                                                Stroke="#FF7C7E9A"
                                                StrokeThickness="1.5" />
                                        </Canvas>
                                    </Viewbox>

                                    <!--  99 - Square with X Inside Circle  -->
                                    <Viewbox
                                        x:Name="Index99"
                                        Width="28"
                                        Height="28"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Stretch="Uniform"
                                        ToolTip="Composite marker: Square + Circle + X"
                                        Visibility="Collapsed">
                                        <Canvas Width="28.06" Height="28.06">
                                            <Path Fill="#FF7C7E9A">
                                                <Path.Data>
                                                    <PathGeometry Figures="M14 1.53A12.5 12.5 0 1 1 1.53 14 12.52 12.52 0 0 1 14 1.53M14 0a14 14 0 1 0 14 14A14 14 0 0 0 14 0" />
                                                </Path.Data>
                                            </Path>
                                            <Path Fill="#FF7C7E9A">
                                                <Path.Data>
                                                    <PathGeometry Figures="M26.53 1.53v25h-25v-25zM28 0H0v28h28z" />
                                                </Path.Data>
                                            </Path>
                                            <Path
                                                Stroke="#FF7C7E9A"
                                                StrokeEndLineCap="Round"
                                                StrokeStartLineCap="Round"
                                                StrokeThickness="1.5">
                                                <Path.Data>
                                                    <PathGeometry Figures="m27.53.53-27 27m0-27 27 27" />
                                                </Path.Data>
                                            </Path>
                                        </Canvas>
                                    </Viewbox>

                                    <!--  100 - Square with Vertical Tick in Circle  -->
                                    <Viewbox
                                        x:Name="Index100"
                                        Width="28"
                                        Height="28"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Stretch="Uniform"
                                        ToolTip="Composite marker: Square + Circle + Vertical Tick"
                                        Visibility="Collapsed">
                                        <Canvas Width="28" Height="28">
                                            <Path Fill="#FF7C7E9A">
                                                <Path.Data>
                                                    <PathGeometry Figures="M14 1.5A12.5 12.5 0 1 1 1.5 14 12.52 12.52 0 0 1 14 1.5M14 0a14 14 0 1 0 14 14A14 14 0 0 0 14 0" />
                                                </Path.Data>
                                            </Path>
                                            <Path Fill="#FF7C7E9A">
                                                <Path.Data>
                                                    <PathGeometry Figures="M26.5 1.5v25h-25v-25zM28 0H0v28h28z" />
                                                </Path.Data>
                                            </Path>
                                            <Path
                                                Stroke="#FF7C7E9A"
                                                StrokeEndLineCap="Round"
                                                StrokeStartLineCap="Round"
                                                StrokeThickness="1.5">
                                                <Path.Data>
                                                    <PathGeometry Figures="M14 14.5v-13" />
                                                </Path.Data>
                                            </Path>
                                        </Canvas>
                                    </Viewbox>
                                </Grid>
                            </Border>

                            <!--  Point Size Controls  -->
                            <StackPanel
                                Grid.Row="0"
                                Grid.Column="1"
                                Orientation="Vertical">
                                <TextBlock
                                    Height="15"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    FontFamily="{StaticResource PoppinsFont}"
                                    FontSize="13"
                                    Text="Point Size:" />


                                <TextBox
                                    x:Name="txtPointSize"
                                    Width="70"
                                    MinHeight="12"
                                    Margin="0,4,0,0"
                                    HorizontalAlignment="Center"
                                    HorizontalContentAlignment="Center"
                                    VerticalContentAlignment="Center"
                                    FontSize="12"
                                    Foreground="#FF364BA4"
                                    Style="{StaticResource RoundedTextBox}" />
                            </StackPanel>

                            <!--  Point Size Mode Label  -->
                            <TextBlock
                                x:Name="txbPointSizeMode"
                                Grid.Row="0"
                                Grid.Column="0"
                                Width="72"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FontSize="12"
                                TextWrapping="Wrap" />

                            <!--  end  -->

                        </Grid>
                    </GroupBox>
                    <!--#endregion-->

                    <!--#region Point Number Height-->
                    <GroupBox
                        Grid.Row="2"
                        Height="90"
                        VerticalAlignment="Top"
                        FontFamily="{StaticResource PoppinsFont}"
                        Header="Point Number Settings"
                        Style="{StaticResource RoundedGroupBox}"
                        ToolTip="Configure how point numbers appear in the drawing">
                        <Grid Margin="5">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="37" />
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <TextBlock
                                Grid.Column="0"
                                Height="20"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Text="Point Number Height"
                                ToolTip="Label indicating the text size control for point numbers" />
                            <!--  PointSizeTextBox_PreviewTextInput  -->
                            <TextBox
                                x:Name="txtPointNumberTextHeight"
                                Grid.Column="1"
                                Width="70"
                                Height="25"
                                Padding="3,3,3,3"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Center"
                                HorizontalContentAlignment="Center"
                                VerticalContentAlignment="Center"
                                FontSize="12"
                                Foreground="#FF364BA4"
                                Style="{StaticResource RoundedTextBox}"
                                ToolTip="Enter the text height for point numbers (in drawing units)" />
                        </Grid>
                    </GroupBox>
                    <!--#endregion-->
                </Grid>
            </TabItem>
            <!--#endregion-->

            <!--#region Data-->
            <TabItem Width="70" Header="Data">
                <Grid Width="Auto" Height="Auto">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="70" />
                    </Grid.RowDefinitions>

                    <Grid.ColumnDefinitions>
                        <ColumnDefinition />
                    </Grid.ColumnDefinitions>

                    <!--#region Table Options-->
                    <GroupBox
                        Grid.Row="0"
                        Height="169"
                        Margin="5"
                        VerticalAlignment="Top"
                        FontFamily="{StaticResource PoppinsFont}"
                        Header="Table Options"
                        Style="{StaticResource RoundedGroupBox}">
                        <Grid Margin="5">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*" />
                                <RowDefinition Height="*" />
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>

                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <TextBlock
                                Width="68"
                                Height="15"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FontSize="12"
                                Text="Table Style:" />

                            <TextBox
                                x:Name="txtTableStyle"
                                Grid.Row="0"
                                Grid.Column="1"
                                Width="80"
                                Height="27"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Center"
                                FontSize="11.5"
                                Foreground="#FF364BA4"
                                Style="{StaticResource RoundedTextBox}" />

                            <TextBlock
                                Grid.Row="1"
                                Width="62"
                                Height="15"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                FontSize="12"
                                Text="Max Rows:" />

                            <TextBox
                                x:Name="txtMaxRowsPerTable"
                                Grid.Row="1"
                                Grid.Column="1"
                                Width="80"
                                Height="27"
                                HorizontalAlignment="Left"
                                FontSize="11.5"
                                Foreground="#FF364BA4"
                                Style="{StaticResource RoundedTextBox}" />


                            <CheckBox
                                x:Name="chkShowTableTitle"
                                Grid.Row="2"
                                Grid.Column="0"
                                VerticalContentAlignment="Center"
                                Background="#FFCDD7E8"
                                BorderBrush="{x:Null}"
                                Content="Table Title"
                                FontSize="12"
                                Foreground="#FF687A99"
                                IsChecked="True" />

                            <TextBox
                                x:Name="txtTableTitle"
                                Grid.Row="2"
                                Grid.Column="1"
                                Width="80"
                                Height="27"
                                HorizontalAlignment="Left"
                                FontSize="11.5"
                                Foreground="#FF364BA4"
                                Style="{StaticResource RoundedTextBox}" />

                            <StackPanel
                                Grid.RowSpan="3"
                                Grid.Column="2"
                                Margin="3,7,0,0">

                                <CheckBox
                                    x:Name="chkAllTableColumns"
                                    Margin="0,0,0,5"
                                    VerticalContentAlignment="Center"
                                    Background="#FFCDD7E8"
                                    BorderBrush="{x:Null}"
                                    Click="chkAllTableColumns_Click"
                                    Content="All Columns"
                                    FontSize="11"
                                    FontWeight="DemiBold"
                                    Foreground="#FF687A99"
                                    IsChecked="True"
                                    IsThreeState="True" />

                                <CheckBox
                                    x:Name="chkShowPointNumber"
                                    Margin="13,0,0,5"
                                    VerticalContentAlignment="Center"
                                    Background="#FFCDD7E8"
                                    BorderBrush="{x:Null}"
                                    Content="Point Number"
                                    FontSize="9.4"
                                    Foreground="#FF687A99"
                                    IsChecked="True" />

                                <CheckBox
                                    x:Name="chkDisplayXY"
                                    Margin="13,0,0,5"
                                    VerticalContentAlignment="Center"
                                    Background="#FFCDD7E8"
                                    BorderBrush="{x:Null}"
                                    Content="X, Y Coord."
                                    FontSize="10"
                                    Foreground="#FF687A99"
                                    IsChecked="True"
                                    IsEnabled="False" />

                                <CheckBox
                                    x:Name="chkShowElevation"
                                    Margin="13,0,0,5"
                                    VerticalContentAlignment="Center"
                                    Background="#FFCDD7E8"
                                    BorderBrush="{x:Null}"
                                    Content="Elevation"
                                    FontSize="10"
                                    Foreground="#FF687A99"
                                    IsChecked="True" />

                                <CheckBox
                                    x:Name="chkShowDescription"
                                    Margin="13,0,0,0"
                                    VerticalContentAlignment="Center"
                                    Background="#FFCDD7E8"
                                    BorderBrush="{x:Null}"
                                    Content="Description"
                                    FontSize="10"
                                    Foreground="#FF687A99"
                                    IsChecked="True" />
                            </StackPanel>



                        </Grid>
                    </GroupBox>
                    <!--#endregion-->

                    <!--#region Record History-->
                    <GroupBox
                        Grid.Row="1"
                        Height="102"
                        VerticalAlignment="Top"
                        FontFamily="{StaticResource PoppinsFont}"
                        Header="Record History"
                        Style="{StaticResource RoundedGroupBox}">
                        <Grid Margin="5">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <CheckBox
                                x:Name="chkRecordHistory"
                                Grid.Column="0"
                                Width="128"
                                Height="25"
                                Padding="5,0,0,0"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                VerticalContentAlignment="Center"
                                Background="#FFCDD7E8"
                                BorderBrush="{x:Null}"
                                Content="Record History?"
                                FontSize="12"
                                Foreground="#FF687A99"
                                IsChecked="True" />

                            <StackPanel Grid.Column="1" Orientation="Vertical">
                                <TextBlock
                                    Height="15"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    FontSize="12"
                                    Text="Records Limit:" />
                                <!--  PreviewTextInput="PointTextHeightTextBox_PreviewTextInput"  -->
                                <TextBox
                                    x:Name="txtRecordHistoryLimit"
                                    Width="70"
                                    Margin="0,4,0,0"
                                    HorizontalAlignment="Center"
                                    FontSize="11"
                                    Foreground="#FF364BA4"
                                    Style="{StaticResource RoundedTextBox}" />
                            </StackPanel>
                        </Grid>
                    </GroupBox>
                    <!--#endregion-->

                    <!--#region Info-->
                    <StackPanel
                        Grid.Row="2"
                        Width="305"
                        Orientation="Horizontal">
                        <Viewbox
                            xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                            xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                            Width="19"
                            Height="19"
                            Margin="0,4,5,0"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Top"
                            Stretch="Uniform">
                            <Canvas Width="20" Height="20">
                                <Path Fill="#FF687A99">
                                    <Path.Data>
                                        <GeometryGroup FillRule="EvenOdd">
                                            <PathGeometry Figures="M10 1.875C8.39303 1.875 6.82214 2.35152 5.486 3.24431C4.14985 4.1371 3.10844 5.40605 2.49348 6.8907C1.87852 8.37535 1.71762 10.009 2.03112 11.5851C2.34463 13.1612 3.11846 14.6089 4.25476 15.7452C5.39106 16.8815 6.8388 17.6554 8.41489 17.9689C9.99099 18.2824 11.6247 18.1215 13.1093 17.5065C14.594 16.8916 15.8629 15.8502 16.7557 14.514C17.6485 13.1779 18.125 11.607 18.125 10C18.1227 7.84581 17.266 5.78051 15.7427 4.25727C14.2195 2.73403 12.1542 1.87727 10 1.875ZM10 16.875C8.64026 16.875 7.31105 16.4718 6.18046 15.7164C5.04987 14.9609 4.16868 13.8872 3.64833 12.6309C3.12798 11.3747 2.99183 9.99237 3.2571 8.65875C3.52238 7.32513 4.17716 6.10013 5.13864 5.13864C6.10013 4.17715 7.32514 3.52237 8.65876 3.2571C9.99238 2.99183 11.3747 3.12798 12.631 3.64833C13.8872 4.16868 14.9609 5.04987 15.7164 6.18045C16.4718 7.31104 16.875 8.64025 16.875 10C16.8729 11.8227 16.1479 13.5702 14.8591 14.8591C13.5702 16.1479 11.8227 16.8729 10 16.875ZM11.25 13.75C11.25 13.9158 11.1842 14.0747 11.0669 14.1919C10.9497 14.3092 10.7908 14.375 10.625 14.375C10.2935 14.375 9.97554 14.2433 9.74112 14.0089C9.5067 13.7745 9.375 13.4565 9.375 13.125V10C9.20924 10 9.05027 9.93415 8.93306 9.81694C8.81585 9.69973 8.75 9.54076 8.75 9.375C8.75 9.20924 8.81585 9.05027 8.93306 8.93306C9.05027 8.81585 9.20924 8.75 9.375 8.75C9.70652 8.75 10.0245 8.8817 10.2589 9.11612C10.4933 9.35054 10.625 9.66848 10.625 10V13.125C10.7908 13.125 10.9497 13.1908 11.0669 13.3081C11.1842 13.4253 11.25 13.5842 11.25 13.75ZM8.75 6.5625C8.75 6.37708 8.80499 6.19582 8.908 6.04165C9.01101 5.88748 9.15743 5.76732 9.32874 5.69636C9.50004 5.62541 9.68854 5.60684 9.8704 5.64301C10.0523 5.67919 10.2193 5.76848 10.3504 5.89959C10.4815 6.0307 10.5708 6.19775 10.607 6.3796C10.6432 6.56146 10.6246 6.74996 10.5536 6.92127C10.4827 7.09257 10.3625 7.23899 10.2084 7.342C10.0542 7.44502 9.87292 7.5 9.6875 7.5C9.43886 7.5 9.20041 7.40123 9.02459 7.22541C8.84878 7.0496 8.75 6.81114 8.75 6.5625Z" />
                                        </GeometryGroup>
                                    </Path.Data>
                                </Path>
                            </Canvas>
                        </Viewbox>
                        <TextBlock
                            Width="276"
                            HorizontalAlignment="Left"
                            VerticalAlignment="top"
                            FontFamily="{StaticResource PoppinsFont}"
                            FontSize="12"
                            Foreground="#FF687A99"
                            TextWrapping="Wrap">

                            <Run Text="Unchecking " />
                            <Run FontWeight="Bold" Text="'Record History'" />
                            <Run Text="will stop saving future operations, but previous actions will remain stored." />


                        </TextBlock>

                    </StackPanel>
                    <!--#endregion-->

                </Grid>
            </TabItem>
            <!--#endregion-->

        </TabControl>
        <!--#endregion-->


        <!--#region Footer Buttons-->
        <Grid
            Grid.Row="3"
            Width="330"
            Margin="0,5,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <!--  Restore All Defaults Button  -->
            <Button
                x:Name="RestoreDefaultsButton"
                Grid.Column="0"
                Width="100"
                Height="32"
                Click="RestoreDefaultsButton_Click"
                Content="Restore Defaults"
                Foreground="#FFFBB0CB"
                Style="{StaticResource RoundedButton}" />

            <!--  Undo Settings Changes Button  -->
            <Button
                x:Name="UndoSettingsChangesButton"
                Grid.Column="1"
                Width="95"
                Height="32"
                Click="UndoSettingsChangesButton_Click"
                Content="Undo Changes"
                Style="{StaticResource RoundedButton}"
                Visibility="Collapsed" />

            <!--  Save Settings Button  -->
            <Button
                x:Name="SaveSettingsButton"
                Grid.Column="2"
                Height="32"
                Click="SaveSettingsButton_Click"
                IsEnabled="False"
                Style="{StaticResource RoundedButton}">
                <StackPanel
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Orientation="Horizontal">
                    <Viewbox
                        Width="16"
                        Height="16"
                        Margin="0,0,5,0"
                        VerticalAlignment="Center">
                        <Path Fill="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Button}}">
                            <Path.Data>
                                <PathGeometry Figures="M7 22h2v2H7zm4 0h2v2h-2zm4 0h2v2h-2zm2-20H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14c1.1 0 2-.9 2-2V6zm2 16H5V4h11.17L19 6.83zm-7-7c-1.66 0-3 1.34-3 3s1.34 3 3 3s3-1.34 3-3s-1.34-3-3-3M6 5h9v4H6z" />
                            </Path.Data>
                        </Path>
                    </Viewbox>
                    <TextBlock VerticalAlignment="Center" Text="Save Changes" />
                </StackPanel>
            </Button>
        </Grid>
        <!--#endregion-->

    </Grid>
</UserControl>