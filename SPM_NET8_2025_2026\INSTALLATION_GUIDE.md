# Survey Points Manager - Installation Guide

## Quick Installation

### Prerequisites
- **AutoCAD 2025 or AutoCAD 2026** (required)
- **Windows 10/11** (64-bit)
- **.NET 8 Runtime** (included with AutoCAD 2025/2026)
- **Administrator privileges** (for installation)

### Installation Steps

#### Method 1: NETLOAD Command (Recommended)
1. **Download the Plugin**
   - Extract all files to a folder (e.g., `C:\SPM_Plugin\`)
   - Ensure all files are in the same directory

2. **Load in AutoCAD**
   - Open AutoCAD 2025 or 2026
   - Type `NETLOAD` in the command line
   - Browse to the plugin folder
   - Select `SPM_NET8_2025_2026.dll`
   - Click "Load"

3. **Verify Installation**
   - The SPM palette should appear automatically
   - If not visible, type `SPM` to show the palette
   - Check that all tabs are accessible

#### Method 2: Startup Suite (Auto-Load)
1. **Access Startup Suite**
   - In AutoCAD, type `APPLOAD`
   - Click "Startup Suite" button

2. **Add Plugin**
   - Click "Add" in the Startup Suite dialog
   - Browse to `SPM_NET8_2025_2026.dll`
   - Select the file and click "Add"

3. **Configure Auto-Loading**
   - Ensure the plugin is checked in the list
   - Click "Close" to save settings
   - Plugin will load automatically on AutoCAD startup

#### Method 3: Registry Installation (Advanced)
1. **Registry Entry**
   - Open Registry Editor (regedit.exe)
   - Navigate to: `HKEY_CURRENT_USER\Software\Autodesk\AutoCAD\R25.0\ACAD-xxxx\Applications`
   - Create new key: `SPM_NET8_2025_2026`

2. **Set Values**
   - Create string value: `LOADCTRLS` = `14`
   - Create string value: `LOADER` = `[path to SPM_NET8_2025_2026.dll]`
   - Create string value: `MANAGED` = `1`

## File Structure

### Required Files
```
SPM_Plugin/
├── SPM_NET8_2025_2026.dll          # Main plugin assembly
├── Newtonsoft.Json.dll              # JSON processing library
├── SPM_NET8_2025_2026.dll.config   # Configuration file
└── Resources/                       # Plugin resources (if any)
```

### File Permissions
- Ensure all files have read permissions
- Plugin folder should be accessible to AutoCAD
- Avoid placing in system-protected directories

## Configuration

### Initial Setup
1. **First Launch**
   - Plugin loads automatically after installation
   - SPM palette appears on the right side
   - Default settings are applied

2. **Settings Configuration**
   - Go to Settings tab in SPM palette
   - Configure default paths for import/export
   - Set coordinate system preferences
   - Adjust performance settings if needed

### User Preferences
- **Default Directories**: Set preferred import/export folders
- **Coordinate Systems**: Configure default UTM zones
- **Display Options**: Adjust grid and interface settings
- **Performance**: Optimize for your system specifications

## Verification

### Test Installation
1. **Load Test**
   - Restart AutoCAD
   - Verify SPM palette loads automatically
   - Check all tabs are functional

2. **Basic Functionality**
   - Try importing a small CSV file
   - Verify points display in the grid
   - Test export functionality

3. **AutoCAD Integration**
   - Test point insertion into drawing
   - Verify OSNAP functionality
   - Check table generation features

### Troubleshooting Installation

#### Common Issues

**Plugin Won't Load**
- Check AutoCAD version (must be 2025 or 2026)
- Verify .NET 8 runtime is installed
- Ensure all plugin files are present
- Check file permissions

**Missing Dependencies**
- Verify Newtonsoft.Json.dll is present
- Check for blocked files (right-click → Properties → Unblock)
- Ensure all files are in the same directory

**Palette Not Visible**
- Type `SPM` command to show palette
- Check if palette is docked or floating
- Reset AutoCAD workspace if necessary

**Performance Issues**
- Close unnecessary applications
- Increase available system memory
- Check AutoCAD graphics settings

## Uninstallation

### Remove Plugin
1. **From Startup Suite**
   - Type `APPLOAD` in AutoCAD
   - Click "Startup Suite"
   - Select SPM plugin and click "Remove"

2. **Manual Removal**
   - Close AutoCAD completely
   - Delete plugin files from installation folder
   - Remove registry entries (if used Method 3)

3. **Clean Uninstall**
   - Clear AutoCAD user settings (optional)
   - Remove any custom configuration files
   - Restart AutoCAD to verify removal

## Network Installation

### Shared Network Drive
1. **Setup Network Location**
   - Place plugin files on accessible network drive
   - Ensure all users have read permissions
   - Test network connectivity and speed

2. **Client Configuration**
   - Each client loads plugin from network location
   - Use UNC paths for reliability
   - Consider local caching for performance

### Deployment Script
```batch
@echo off
echo Installing SPM Plugin...
copy "\\server\share\SPM_Plugin\*.*" "C:\SPM_Plugin\"
echo Plugin files copied.
echo.
echo To complete installation:
echo 1. Open AutoCAD 2025/2026
echo 2. Type NETLOAD
echo 3. Select C:\SPM_Plugin\SPM_NET8_2025_2026.dll
echo 4. Click Load
pause
```

## Updates and Maintenance

### Updating the Plugin
1. **Backup Current Version**
   - Save current plugin files
   - Export user settings if needed

2. **Install New Version**
   - Close AutoCAD completely
   - Replace plugin files with new version
   - Restart AutoCAD

3. **Verify Update**
   - Check version information in About dialog
   - Test critical functionality
   - Restore user settings if needed

### Maintenance Tasks
- **Regular Cleanup**: Clear temporary files and history
- **Settings Backup**: Export settings before major updates
- **Performance Monitoring**: Monitor memory usage with large datasets
- **License Validation**: Ensure license remains valid

## Support and Resources

### Getting Help
- **User Guide**: Comprehensive usage instructions
- **README**: Technical specifications and overview
- **Error Codes**: Reference for troubleshooting
- **Technical Support**: Contact information for assistance

### System Information
When contacting support, provide:
- AutoCAD version and build number
- Windows version and architecture
- Plugin version information
- Error messages or symptoms
- System specifications (RAM, CPU, etc.)

---

**Installation Complete!**

Your Survey Points Manager plugin is now ready to use. Refer to the USER_GUIDE.md for detailed usage instructions and the README.md for technical information.

For technical support or questions, please contact the support team with your system information and any error messages.
