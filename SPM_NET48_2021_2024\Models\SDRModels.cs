﻿using System.Collections.ObjectModel;

namespace SPM_NET48_2021_2024.Models
{
    public class SDRFileInfo
    {
        public string JobName { get; set; }
        public string InstrumentModel { get; set; }
        public double? TargetHeight { get; set; }
        public double? Scale { get; set; }
    }

    public class SDRFileData
    {
        public ObservableCollection<SurveyPoint> SurveyPoints { get; set; } = new ObservableCollection<SurveyPoint>();
        public SDRFileInfo FileInfo { get; set; } = new SDRFileInfo();
    }
}
