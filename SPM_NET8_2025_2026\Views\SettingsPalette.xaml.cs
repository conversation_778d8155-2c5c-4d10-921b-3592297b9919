﻿using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using SPM_NET8_2025_2026.Constants;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;

namespace SPM_NET8_2025_2026.Views
{
    public partial class SettingsPalette : System.Windows.Controls.UserControl
    {
        #region Snapshot fields: last-saved state from persisted properties
        private string _originalPointLayer;
        private int _originalPointLayerColor;
        private string _originalPointNumberLayer;
        private int _originalPointNumberLayerColor;
        private string _originalTableLayer;
        private int _originalTableLayerColor;
        private int _originalPointDisplayImage;
        private double _originalPointSize;
        private double _originalPointNumberTextHeight;
        private string _originalTableStyle;
        private int _originalMaxRowPerTable;
        private bool _originalShowTableTitle;
        private string _originalTableTitle;
        private bool _originalShowPointNumber;
        private bool _originalShowElevation;
        private bool _originalShowDescription;
        private bool _originalRecordHistory;
        private int _originalRecordHistoryLimit;
        #endregion

        private bool _settingsChanged = false;
        private int _currentPointDisplayImage;

        // Track if we're currently updating checkboxes to avoid circular updates
        private bool isUpdating = false;

        public SettingsPalette()
        {
            InitializeComponent();

            SaveSettingsButton.IsEnabled = false;
            UndoSettingsChangesButton.Visibility = System.Windows.Visibility.Collapsed;

            _currentPointDisplayImage = Properties.Settings.Default.PointDisplayImage;

            LoadInitialSettings();
            LoadInitialColors();

            AttachChangeHandlers();
            StoreOriginalValues();

            SubscribeToDoc(ActiveDocument);


            chkShowPointNumber.Checked += Child_Click;
            chkShowPointNumber.Unchecked += Child_Click;
            chkShowElevation.Checked += Child_Click;
            chkShowElevation.Unchecked += Child_Click;
            chkShowDescription.Checked += Child_Click;
            chkShowDescription.Unchecked += Child_Click;

            UpdateSelectAllState();

        }

        #region AutoCAD Properties
        /// <summary>
        /// Gets the currently active AutoCAD document.
        /// </summary>
        private Document ActiveDocument => Autodesk.AutoCAD.ApplicationServices.Application.DocumentManager.MdiActiveDocument;

        /// <summary>
        /// Gets the database of the currently active AutoCAD document.
        /// </summary>
        private Database ActiveDatabase => ActiveDocument?.Database;

        /// <summary>
        /// Gets the editor of the currently active AutoCAD document.
        /// </summary>
        private Editor ActiveEditor => ActiveDocument?.Editor;
        #endregion

        private void LoadInitialSettings()
        {
            txtPointLayer.Text = Properties.Settings.Default.PointLayer;
            txtPointNumberLayer.Text = Properties.Settings.Default.PointNumberLayer;
            txtTableLayer.Text = Properties.Settings.Default.TableLayer;
            txtPointSize.Text = Properties.Settings.Default.PointSize.ToString();
            txtPointNumberTextHeight.Text = Properties.Settings.Default.PointNumberTextHeight.ToString();
            txtTableStyle.Text = Properties.Settings.Default.TableStyleName;
            txtMaxRowsPerTable.Text = Properties.Settings.Default.MaxRowPerTable.ToString();
            txtTableTitle.Text = Properties.Settings.Default.TableTitle;
            txtRecordHistoryLimit.Text = Properties.Settings.Default.RecordHistoryLimit.ToString();

            // Notice: Rectangles Colors are handled in LoadInitialColors()

            // Set Point Display Image
            SetPointDisplayImage(Properties.Settings.Default.PointDisplayImage);

            // Point Size Mode is computed
            txbPointSizeMode.Text = GetPointSizeMode();

            chkShowTableTitle.IsChecked = Properties.Settings.Default.ShowTableTitle;
            chkShowPointNumber.IsChecked = Properties.Settings.Default.ShowPointNumber;
            chkShowElevation.IsChecked = Properties.Settings.Default.ShowElevation;
            chkShowDescription.IsChecked = Properties.Settings.Default.ShowDescription;
            chkRecordHistory.IsChecked = Properties.Settings.Default.RecordHistory;
        }

        private void LoadInitialColors()
        {
            try
            {

                short pointColorIndex = Properties.Settings.Default.PointLayerColor;

                Autodesk.AutoCAD.Colors.Color displayColor;

                // point color
                if (pointColorIndex == 0)
                {
                    displayColor = Autodesk.AutoCAD.Colors.Color.FromRgb(255, 255, 255);
                }
                else if (pointColorIndex == 256)
                {
                    displayColor = Autodesk.AutoCAD.Colors.Color.FromRgb(200, 200, 200);
                }
                else
                {
                    displayColor = Autodesk.AutoCAD.Colors.Color.FromColorIndex(Autodesk.AutoCAD.Colors.ColorMethod.ByAci, pointColorIndex);
                }

                System.Drawing.Color pointColor = displayColor.ColorValue;

                rectPointLayerColor.Fill = new SolidColorBrush(System.Windows.Media.Color.FromArgb(pointColor.A, pointColor.R, pointColor.G, pointColor.B));


                short pointNumberColorIndex = Properties.Settings.Default.PointNumberLayerColor;

                // // point number color
                if (pointNumberColorIndex == 0)
                {
                    displayColor = Autodesk.AutoCAD.Colors.Color.FromRgb(255, 255, 255);
                }
                else if (pointNumberColorIndex == 256)
                {
                    displayColor = Autodesk.AutoCAD.Colors.Color.FromRgb(200, 200, 200);
                }
                else
                {
                    displayColor = Autodesk.AutoCAD.Colors.Color.FromColorIndex(Autodesk.AutoCAD.Colors.ColorMethod.ByAci, pointNumberColorIndex);
                }
                System.Drawing.Color pointNumberColor = displayColor.ColorValue;
                rectPointNumberLayerColor.Fill = new SolidColorBrush(System.Windows.Media.Color.FromArgb(pointNumberColor.A, pointNumberColor.R, pointNumberColor.G, pointNumberColor.B));


                short tableColorIndex = Properties.Settings.Default.TableLayerColor;

                // table color
                if (tableColorIndex == 0)
                {
                    displayColor = Autodesk.AutoCAD.Colors.Color.FromRgb(255, 255, 255);
                }
                else if (tableColorIndex == 256)
                {
                    displayColor = Autodesk.AutoCAD.Colors.Color.FromRgb(200, 200, 200);
                }
                else
                {
                    displayColor = Autodesk.AutoCAD.Colors.Color.FromColorIndex(Autodesk.AutoCAD.Colors.ColorMethod.ByAci, tableColorIndex);
                }
                System.Drawing.Color tableColor = displayColor.ColorValue;
                rectTableLayerColor.Fill = new SolidColorBrush(System.Windows.Media.Color.FromArgb(tableColor.A, tableColor.R, tableColor.G, tableColor.B));
            }
            catch (Exception)
            {
                RestoreDefaultColors();
            }
        }

        private void RestoreDefaultColors()
        {
            Properties.Settings.Default.PointLayerColor = PluginDefaults.DefaultPointLayerColor;
            Properties.Settings.Default.PointNumberLayerColor = PluginDefaults.DefaultPointNumberLayerColor;


            // point color
            var defaultPointColor = Autodesk.AutoCAD.Colors.Color.FromColorIndex(Autodesk.AutoCAD.Colors.ColorMethod.ByAci, PluginDefaults.DefaultPointLayerColor);

            System.Drawing.Color pointColor = defaultPointColor.ColorValue;
            rectPointLayerColor.Fill = new SolidColorBrush(System.Windows.Media.Color.FromArgb(pointColor.A, pointColor.R, pointColor.G, pointColor.B));


            // point number color
            var defaultPointNumberColor = Autodesk.AutoCAD.Colors.Color.FromColorIndex(Autodesk.AutoCAD.Colors.ColorMethod.ByAci, PluginDefaults.DefaultPointNumberLayerColor);

            System.Drawing.Color pointNumberColor = defaultPointNumberColor.ColorValue;
            rectPointNumberLayerColor.Fill = new SolidColorBrush(System.Windows.Media.Color.FromArgb(pointNumberColor.A, pointNumberColor.R, pointNumberColor.G, pointNumberColor.B));

            // table color
            var defaultTableColor = Autodesk.AutoCAD.Colors.Color.FromColorIndex(Autodesk.AutoCAD.Colors.ColorMethod.ByAci, PluginDefaults.DefaultTableLayerColor);
            System.Drawing.Color tableColor = defaultTableColor.ColorValue;
            rectTableLayerColor.Fill = new SolidColorBrush(System.Windows.Media.Color.FromArgb(tableColor.A, tableColor.R, tableColor.G, tableColor.B));
        }

        private void SetPointDisplayImage(int pointMode)
        {
            if (IsInitialized && pointMode != _currentPointDisplayImage)
            {
                SettingsChanged(this, EventArgs.Empty);
            }

            //MessageBox.Show($" {pointMode} , {_currentPointDisplayImage}");

            // Hide all
            Index0.Visibility = System.Windows.Visibility.Collapsed;
            Index1.Visibility = System.Windows.Visibility.Collapsed;
            Index2.Visibility = System.Windows.Visibility.Collapsed;
            Index3.Visibility = System.Windows.Visibility.Collapsed;
            Index4.Visibility = System.Windows.Visibility.Collapsed;
            Index32.Visibility = System.Windows.Visibility.Collapsed;
            Index33.Visibility = System.Windows.Visibility.Collapsed;
            Index34.Visibility = System.Windows.Visibility.Collapsed;
            Index35.Visibility = System.Windows.Visibility.Collapsed;
            Index36.Visibility = System.Windows.Visibility.Collapsed;
            Index64.Visibility = System.Windows.Visibility.Collapsed;
            Index65.Visibility = System.Windows.Visibility.Collapsed;
            Index66.Visibility = System.Windows.Visibility.Collapsed;
            Index67.Visibility = System.Windows.Visibility.Collapsed;
            Index68.Visibility = System.Windows.Visibility.Collapsed;
            Index96.Visibility = System.Windows.Visibility.Collapsed;
            Index97.Visibility = System.Windows.Visibility.Collapsed;
            Index98.Visibility = System.Windows.Visibility.Collapsed;
            Index99.Visibility = System.Windows.Visibility.Collapsed;
            Index100.Visibility = System.Windows.Visibility.Collapsed;

            // Show selected
            switch (pointMode)
            {
                case 0: Index0.Visibility = System.Windows.Visibility.Visible; break;
                case 1: Index1.Visibility = System.Windows.Visibility.Visible; break;
                case 2: Index2.Visibility = System.Windows.Visibility.Visible; break;
                case 3: Index3.Visibility = System.Windows.Visibility.Visible; break;
                case 4: Index4.Visibility = System.Windows.Visibility.Visible; break;
                case 32: Index32.Visibility = System.Windows.Visibility.Visible; break;
                case 33: Index33.Visibility = System.Windows.Visibility.Visible; break;
                case 34: Index34.Visibility = System.Windows.Visibility.Visible; break;
                case 35: Index35.Visibility = System.Windows.Visibility.Visible; break;
                case 36: Index36.Visibility = System.Windows.Visibility.Visible; break;
                case 64: Index64.Visibility = System.Windows.Visibility.Visible; break;
                case 65: Index65.Visibility = System.Windows.Visibility.Visible; break;
                case 66: Index66.Visibility = System.Windows.Visibility.Visible; break;
                case 67: Index67.Visibility = System.Windows.Visibility.Visible; break;
                case 68: Index68.Visibility = System.Windows.Visibility.Visible; break;
                case 96: Index96.Visibility = System.Windows.Visibility.Visible; break;
                case 97: Index97.Visibility = System.Windows.Visibility.Visible; break;
                case 98: Index98.Visibility = System.Windows.Visibility.Visible; break;
                case 99: Index99.Visibility = System.Windows.Visibility.Visible; break;
                case 100: Index100.Visibility = System.Windows.Visibility.Visible; break;
                default: Index0.Visibility = System.Windows.Visibility.Visible; break;
            }

            _currentPointDisplayImage = pointMode;
        }

        private string GetPointSizeMode()
        {
            return Properties.Settings.Default.PointSize <= 0 ? "Size Relative to Screen (%)" : "Absolute Unit (Unit)";
        }

        private void StoreOriginalValues()
        {
            _originalPointLayer = Properties.Settings.Default.PointLayer;
            _originalPointLayerColor = Properties.Settings.Default.PointLayerColor;
            _originalPointNumberLayer = Properties.Settings.Default.PointNumberLayer;
            _originalPointNumberLayerColor = Properties.Settings.Default.PointNumberLayerColor;
            _originalTableLayer = Properties.Settings.Default.TableLayer;
            _originalTableLayerColor = Properties.Settings.Default.TableLayerColor;
            _originalPointDisplayImage = Properties.Settings.Default.PointDisplayImage;
            _originalPointSize = Properties.Settings.Default.PointSize;
            _originalPointNumberTextHeight = Properties.Settings.Default.PointNumberTextHeight;
            _originalTableStyle = Properties.Settings.Default.TableStyleName;
            _originalMaxRowPerTable = Properties.Settings.Default.MaxRowPerTable;
            _originalTableTitle = Properties.Settings.Default.TableTitle;
            _originalRecordHistoryLimit = Properties.Settings.Default.RecordHistoryLimit;
            _originalShowTableTitle = Properties.Settings.Default.ShowTableTitle;
            _originalShowPointNumber = Properties.Settings.Default.ShowPointNumber;
            _originalShowElevation = Properties.Settings.Default.ShowElevation;
            _originalShowDescription = Properties.Settings.Default.ShowDescription;
            _originalRecordHistory = Properties.Settings.Default.RecordHistory;
        }

        private void SaveSettingsButton_Click(object sender, System.Windows.RoutedEventArgs e)
        {
            try
            {
                // Save text-based settings from UI to Properties.Settings.Default
                Properties.Settings.Default.PointLayer = txtPointLayer.Text;
                Properties.Settings.Default.PointNumberLayer = txtPointNumberLayer.Text;
                Properties.Settings.Default.TableLayer = txtTableLayer.Text;
                Properties.Settings.Default.TableStyleName = txtTableStyle.Text;
                Properties.Settings.Default.TableTitle = txtTableTitle.Text;

                Properties.Settings.Default.PointDisplayImage = _currentPointDisplayImage;

                // Parse and save numeric settings with validation
                if (double.TryParse(txtPointSize.Text, out double pointSize))
                {
                    Properties.Settings.Default.PointSize = pointSize;
                }
                else
                {
                    throw new FormatException("Point Size must be a valid number.");
                }

                if (double.TryParse(txtPointNumberTextHeight.Text, out double pointNumberTextHeight))
                {
                    Properties.Settings.Default.PointNumberTextHeight = pointNumberTextHeight;
                }
                else
                {
                    throw new FormatException("Point Number Text Height must be a valid number.");
                }

                if (int.TryParse(txtMaxRowsPerTable.Text, out int maxRowsPerTable))
                {
                    Properties.Settings.Default.MaxRowPerTable = maxRowsPerTable;
                }
                else
                {
                    throw new FormatException("Max Rows Per Table must be a valid integer.");
                }

                if (int.TryParse(txtRecordHistoryLimit.Text, out int recordHistoryLimit))
                {
                    Properties.Settings.Default.RecordHistoryLimit = recordHistoryLimit;
                }
                else
                {
                    throw new FormatException("Record History Limit must be a valid integer.");
                }

                // Save checkbox states from UI to Properties.Settings.Default
                Properties.Settings.Default.ShowTableTitle = chkShowTableTitle.IsChecked ?? false;
                Properties.Settings.Default.ShowPointNumber = chkShowPointNumber.IsChecked ?? false;
                Properties.Settings.Default.ShowElevation = chkShowElevation.IsChecked ?? false;
                Properties.Settings.Default.ShowDescription = chkShowDescription.IsChecked ?? false;
                Properties.Settings.Default.RecordHistory = chkRecordHistory.IsChecked ?? false;

                // Persist all settings to the configuration file
                Properties.Settings.Default.Save();

                // Update snapshot fields to reflect the newly saved settings
                StoreOriginalValues();

                // Reset UI state to indicate no unsaved changes
                _settingsChanged = false;
                SaveSettingsButton.IsEnabled = false;
                UndoSettingsChangesButton.Visibility = System.Windows.Visibility.Collapsed;

                // Show success message to the user
                System.Windows.MessageBox.Show("Settings saved successfully.", "Settings",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                // Display error message if something goes wrong
                System.Windows.MessageBox.Show("Error saving settings: " + ex.Message, "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        #region Change Handlers

        // Method to attach event handlers to controls
        private void AttachChangeHandlers()
        {
            // Attach directly to TextChanged for TextBoxes
            txtPointLayer.TextChanged += SettingsChanged;
            txtPointNumberLayer.TextChanged += SettingsChanged;
            txtTableLayer.TextChanged += SettingsChanged;
            txtPointSize.TextChanged += SettingsChanged;
            txtPointNumberTextHeight.TextChanged += SettingsChanged;
            txtTableStyle.TextChanged += SettingsChanged;
            txtMaxRowsPerTable.TextChanged += SettingsChanged;
            txtTableTitle.TextChanged += SettingsChanged;
            txtRecordHistoryLimit.TextChanged += SettingsChanged;

            // Attach directly to Checked and Unchecked for CheckBoxes
            chkShowTableTitle.Checked += SettingsChanged;
            chkShowTableTitle.Unchecked += SettingsChanged;
            chkShowPointNumber.Checked += SettingsChanged;
            chkShowPointNumber.Unchecked += SettingsChanged;
            chkShowElevation.Checked += SettingsChanged;
            chkShowElevation.Unchecked += SettingsChanged;
            chkShowDescription.Checked += SettingsChanged;
            chkShowDescription.Unchecked += SettingsChanged;
            chkRecordHistory.Checked += SettingsChanged;
            chkRecordHistory.Unchecked += SettingsChanged;
        }

        // Method to detach event handlers from controls
        private void DetachChangeHandlers()
        {
            // Detach from TextChanged for TextBoxes
            txtPointLayer.TextChanged -= SettingsChanged;
            txtPointNumberLayer.TextChanged -= SettingsChanged;
            txtTableLayer.TextChanged -= SettingsChanged;
            txtPointSize.TextChanged -= SettingsChanged;
            txtPointNumberTextHeight.TextChanged -= SettingsChanged;
            txtTableStyle.TextChanged -= SettingsChanged;
            txtMaxRowsPerTable.TextChanged -= SettingsChanged;
            txtTableTitle.TextChanged -= SettingsChanged;
            txtRecordHistoryLimit.TextChanged -= SettingsChanged;

            // Detach from Checked and Unchecked for CheckBoxes
            chkShowTableTitle.Checked -= SettingsChanged;
            chkShowTableTitle.Unchecked -= SettingsChanged;
            chkShowPointNumber.Checked -= SettingsChanged;
            chkShowPointNumber.Unchecked -= SettingsChanged;
            chkShowElevation.Checked -= SettingsChanged;
            chkShowElevation.Unchecked -= SettingsChanged;
            chkShowDescription.Checked -= SettingsChanged;
            chkShowDescription.Unchecked -= SettingsChanged;
            chkRecordHistory.Checked -= SettingsChanged;
            chkRecordHistory.Unchecked -= SettingsChanged;
        }

        // Core method to handle settings changes
        private void SettingsChanged(object sender, EventArgs e)
        {
            if (!IsInitialized)
            {
                return;
            }
            _settingsChanged = true;
            SaveSettingsButton.IsEnabled = true;
            UndoSettingsChangesButton.Visibility = System.Windows.Visibility.Visible;
        }

        #endregion


        #region Color Selection
        private void ChangeColorSetting(object sender, MouseButtonEventArgs e)
        {
            try
            {
                // Validate the sender as a Rectangle
                System.Windows.Shapes.Rectangle swatch = sender as System.Windows.Shapes.Rectangle;
                if (swatch == null)
                {
                    return;
                }

                // Named constants for AutoCAD color indices
                const short ByBlockColorIndex = 0;
                const short ByLayerColorIndex = 256;
                const short DefaultFallbackColorIndex = 7;

                // Colors for display
                var byBlockDisplayColor = Autodesk.AutoCAD.Colors.Color.FromRgb(255, 255, 255); // White for ByBlock
                var byLayerDisplayColor = Autodesk.AutoCAD.Colors.Color.FromRgb(200, 200, 200); // Grey for ByLayer

                // Determine which setting to update based on the swatch
                string settingName = null;
                short currentColorIndex = 0;
                if (swatch == rectPointLayerColor)
                {
                    settingName = "PointLayerColor";
                    currentColorIndex = Properties.Settings.Default.PointLayerColor;
                }
                else if (swatch == rectPointNumberLayerColor)
                {
                    settingName = "PointNumberLayerColor";
                    currentColorIndex = Properties.Settings.Default.PointNumberLayerColor;
                }
                else if (swatch == rectTableLayerColor)
                {
                    settingName = "TableLayerColor";
                    currentColorIndex = Properties.Settings.Default.TableLayerColor;
                }
                else
                {
                    throw new InvalidOperationException("Unknown color swatch.");
                }

                // Show AutoCAD color dialog
                var colorDialog = new Autodesk.AutoCAD.Windows.ColorDialog();
                colorDialog.Color = Autodesk.AutoCAD.Colors.Color.FromColorIndex(Autodesk.AutoCAD.Colors.ColorMethod.ByAci, currentColorIndex);

                if (colorDialog.ShowDialog() != System.Windows.Forms.DialogResult.OK)
                {
                    return;
                }

                // Process the selected color
                var selectedColor = colorDialog.Color;
                short newColorIndex;
                if (selectedColor.ColorMethod == Autodesk.AutoCAD.Colors.ColorMethod.ByColor ||
                    selectedColor.ColorMethod == Autodesk.AutoCAD.Colors.ColorMethod.ByAci)
                {
                    newColorIndex = selectedColor.ColorIndex;
                }
                else if (selectedColor.ColorMethod == Autodesk.AutoCAD.Colors.ColorMethod.ByBlock)
                {
                    newColorIndex = ByBlockColorIndex;
                }
                else if (selectedColor.ColorMethod == Autodesk.AutoCAD.Colors.ColorMethod.ByLayer)
                {
                    newColorIndex = ByLayerColorIndex;
                }
                else
                {
                    newColorIndex = DefaultFallbackColorIndex;
                }

                // Determine the display color for the swatch
                Autodesk.AutoCAD.Colors.Color displayColor;
                if (newColorIndex == ByBlockColorIndex)
                {
                    displayColor = byBlockDisplayColor;
                }
                else if (newColorIndex == ByLayerColorIndex)
                {
                    displayColor = byLayerDisplayColor;
                }
                else
                {
                    displayColor = Autodesk.AutoCAD.Colors.Color.FromColorIndex(Autodesk.AutoCAD.Colors.ColorMethod.ByAci, newColorIndex);
                }

                // Update the swatch's appearance
                var systemColor = displayColor.ColorValue;
                swatch.Fill = new SolidColorBrush(System.Windows.Media.Color.FromArgb(
                    systemColor.A, systemColor.R, systemColor.G, systemColor.B));

                // Update the corresponding setting
                if (settingName == "PointLayerColor")
                {
                    Properties.Settings.Default.PointLayerColor = newColorIndex;
                }
                else if (settingName == "PointNumberLayerColor")
                {
                    Properties.Settings.Default.PointNumberLayerColor = newColorIndex;
                }
                else if (settingName == "TableLayerColor")
                {
                    Properties.Settings.Default.TableLayerColor = newColorIndex;
                }

                // Mark settings as changed
                //btnResetLayerDefaults.IsEnabled = true;
                SettingsChanged(sender, e);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show(
                    $"Error selecting color: {ex.Message}",
                    "Error",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
        }
        #endregion


        #region UNDO
        private void UndoSettingsChangesButton_Click(object sender, RoutedEventArgs e)
        {
            // Temporarily suppress change events
            DetachChangeHandlers();

            // Revert TextBoxes to snapshot values
            txtPointLayer.Text = _originalPointLayer;
            txtPointNumberLayer.Text = _originalPointNumberLayer;
            txtTableLayer.Text = _originalTableLayer;
            txtPointSize.Text = _originalPointSize.ToString();
            txtPointNumberTextHeight.Text = _originalPointNumberTextHeight.ToString();
            txtTableStyle.Text = _originalTableStyle;
            txtMaxRowsPerTable.Text = _originalMaxRowPerTable.ToString();
            txtTableTitle.Text = _originalTableTitle;
            txtRecordHistoryLimit.Text = _originalRecordHistoryLimit.ToString();

            // Revert CheckBoxes to snapshot values
            chkShowTableTitle.IsChecked = _originalShowTableTitle;
            chkShowPointNumber.IsChecked = _originalShowPointNumber;
            chkShowElevation.IsChecked = _originalShowElevation;
            chkShowDescription.IsChecked = _originalShowDescription;
            chkRecordHistory.IsChecked = _originalRecordHistory;

            // Revert Color Swatches to snapshot values
            SetColorSwatch(rectPointLayerColor, _originalPointLayerColor);
            SetColorSwatch(rectPointNumberLayerColor, _originalPointNumberLayerColor);
            SetColorSwatch(rectTableLayerColor, _originalTableLayerColor);

            SetPointDisplayImage(_originalPointDisplayImage);

            // Reset UI state: Disable Save button and hide Undo button
            _settingsChanged = false;
            SaveSettingsButton.IsEnabled = false;
            UndoSettingsChangesButton.Visibility = System.Windows.Visibility.Collapsed;

            // Reattach change handlers
            AttachChangeHandlers();
        }

        // Helper method to set the color swatch based on the color index
        private void SetColorSwatch(System.Windows.Shapes.Rectangle swatch, int colorIndex)
        {
            const int ByBlockColorIndex = 0;
            const int ByLayerColorIndex = 256;
            var byBlockDisplayColor = Autodesk.AutoCAD.Colors.Color.FromRgb(255, 255, 255); // White for ByBlock
            var byLayerDisplayColor = Autodesk.AutoCAD.Colors.Color.FromRgb(200, 200, 200); // Grey for ByLayer

            Autodesk.AutoCAD.Colors.Color displayColor;
            if (colorIndex == ByBlockColorIndex)
            {
                displayColor = byBlockDisplayColor;
            }
            else if (colorIndex == ByLayerColorIndex)
            {
                displayColor = byLayerDisplayColor;
            }
            else
            {
                displayColor = Autodesk.AutoCAD.Colors.Color.FromColorIndex(Autodesk.AutoCAD.Colors.ColorMethod.ByAci, (short)colorIndex);
            }

            var systemColor = displayColor.ColorValue;
            swatch.Fill = new SolidColorBrush(System.Windows.Media.Color.FromArgb(
                systemColor.A, systemColor.R, systemColor.G, systemColor.B));
        }
        #endregion

        private void RestoreDefaultsButton_Click(object sender, RoutedEventArgs e)
        {
            // Prompt user for confirmation
            var result = System.Windows.MessageBox.Show(
                "Are you sure you want to restore all defaults? Unsaved changes will be lost.",
                "Confirm Restore Defaults",
                System.Windows.MessageBoxButton.YesNo,
                System.Windows.MessageBoxImage.Warning);

            if (result != System.Windows.MessageBoxResult.Yes)
            {
                return;
            }

            // Temporarily suppress change events (assuming these methods exist)
            DetachChangeHandlers();

            // Reset TextBoxes to default values
            txtPointLayer.Text = PluginDefaults.DefaultPointLayer;
            txtPointNumberLayer.Text = PluginDefaults.DefaultPointNumberLayer;
            txtTableLayer.Text = PluginDefaults.DefaultTableLayer;
            txtPointSize.Text = PluginDefaults.DefaultPointSize.ToString();
            txtPointNumberTextHeight.Text = PluginDefaults.DefaultPointNumberTextHeight.ToString();
            txtTableStyle.Text = PluginDefaults.DefaultTableStyle;
            txtMaxRowsPerTable.Text = PluginDefaults.DefaultMaxRowPerTable.ToString();
            txtTableTitle.Text = PluginDefaults.DefaultTableTitle;
            txtRecordHistoryLimit.Text = PluginDefaults.DefaultRecordHistoryLimit.ToString();

            // Reset CheckBoxes to default values
            chkShowTableTitle.IsChecked = PluginDefaults.DefaultShowTableTitle;
            chkShowPointNumber.IsChecked = PluginDefaults.DefaultShowPointNumber;
            chkShowElevation.IsChecked = PluginDefaults.DefaultShowElevation;
            chkShowDescription.IsChecked = PluginDefaults.DefaultShowDescription;
            chkRecordHistory.IsChecked = PluginDefaults.DefaultRecordHistory;

            SetPointDisplayImage(PluginDefaults.DefaultPointDisplayImage);

            // Reset color swatches to default values
            SetColorSwatch(rectPointLayerColor, PluginDefaults.DefaultPointLayerColor);
            SetColorSwatch(rectPointNumberLayerColor, PluginDefaults.DefaultPointNumberLayerColor);
            SetColorSwatch(rectTableLayerColor, PluginDefaults.DefaultTableLayerColor);

            // Update UI state to indicate changes
            _settingsChanged = true;
            SaveSettingsButton.IsEnabled = true;
            UndoSettingsChangesButton.Visibility = System.Windows.Visibility.Visible;

            // Reattach change handlers
            AttachChangeHandlers();
        }

        private void borderPointDisplayImage_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            ActiveDocument.SendStringToExecute("_DDPTYPE ", true, false, true);
        }

        public void SubscribeToDoc(Document doc)
        {
            doc.CommandEnded += new CommandEventHandler(doc_CommandEnded);
        }

        private void doc_CommandEnded(object sender, CommandEventArgs e)
        {
            if (e.GlobalCommandName == "PTYPE")
            {
                double pdsize = ActiveDocument.Database.Pdsize;
                int pdmode = ActiveDocument.Database.Pdmode;

                // Update UI only
                txtPointSize.Text = pdsize.ToString();
                SetPointDisplayImage(pdmode);
                // Compute point size mode directly from pdsize
                txbPointSizeMode.Text = pdsize <= 0 ? "Size Relative to Screen (%)" : "Absolute Unit (Unit)";
            }
        }



        #region columns Checkboxes
        private void chkAllTableColumns_Click(object sender, RoutedEventArgs e)
        {
            bool newVal = chkAllTableColumns.IsChecked == true;

            isUpdating = true;
            chkShowPointNumber.IsChecked = newVal;
            chkShowElevation.IsChecked = newVal;
            chkShowDescription.IsChecked = newVal;
            isUpdating = false;

            UpdateSelectAllState();
        }

        private void Child_Click(object sender, RoutedEventArgs e)
        {
            if (isUpdating)
            {
                return;
            }

            UpdateSelectAllState();
        }

        private void UpdateSelectAllState()
        {
            if (isUpdating)
            {
                return;
            }

            bool allChecked = chkShowPointNumber.IsChecked == true
                           && chkShowElevation.IsChecked == true
                           && chkShowDescription.IsChecked == true;

            bool noneChecked = chkShowPointNumber.IsChecked == false
                            && chkShowElevation.IsChecked == false
                            && chkShowDescription.IsChecked == false;

            if (allChecked)
            {
                chkAllTableColumns.IsChecked = true;
            }
            else if (noneChecked)
            {
                chkAllTableColumns.IsChecked = false;
            }
            else
            {
                chkAllTableColumns.IsChecked = null;
            }
        }

        #endregion


    }


}

