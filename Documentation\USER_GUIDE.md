# Survey Points Manager - User Guide

*Professional AutoCAD Plugin for Survey Data Management*

## Table of Contents
1. [Getting Started](#getting-started)
2. [Interface Overview](#interface-overview)
3. [Importing Survey Data](#importing-survey-data)
4. [Managing Points](#managing-points)
5. [Exporting Data](#exporting-data)
6. [Drawing and Visualization](#drawing-and-visualization)
7. [Settings and Preferences](#settings-and-preferences)
8. [Project History](#project-history)
9. [Advanced Workflows](#advanced-workflows)
10. [Troubleshooting](#troubleshooting)
11. [Customer Support](#customer-support)

## Getting Started

Welcome to Survey Points Manager! This guide will help you get the most out of your professional survey data management plugin.

### What You Can Do with Survey Points Manager

As a surveyor or civil engineer, Survey Points Manager helps you:
- **Import survey data** from total stations, GPS units, and field data collectors
- **Convert between formats** (CSV, TXT, KML, SDR, IDX, GSI)
- **Manage large point datasets** efficiently within AutoCAD
- **Create professional drawings** with automated point placement and numbering
- **Export data** for use in other surveying and engineering software
- **Generate coordinate tables** for reports and documentation

### Installation and First Launch
1. Install the Survey Points Manager bundle (see Installation Guide)
2. The plugin **automatically detects your AutoCAD version** and loads the appropriate components
3. The SPM palette will automatically appear on the right side
4. If the palette is not visible, type `SPM` in the command line to show it

### Automatic Version Detection
Survey Points Manager automatically uses the best version for your AutoCAD:
- **AutoCAD 2025/2026**: Uses .NET 8 version for optimal performance
- **AutoCAD 2021-2024**: Uses .NET Framework 4.8 version
- **AutoCAD 2019-2020**: Uses .NET Framework 4.7 version
- **AutoCAD 2017-2018**: Uses .NET Framework 4.6 version
- **AutoCAD 2015-2016**: Uses .NET Framework 4.5 version

**No manual version selection needed** - the plugin handles everything automatically!

### Interface Layout
The SPM interface consists of five main tabs:
- **Home**: Overview and quick actions
- **Import**: Data import tools and settings
- **Export**: Data export tools and options
- **Settings**: Configuration and preferences
- **History**: Operation history and management

## Version Selection

### Choosing the Right Version

#### For New Users
- **Latest AutoCAD (2025/2026)**: Use **SPM_NET8_2025_2026** for best performance
- **Older AutoCAD**: Select the version matching your AutoCAD release
- **Performance Critical**: .NET 8 version offers 25% better performance

#### For Existing Users
- **Upgrading AutoCAD**: Consider upgrading to 2025/2026 + .NET 8 version
- **Staying Current**: Use version matching your current AutoCAD
- **Data Compatibility**: All versions use compatible data formats

### Version-Specific Features

#### SPM_NET8_2025_2026 (Recommended)
- ✅ **Enhanced Performance**: 25% faster processing
- ✅ **Better Memory Management**: 15% less memory usage
- ✅ **Modern Architecture**: Latest .NET 8 features
- ✅ **Active Development**: New features and updates
- ✅ **Windows Forms Compatibility**: Enhanced UI compatibility

#### Legacy Versions
- 🔧 **Maintenance Mode**: Bug fixes only
- 📦 **Stable**: Proven reliability for older AutoCAD versions
- 🔄 **Compatible**: Same data formats and workflows

## Interface Overview

### Home Tab
- **Points Summary**: Displays current point count and statistics
- **Quick Actions**: Common operations like Clear All, Undo/Redo
- **Recent Files**: Quick access to recently imported files
- **Status Information**: Current session status and notifications
- **Version Information**: Shows current SPM version

### Navigation
- Use tabs at the top to switch between different functions
- Each tab contains relevant tools and settings
- Context-sensitive help is available throughout the interface
- Keyboard shortcuts available for common operations

## Importing Data

### Supported File Formats

All SPM versions support the same file formats:

#### CSV Files
- **Standard CSV**: Comma-separated values with customizable delimiters
- **Format Detection**: Automatic detection of coordinate order and structure
- **Column Mapping**: Map columns to Point Number, Easting, Northing, Elevation, Description
- **Header Support**: Files with or without header rows

#### TXT Files
- **Space-Delimited**: Space or tab-separated coordinate files
- **Fixed-Width**: Fixed-width column formats
- **Custom Delimiters**: Support for various delimiter types

#### KML Files
- **Google Earth**: Standard KML format with coordinate extraction
- **UTM Conversion**: Automatic conversion from geographic to UTM coordinates
- **Zone Selection**: Manual UTM zone and hemisphere selection

#### Survey Data Formats
- **SDR**: Sokkia data recorder format
- **IDX**: Survey index files
- **GSI**: Leica GSI format (8-bit and 16-bit variants)

### Import Process

#### Step 1: Select File
1. Click "Browse" in the Import tab
2. Navigate to your data file
3. Select the file and click "Open"
4. The system will attempt automatic format detection

#### Step 2: Configure Format
1. **CSV/TXT Files**:
   - Select the detected format from the dropdown
   - Verify column mappings are correct
   - Adjust delimiter settings if needed

2. **KML Files**:
   - Select UTM zone from the dropdown
   - Choose hemisphere (North/South)
   - Verify coordinate system settings

3. **Survey Formats**:
   - Select the appropriate variant (for GSI files)
   - Review format-specific settings

#### Step 3: Preview and Import
1. Click "Import" to process the file
2. Review the imported data in the points grid
3. Check for any validation errors or warnings
4. Make corrections if necessary

### Performance Considerations by Version

#### .NET 8 Version (SPM_NET8_2025_2026)
- **Large Files**: Handles 50,000+ points efficiently
- **Memory Usage**: Optimized memory management
- **Processing Speed**: 25% faster than legacy versions

#### Legacy Versions
- **Recommended Limits**: 10,000-20,000 points for optimal performance
- **Memory Management**: Manual cleanup may be needed for large datasets
- **Processing Speed**: Adequate for most survey applications

## Managing Points

### Points Grid
The main points grid displays all imported survey points with the following columns:
- **Point Number**: Unique identifier for each point
- **Easting**: X-coordinate (typically in meters)
- **Northing**: Y-coordinate (typically in meters)
- **Elevation**: Z-coordinate or height
- **Description**: Text description or point code

### Editing Points

#### In-Place Editing
1. Double-click any cell in the points grid
2. Edit the value directly
3. Press Enter to confirm or Esc to cancel
4. Changes are automatically validated

#### Bulk Operations
- **Select Multiple Points**: Use Ctrl+Click or Shift+Click
- **Delete Points**: Select points and press Delete key
- **Copy/Paste**: Standard clipboard operations supported

### Search and Filter

#### Search Functionality
1. Use the search box at the top of the points grid
2. Search across all fields: Point Number, Coordinates, Description
3. Results update in real-time as you type
4. Clear search to show all points

#### Performance by Version
- **.NET 8**: Real-time search with large datasets
- **Legacy**: May have delays with datasets > 10,000 points

## Exporting Data

### Export Formats
All import formats are also available for export:
- CSV, TXT, KML, SDR, IDX, GSI

### Export Process

#### Step 1: Select Format
1. Go to the Export tab
2. Click "Open Export Dialog"
3. Select desired export format from dropdown
4. Format-specific options will appear

#### Step 2: Configure Options

**CSV/TXT Export**:
- **Include Headers**: Add column headers to output file
- **Include Point Numbers**: Export point numbers
- **Coordinate Order**: Choose XY or YX order
- **Delimiter**: Select separator character

**KML Export**:
- **UTM Zone**: Specify source UTM zone
- **Hemisphere**: North or South hemisphere
- **Coordinate System**: Target coordinate system

**Survey Format Export**:
- **GSI Variant**: Select 8-bit or 16-bit format
- **Format Options**: Specific to each survey format

#### Step 3: Export
1. Click "Export" button
2. Choose destination folder and filename
3. Confirm export settings
4. File will be saved to specified location

### Performance Optimization
- **.NET 8 Version**: Optimized for large exports
- **Legacy Versions**: Process large datasets in smaller batches

## Drawing Tools

### Point Insertion

#### Manual Point Picking
1. Enable "Manual Picking" mode
2. Click points in the AutoCAD drawing
3. Points are automatically numbered and added to the grid
4. Use OSNAP for precise point placement

#### Automatic Point Drawing
1. Import or create points in the grid
2. Click "Draw Points" button
3. Points are inserted into the current AutoCAD drawing
4. Point numbers are added as text labels

### Smart Picking Features

#### Auto Pick
- Automatically detect and number points in the drawing
- Uses intelligent algorithms to identify survey points
- Configurable starting point and numbering sequence

#### Smart Pick (.NET 8 Version Enhanced)
- Interactive point selection with real-time feedback
- Object filtering (blocks, text, points)
- Batch processing of selected objects
- Improved performance and responsiveness

### Drawing Settings

#### Point Display
- **Point Style**: Configure AutoCAD point display style
- **Point Size**: Set point size relative to screen or absolute
- **Layer Assignment**: Specify target layer for points

#### Text Labels
- **Text Height**: Set height for point number labels
- **Text Style**: Choose text style for labels
- **Label Offset**: Position labels relative to points

### Table Generation
- Create formatted tables in AutoCAD model space
- Customizable table layout and formatting
- Automatic pagination for large datasets
- Include headers and formatting options

## Settings Configuration

### General Settings

#### Display Options
- **Units**: Set coordinate display units (meters, feet, etc.)
- **Precision**: Number of decimal places for coordinates
- **Grid Options**: Configure points grid appearance

#### File Handling
- **Default Paths**: Set default import/export directories
- **Auto-Save**: Enable automatic saving of work
- **Backup Options**: Configure backup file creation

### Version-Specific Settings

#### .NET 8 Version Additional Settings
- **Performance Mode**: Optimize for large datasets
- **Memory Management**: Configure caching options
- **UI Responsiveness**: Adjust update frequency

#### Legacy Version Settings
- **Compatibility Mode**: Ensure compatibility with older AutoCAD
- **Memory Limits**: Set safe memory usage limits
- **Processing Batch Size**: Configure batch processing

### Import/Export Defaults

#### Format Preferences
- **Default Import Format**: Set preferred import format
- **Default Export Format**: Set preferred export format
- **Column Mappings**: Save custom column mapping templates

#### Coordinate Systems
- **Default UTM Zone**: Set default UTM zone for imports
- **Hemisphere**: Default hemisphere setting
- **Coordinate Order**: Preferred coordinate order (XY/YX)

## History Management

### Operation History
The History tab maintains a complete record of all operations:
- **Import Operations**: Files imported with timestamps
- **Export Operations**: Files exported with settings
- **Point Modifications**: Changes made to point data
- **Drawing Operations**: Points inserted into drawings

### History Features

#### Search and Filter
- **Date Range**: Filter operations by date
- **Operation Type**: Filter by import, export, edit, etc.
- **File Names**: Search by filename or path
- **User Actions**: Track specific user operations

#### History Performance
- **.NET 8**: Enhanced history management with better performance
- **Legacy**: Basic history functionality

### History Management

#### Cleanup Options
- **Auto-Cleanup**: Automatically remove old history entries
- **Manual Cleanup**: Selectively remove history items
- **Export History**: Save history to external file

## Advanced Features

### Version-Specific Advanced Features

#### .NET 8 Version Exclusive
- **Enhanced Memory Management**: Better handling of large datasets
- **Improved Error Handling**: More detailed error reporting
- **Modern UI Framework**: Better responsiveness and compatibility
- **Windows Forms Integration**: Enhanced compatibility layer

#### All Versions
- **Coordinate System Handling**: UTM projections and transformations
- **Data Validation**: Quality control and validation rules
- **Batch Processing**: Multiple file operations

### Performance Optimization

#### .NET 8 Version
- **Grid Virtualization**: Automatic for datasets > 5,000 points
- **Background Processing**: Non-blocking operations
- **Memory Caching**: Intelligent caching system
- **Multi-threading**: Parallel processing where applicable

#### Legacy Versions
- **Manual Optimization**: User-controlled performance settings
- **Batch Processing**: Process large datasets in chunks
- **Memory Management**: Manual cleanup recommendations

## Troubleshooting

### Version-Specific Issues

#### .NET 8 Version (SPM_NET8_2025_2026)
**Plugin Won't Load**
- Ensure AutoCAD 2025 or 2026 is installed
- Verify .NET 8 runtime is available (included with AutoCAD)
- Check all plugin files are present

**Performance Issues**
- Enable performance mode in settings
- Adjust memory caching settings
- Close unnecessary applications

#### Legacy Versions
**Memory Issues**
- Process large datasets in smaller batches
- Restart AutoCAD periodically with large datasets
- Monitor system memory usage

**Compatibility Issues**
- Ensure correct version for your AutoCAD
- Check .NET Framework version requirements
- Verify all dependencies are installed

### Common Issues (All Versions)

#### Import Problems
**File Won't Import**
- Check file format and encoding
- Verify file is not corrupted or locked
- Ensure file contains valid coordinate data
- Check delimiter settings for CSV/TXT files

**Incorrect Coordinates**
- Verify coordinate order (XY vs YX)
- Check UTM zone and hemisphere settings
- Ensure coordinate system is correctly specified
- Validate source data format

#### Export Problems
**Export Fails**
- Ensure destination folder is writable
- Check available disk space
- Verify export format settings
- Close file if it's open in another application

### Performance Guidelines by Version

#### Dataset Size Recommendations

| Dataset Size | .NET 8 Version | Legacy Versions | Notes |
|--------------|----------------|-----------------|-------|
| < 1,000 points | Excellent | Good | All versions handle well |
| 1,000 - 10,000 | Excellent | Good | .NET 8 has better responsiveness |
| 10,000 - 50,000 | Good | Fair | .NET 8 recommended |
| > 50,000 points | Fair | Poor | .NET 8 required |

### Getting Help

#### Built-in Help
- Hover over controls for tooltips
- Check status bar for current operation info
- Review validation messages for guidance

#### Version Information
- Check About dialog for version details
- Verify you're using the correct version for your AutoCAD
- Review changelog for version-specific features

#### External Resources
- Complete documentation in this repository
- Installation guides for each version
- Technical support contact information

---

## Keyboard Shortcuts

### General Navigation
- **Ctrl + Tab**: Switch between tabs
- **F1**: Show help
- **Esc**: Cancel current operation
- **Ctrl + Z**: Undo last operation
- **Ctrl + Y**: Redo last operation

### Points Grid
- **Ctrl + A**: Select all points
- **Ctrl + C**: Copy selected points
- **Ctrl + V**: Paste points
- **Delete**: Delete selected points
- **F2**: Edit selected cell
- **Ctrl + F**: Open search box

### Import/Export
- **Ctrl + O**: Open import dialog
- **Ctrl + S**: Quick export
- **Ctrl + Shift + S**: Export dialog
- **F5**: Refresh file list

## Customer Support

### Getting Help

As a licensed user of Survey Points Manager, you have access to professional customer support:

#### Technical Support
- **Email Support**: Priority email support for technical issues
- **Response Time**: 24-48 hours for standard issues, same-day for critical problems
- **Remote Assistance**: Screen sharing support available for complex issues
- **Phone Support**: Available for enterprise customers

#### Training and Resources
- **Video Tutorials**: Access to comprehensive video training library
- **Webinars**: Regular training webinars for new features
- **Best Practices**: Industry-specific workflow guides
- **Custom Training**: On-site or remote training available for teams

#### Feature Requests and Feedback
- **Customer Portal**: Submit feature requests and track development
- **Priority Features**: Licensed customers get priority for feature development
- **Beta Testing**: Early access to new features and versions
- **User Community**: Access to private user forums and discussions

### Before Contacting Support

To help us assist you quickly, please have the following information ready:

#### System Information
- **SPM Version**: Check Help → About in the plugin
- **AutoCAD Version**: Including build number
- **Operating System**: Windows version and architecture
- **License Information**: Your license key or customer ID

#### Issue Details
- **Problem Description**: Clear description of the issue
- **Steps to Reproduce**: What you were doing when the problem occurred
- **Error Messages**: Any error messages or codes displayed
- **Sample Data**: If possible, a small sample file that demonstrates the issue

#### Data Information
- **File Format**: CSV, TXT, KML, SDR, IDX, GSI, etc.
- **Dataset Size**: Number of points
- **Coordinate System**: UTM zone, projection, or coordinate system used
- **Source**: Total station, GPS, data collector model if relevant

### Common Support Scenarios

#### Installation and Licensing
- Plugin installation and activation
- License transfer between computers
- Network licensing setup
- Version upgrades and migrations

#### Data Import/Export Issues
- File format compatibility problems
- Coordinate system transformations
- Large dataset performance
- Custom format requirements

#### AutoCAD Integration
- Drawing compatibility issues
- Layer and style management
- Performance optimization
- Workflow customization

#### Training and Best Practices
- Efficient workflows for your industry
- Integration with other surveying software
- Team collaboration and data sharing
- Quality control procedures

### Emergency Support

For critical issues that stop your work:
- **Priority Support**: Mark emails as "URGENT" for faster response
- **Phone Support**: Available during business hours for critical issues
- **Remote Assistance**: Immediate screen sharing support when available

### Feedback and Suggestions

We value your input as a professional user:
- **Feature Requests**: Tell us what would improve your workflow
- **Workflow Feedback**: Share how you use SPM in your projects
- **Industry Needs**: Help us understand surveying and engineering requirements
- **Beta Testing**: Participate in testing new features

---

**Survey Points Manager - Professional User Guide**
*Designed for surveyors, civil engineers, and CAD professionals*

**Licensed Software - Customer Support Included**
*Contact support for assistance with installation, usage, or technical issues*
