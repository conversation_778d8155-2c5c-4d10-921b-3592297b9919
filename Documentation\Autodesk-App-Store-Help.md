# Survey Points Manager - User Guide
**Professional Survey Data Management for AutoCAD**

---

## 📋 Table of Contents
1. [Overview](#overview)
2. [System Requirements](#system-requirements)
3. [Installation](#installation)
4. [Getting Started](#getting-started)
5. [Import Features](#import-features)
6. [Export Features](#export-features)
7. [Point Management](#point-management)
8. [History & Operation Tracking](#history--operation-tracking)
9. [Settings & Configuration](#settings--configuration)
10. [Troubleshooting](#troubleshooting)
11. [Support](#support)

---

## 🎯 Overview

Survey Points Manager is a professional AutoCAD plugin designed for surveyors, civil engineers, and CAD professionals who work with survey data. The plugin streamlines the import, export, and management of survey points across multiple file formats.

### Key Benefits
- **Multi-Format Support**: Import/Export CSV, TXT, GSI, SDR, IDX files
- **Universal Compatibility**: Works with AutoCAD 2015-2026
- **Automatic Detection**: Plugin automatically loads the correct version for your AutoCAD
- **Professional Workflow**: Designed for surveying and civil engineering professionals
- **Time Saving**: Reduces manual data entry and conversion tasks

### Target Users
- **Surveyors**: Import field data from total stations and GPS units
- **Civil Engineers**: Manage survey control points and site data
- **CAD Technicians**: Convert between different survey data formats
- **Construction Professionals**: Work with as-built and layout points

---

## 💻 System Requirements

### AutoCAD Versions
- AutoCAD 2015, 2016, 2017, 2018, 2019, 2020, 2021, 2022, 2023, 2024, 2025, 2026
- AutoCAD LT (limited functionality)
- AutoCAD Civil 3D (full compatibility)

### Operating System
- Windows 7 (32-bit or 64-bit)
- Windows 10 (32-bit or 64-bit)
- Windows 11 (64-bit) - Recommended

### .NET Framework (Automatically installed with AutoCAD)
- .NET Framework 4.5+ (AutoCAD 2015-2024)
- .NET 8.0 (AutoCAD 2025-2026)

### Hardware
- Minimum 4GB RAM (8GB recommended)
- 100MB free disk space
- Display resolution 1024x768 or higher

---

## 🚀 Installation

### Automatic Installation
1. **Download** the plugin from Autodesk App Store
2. **Double-click** the installer file
3. **Follow** the installation wizard
4. **Restart** AutoCAD
5. **Click** the Survey Points Manager icon in the ribbon or toolbar

### Verification
- **Icon**: Survey Points Manager icon should appear in the ribbon or toolbar
- **Command**: `SURVEYPOINTMANAGER` command also works to open the plugin
- **Status**: Plugin loads automatically based on your AutoCAD version

---

## 🎯 Getting Started

### First Launch
1. **Start AutoCAD** (any supported version)
2. **Click** the Survey Points Manager icon in the ribbon/toolbar (or type `SURVEYPOINTMANAGER` command)
3. **License Verification** will appear (first time only)
4. **Main Interface** opens with five tabs:
   - **Home**: Overview and quick actions
   - **Import**: Bring in survey data
   - **Export**: Save data to various formats
   - **History**: Comprehensive operation tracking and management
   - **Settings**: Configure preferences

### Quick Start Workflow
1. **Import** your survey data (CSV, TXT, GSI, etc.)
2. **Review** points in the drawing
3. **Adjust** settings if needed
4. **Export** to your desired format
5. **Save** your AutoCAD drawing

### Interface Overview
- **Dockable Palette**: Can be moved and resized
- **Tab Navigation**: Click tabs to switch between functions
- **Status Messages**: Real-time feedback in the command line
- **Help Integration**: F1 key opens context help

---

## 📥 Import Features

### Supported Formats
- **CSV**: Comma-separated values (Excel compatible)
- **TXT**: Tab or space-delimited text files
- **GSI**: Leica total station format
- **SDR**: Sokkia data recorder format
- **IDX**: Index files with coordinate data (Leica devices)

### Import Process

The import process is organized into two main sections: **Import Section** and **Settings & Display**.

#### Import Section
1. **Click Import Tab** to access the import interface
2. **Import Button**: Main control for file selection
3. **Format Selection**: Multiple format options (CSV, TXT, GSI, SDR, IDX)
   - **Selected format** appears in **bold text**
   - **Available formats** shown as regular text
4. **File Path Display**: Shows the path of the selected file

#### Settings & Display Section
1. **Press Import Button** and select your desired data file
2. **Format Settings Appear**: Automatic format detection based on selected format
   - **Example**: For CSV files, format selector initializes with intelligent guessing
   - **Column Detection**: Automatically suggests PENZ (Point, Easting, Northing, Elevation) or similar
   - **Smart Analysis**: Plugin analyzes data columns and suggests appropriate mapping
3. **Configure Format Settings**: Adjust column assignments if needed
4. **Press Display Button**: Points appear in the points list

#### Data Management & Output
After displaying points, you have full control over the data:

**Point List Management:**
- **Edit Points**: Modify coordinates, descriptions, or point numbers
- **Add Points**: Insert new points manually
- **Remove Points**: Delete unwanted points from the list

**Visualization Options:**
- **Draw Points Table**: Generate a formatted table in the drawing
- **Draw Points**: Place points with numbers in the AutoCAD drawing
- **Visual Representation**: See your survey data graphically

**Export & Conversion:**
- **Export to Any Format**: Save points to CSV, TXT, GSI, IDX, or KML
- **Format Converter**: Plugin acts as a comprehensive survey data converter
- **Flexible Output**: Convert between different survey instrument formats

### Import Options
- **Layer Assignment**: Automatic or custom layer creation
- **Point Styles**: Choose from predefined styles
- **Coordinate Transformation**: UTM, State Plane, Local
- **Filtering**: Import only specific point ranges
- **Validation**: Check for duplicate points

### Common Import Scenarios
- **Total Station Data**: GSI files from Leica instruments
- **GPS Data**: CSV files from field collection
- **Survey Control**: Known coordinate files
- **As-Built Points**: Field measurement data

---

## 📤 Export Features

### Export Formats
- **CSV**: For Excel and database import
- **TXT**: For CAD and GIS applications
- **GSI**: For Leica total stations
- **IDX**: For Leica devices
- **KML**: For Google Earth visualization

### Export Process

The export process is organized into two main sections: **Points** and **Objects**.

#### Points Section
The Points section offers two modes for point selection:

**Manual Mode (PICK Button):**
1. **Press PICK button** to start manual selection
2. **Pick points** from the drawing one by one
3. **Each selected point** appears in the points list
4. **Point numbers** automatically increment to the next available number
5. **Press PICK button again** to finish the selection process

**Auto Mode (AUTO Button):**
1. **Click AUTO button** to start automatic selection
2. **Modal appears** requesting a starting point number
3. **Enter or pick** the starting point from the drawing
4. **Press AUTO button again** and select multiple points from the drawing
5. **All selected points** are automatically renumbered and listed in the points list

#### Objects Section
**Smart Selection Process:**
1. **Press SMART button** to begin object processing
2. **Modal appears** to set properties - enter or pick a starting point (crucial for the process)
3. **Modal reopens** to show the selected point
4. **Click SMART button again** and select blocks or text objects from the drawing
5. **Press Enter** to confirm selection
6. **Selection modal appears** with results - choose desired objects
7. **Select action** for the objects:
   - Delete objects
   - Keep objects
   - Move to specific layer
8. **Press "List Points"** to add points to the points list
9. **Selected action is applied** to objects, points are drawn, and point numbers are written

#### Final Steps
- **Draw Table**: Generate a table of all points
- **Export**: Save points to any available format (CSV, TXT, GSI, IDX, KML)

### Export Options
- **Coordinate Systems**: Transform to different systems
- **Precision Control**: Decimal places for coordinates
- **File Organization**: Single or multiple files
- **Quality Control**: Validation before export

---

## 🎯 Point Management

### Point Creation
- **Manual Entry**: Create points with coordinates
- **Pick Points**: Select existing AutoCAD points
- **Import Integration**: Automatic point creation during import

### Point Editing
- **Coordinate Adjustment**: Modify X, Y, Z values
- **Description Changes**: Update point descriptions
- **Layer Management**: Move points between layers
- **Bulk Operations**: Edit multiple points simultaneously

### Point Visualization
- **Multiple Styles**: Choose from various point symbols
- **Layer Organization**: Automatic layer assignment
- **Color Coding**: Visual organization by type
- **Label Control**: Show/hide point information

### Quality Control
- **Duplicate Detection**: Find and resolve duplicate points
- **Coordinate Validation**: Check for reasonable values
- **Missing Data**: Identify incomplete point records
- **Statistical Analysis**: Point distribution and accuracy

---

## 📊 History & Operation Tracking

### Comprehensive Operation History
The History tab provides **enterprise-level operation tracking** that automatically records every action you perform with Survey Points Manager. This powerful feature ensures complete project traceability and enables advanced workflow management.

#### What Gets Tracked
Every operation is automatically recorded with detailed information:
- **Import Operations**: File imports with source paths, formats, and point counts
- **Export Operations**: File exports with destination paths, formats, and data volumes
- **Point Modifications**: Changes made to point coordinates, descriptions, or properties
- **Drawing Operations**: Points inserted into AutoCAD drawings with timestamps
- **Format Conversions**: Data transformations between different survey formats

#### History Record Details
Each history entry contains comprehensive metadata:
- **File Name**: Complete file path and name
- **Operation Type**: Import, Export, Edit, Draw, Convert
- **File Format**: CSV, TXT, GSI, SDR, IDX, KML
- **Point Count**: Number of points processed
- **Date & Time**: Precise timestamp of operation
- **Session Context**: AutoCAD session and project information

### Advanced Search & Filtering

#### Intelligent Search System
The History tab features a **powerful search engine** that searches across all record fields:
- **File Names**: Search by complete or partial file names
- **Operation Types**: Filter by Import, Export, Edit, Draw operations
- **File Formats**: Find operations by format (CSV, GSI, etc.)
- **Point Counts**: Search by number of points processed
- **Date/Time**: Search by date ranges or specific timestamps
- **Real-time Filtering**: Results update as you type

#### Multi-Field Search
Search across multiple fields simultaneously:
- **Example**: Search "GSI 2024" to find all GSI operations from 2024
- **Example**: Search "Import 500" to find imports with ~500 points
- **Example**: Search "Export CSV" to find all CSV export operations

### Professional History Management

#### Enterprise-Scale Storage
- **Maximum Capacity**: Stores up to **5,000 history records**
- **Automatic Management**: Oldest records automatically removed when limit reached
- **Persistent Storage**: History survives AutoCAD restarts and system reboots
- **XML Serialization**: Reliable data storage with backup capabilities

#### History Maintenance
**Clear History Function:**
- **Safety Confirmation**: Requires user confirmation before clearing
- **Backup Protection**: Creates automatic backup before clearing
- **Instant Refresh**: UI updates immediately after operations
- **Selective Management**: Future versions will support selective deletion

#### Data Persistence
- **Location**: Stored in user's AppData folder for security
- **Format**: XML serialization for reliability and portability
- **Backup**: Automatic backup creation before major operations
- **Recovery**: Built-in recovery mechanisms for data integrity

### Professional Workflow Benefits

#### Project Traceability
- **Complete Audit Trail**: Track every operation from start to finish
- **Quality Assurance**: Verify all import/export operations
- **Project Documentation**: Generate reports of all operations performed
- **Client Reporting**: Provide detailed operation logs to clients

#### Productivity Enhancement
- **Quick File Access**: Easily find and reference previously processed files
- **Operation Patterns**: Identify frequently used workflows
- **Time Tracking**: Monitor time spent on different operations
- **Efficiency Analysis**: Optimize workflows based on history data

#### Error Prevention
- **Duplicate Detection**: Identify if files have been processed before
- **Version Control**: Track different versions of the same dataset
- **Operation Verification**: Confirm successful completion of operations
- **Troubleshooting**: Detailed logs help identify and resolve issues

### User Interface Excellence

#### Modern Design
- **Professional ListView**: Clean, organized display of history records
- **Alternating Rows**: Enhanced readability with alternating row colors
- **Responsive Layout**: Adapts to different screen sizes and resolutions
- **Intuitive Navigation**: Easy scrolling and selection of records

#### Smart Interactions
- **Placeholder Text**: "Search..." placeholder guides user input
- **Focus Management**: Smart focus handling for optimal user experience
- **Real-time Updates**: History updates immediately after operations
- **Visual Feedback**: Clear indication of search results and filtering

---

## ⚙️ Settings & Configuration

### General Settings
- **Default Layers**: Set preferred layer names
- **Point Styles**: Choose default point appearance
- **Coordinate Precision**: Set decimal places
- **File Locations**: Default import/export folders

### Coordinate Systems
- **UTM Zones**: Automatic zone detection
- **State Plane**: US state coordinate systems
- **Local Systems**: Custom coordinate transformations
- **Datum Settings**: NAD83, WGS84, local datums

### Import/Export Preferences
- **Default Formats**: Set preferred file types
- **Column Mapping**: Save common field assignments
- **Validation Rules**: Set quality control parameters
- **Backup Options**: Automatic file backups

### Advanced Settings
- **Performance**: Optimize for large datasets
- **Memory Management**: Handle large point clouds
- **Error Handling**: Configure error responses
- **Logging**: Enable detailed operation logs

---

## 🔧 Troubleshooting

### Common Issues

#### "Unknown command SURVEYPOINTMANAGER"
- **Cause**: Plugin not loaded properly
- **Solution**: 
  1. Restart AutoCAD
  2. Check installation path
  3. Verify AutoCAD version compatibility

#### Import File Not Found
- **Cause**: File path or format issues
- **Solution**:
  1. Check file exists and is accessible
  2. Verify file format matches selection
  3. Try copying file to local drive

#### Points Not Appearing
- **Cause**: Layer visibility or coordinate issues
- **Solution**:
  1. Check layer is visible and thawed
  2. Zoom to extents (ZOOM E)
  3. Verify coordinate system settings

#### Export File Empty
- **Cause**: No points selected or filtered out
- **Solution**:
  1. Verify points are selected
  2. Check export filters
  3. Ensure points have valid coordinates

### Performance Tips
- **Large Files**: Import in smaller batches
- **Memory**: Close unnecessary drawings
- **Speed**: Use SSD storage for data files
- **Stability**: Save work frequently

### Getting Help
- **F1 Key**: Context-sensitive help
- **Command Line**: Detailed error messages
- **Log Files**: Check operation history
- **Support**: Contact technical support

---

## 📞 Support

### Technical Support
- **Email**: <EMAIL>
- **Website**: https://ahmedalsayed.work/about#contact
- **Response Time**: 24-48 hours for technical issues

### Documentation
- **User Guide**: This document
- **Video Tutorials**: Available on website
- **FAQ**: Common questions and answers
- **Release Notes**: Latest updates and fixes

### Community
- **User Forum**: Share tips and solutions
- **Feature Requests**: Suggest improvements
- **Bug Reports**: Report issues for fixes

### Company Information
- **Developer**: Ahmed Al-Sayed Ahmed Abdel-Bari
- **Company**: Pulsar Star
- **Location**: Egypt
- **Established**: Professional CAD solutions since 2020

---

## 📄 Legal Information

### License
- **Type**: Commercial license per user
- **Usage**: Single user, multiple AutoCAD installations
- **Support**: Includes technical support and updates
- **Restrictions**: No redistribution or reverse engineering

### Privacy
- **Data Collection**: Minimal usage statistics only
- **User Data**: No personal information stored
- **File Access**: Only files you explicitly open
- **Security**: All data remains on your computer

### Warranty
- **Functionality**: Plugin works as described
- **Compatibility**: Supports listed AutoCAD versions
- **Updates**: Free updates for major AutoCAD releases
- **Support**: Technical assistance included

---

**© 2024 Pulsar Star. All rights reserved.**
**Survey Points Manager v1.0 - Professional Survey Data Management**
