﻿<UserControl x:Class="SPM_NET46_2017_2018.Views.HomePalette"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:SPM_NET46_2017_2018.Views"
             Width="400"
    Height="600"
    MinWidth="400"
    MinHeight="600"
    mc:Ignorable="d">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/SPM_NET46_2017_2018;component/ResourceDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <UserControl.Background>
        <VisualBrush Stretch="UniformToFill">
            <VisualBrush.Visual>
                <StaticResource ResourceKey="HomeBackground" />
            </VisualBrush.Visual>
        </VisualBrush>
    </UserControl.Background>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <StackPanel
            Grid.Row="0"
            Width="300"
            Height="150"
            Margin="20,10,0,0"
            HorizontalAlignment="Left"
            VerticalAlignment="Top"
            Orientation="Horizontal">
            <ContentControl Template="{StaticResource HomeLogoTemplate}" />
        </StackPanel>

        <Grid
            Grid.Row="1"
            Width="245"
            Height="50"
            Margin="25,0,0,0"
            HorizontalAlignment="Left">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <!--  Import Button  -->
            <Button
                x:Name="ImportBTN"
                Grid.Column="0"
                Width="105"
                Height="40"
                HorizontalAlignment="Left"
                VerticalAlignment="Center"
                Click="ImportBTN_Click"
                Style="{StaticResource OutlinedButtonStyle}">
                <StackPanel
                    Width="auto"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center"
                    Orientation="Horizontal">
                    <Viewbox
                        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                        Width="27"
                        Height="27"
                        Margin="0,0,3,0"
                        HorizontalAlignment="Left"
                        Stretch="Uniform">
                        <Canvas Width="23" Height="23">
                            <Path
                                Data="M4 12a8 8 0 1 0 16 0z"
                                Fill="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"
                                Opacity="0.35" />
                            <Path Data="M15.53 10.47a.75.75 0 0 0-1.06 0l-1.72 1.72V4a.75.75 0 0 0-1.5 0v8.19l-1.72-1.72a.75.75 0 0 0-1.06 1.06l3 3a.75.75 0 0 0 1.06 0l3-3a.75.75 0 0 0 0-1.06" Fill="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}" />
                        </Canvas>
                    </Viewbox>
                    <TextBlock
                        HorizontalAlignment="Right"
                        VerticalAlignment="Center"
                        FontFamily="{StaticResource PoppinsFont}"
                        FontSize="16"
                        Text="Import" />
                </StackPanel>
            </Button>

            <!--  Export Button  -->
            <Button
                x:Name="ExportBTN"
                Grid.Column="2"
                Width="105"
                Height="40"
                HorizontalAlignment="Right"
                VerticalAlignment="Center"
                Click="ExportBTN_Click"
                Style="{StaticResource OutlinedButtonStyle}">
                <StackPanel
                    Width="80"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center"
                    Orientation="Horizontal">
                    <Viewbox
                        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                        Width="27"
                        Height="27"
                        HorizontalAlignment="Left"
                        Stretch="Uniform">
                        <Canvas
                            Width="23"
                            Height="23"
                            Margin="-1,0,0,0">
                            <Path
                                Data="M4 12a8 8 0 1 0 16 0z"
                                Fill="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"
                                Opacity="0.35" />
                            <Path Data="M15.53 7.53a.75.75 0 0 1-1.06 0l-1.72-1.72V14a.75.75 0 0 1-1.5 0V5.81L9.53 7.53a.75.75 0 0 1-1.06-1.06l3-3a.75.75 0 0 1 1.06 0l3 3a.75.75 0 0 1 0 1.06" Fill="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}" />
                        </Canvas>
                    </Viewbox>
                    <TextBlock
                        Margin="1,0,0,0"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Center"
                        FontFamily="{StaticResource PoppinsFont}"
                        FontSize="16"
                        Text="Export" />
                </StackPanel>
            </Button>

        </Grid>


        <Grid
            Grid.Row="2"
            Width="270"
            Height="119"
            Margin="24,0,0,0"
            HorizontalAlignment="Left"
            VerticalAlignment="Center">

            <!--  Frosted glass background  -->
            <Border
                Background="#ececec"
                CornerRadius="10"
                Effect="{DynamicResource BlurEffectShadow}"
                Opacity="0.7" />

            <!--  Text not affected by blur  -->
            <TextBlock
                Margin="12"
                FontFamily="{StaticResource PoppinsFont}"
                FontSize="16"
                FontWeight="Medium"
                Foreground="#FF4B99C5"
                TextWrapping="Wrap">
                <Run Text="Easily import and export points in formats like CSV, IDX, GSI, TXT, SDR, and KML. More formats will be added soon." />
            </TextBlock>
        </Grid>


        <Grid
            Grid.Row="4"
            Width="130"
            Height="51"
            HorizontalAlignment="Right"
            VerticalAlignment="Center">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="5" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="5" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <!--  Settings Button  -->
            <Button
                x:Name="SettingsBTN"
                Grid.Column="0"
                Width="35"
                Height="35"
                Click="SettingsBTN_Click"
                Cursor="Hand"
                Style="{StaticResource IconButtonStyle}"
                ToolTip="Go to App Settings">

                <Viewbox
                    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    Width="32"
                    Height="32">
                    <Canvas Width="23.5" Height="23.5">

                        <!--  Ellipse  -->
                        <Ellipse
                            x:Name="EllipseShape"
                            Canvas.Left="8"
                            Canvas.Top="8"
                            Width="8"
                            Height="8"
                            Stroke="#3ea5e1"
                            StrokeThickness="1.5" />

                        <!--  Path (Cog)  -->
                        <Path
                            x:Name="IconPath"
                            Data="M13.765 2.152C13.398 2 12.932 2 12 2s-1.398 0-1.765.152a2 2 0 0 0-1.083 1.083c-.092.223-.129.484-.143.863a1.62 1.62 0 0 1-.79 1.353a1.62 1.62 0 0 1-1.567.008c-.336-.178-.579-.276-.82-.308a2 2 0 0 0-1.478.396C4.04 5.79 3.806 6.193 3.34 7s-.7 1.21-.751 1.605a2 2 0 0 0 .396 1.479c.148.192.355.353.676.555c.473.297.777.803.777 1.361s-.304 1.064-.777 1.36c-.321.203-.529.364-.676.556a2 2 0 0 0-.396 1.479c.052.394.285.798.75 1.605c.467.807.7 1.21 1.015 1.453a2 2 0 0 0 1.479.396c.24-.032.483-.13.819-.308a1.62 1.62 0 0 1 1.567.008c.483.28.77.795.79 1.353c.************.143.863a2 2 0 0 0 1.083 1.083C10.602 22 11.068 22 12 22s1.398 0 1.765-.152a2 2 0 0 0 1.083-1.083c.092-.223.129-.483.143-.863c.02-.558.307-1.074.79-1.353a1.62 1.62 0 0 1 1.567-.008c.336.178.579.276.819.308a2 2 0 0 0 1.479-.396c.315-.242.548-.646 1.014-1.453s.7-1.21.751-1.605a2 2 0 0 0-.396-1.479c-.148-.192-.355-.353-.676-.555A1.62 1.62 0 0 1 19.562 12c0-.558.304-1.064.777-1.36c.321-.203.529-.364.676-.556a2 2 0 0 0 .396-1.479c-.052-.394-.285-.798-.75-1.605c-.467-.807-.7-1.21-1.015-1.453a2 2 0 0 0-1.479-.396c-.24.032-.483.13-.82.308a1.62 1.62 0 0 1-1.566-.008a1.62 1.62 0 0 1-.79-1.353c-.014-.38-.05-.64-.143-.863a2 2 0 0 0-1.083-1.083Z"
                            Opacity="0.7"
                            Stroke="#3ea5e1"
                            StrokeThickness="1.5" />

                    </Canvas>
                </Viewbox>
            </Button>

            <!--  History Button  -->
            <Button
                x:Name="HistoryBTN"
                Grid.Column="2"
                Width="35"
                Height="35"
                Click="HistoryBTN_Click"
                Cursor="Hand"
                Style="{StaticResource IconButtonStyle}"
                ToolTip="Go to Operations History.">

                <Viewbox Width="33" Height="33">
                    <Canvas Width="23.5" Height="23.5">

                        <!--  First Path  -->
                        <Path
                            x:Name="FirstPath"
                            Data="M12 8v4l2.5 2.5"
                            Fill="#00000000"
                            Stroke="#3ea5e1"
                            StrokeEndLineCap="round"
                            StrokeLineJoin="round"
                            StrokeStartLineCap="round"
                            StrokeThickness="1.5" />

                        <!--  Second Path  -->
                        <Path
                            x:Name="SecondPath"
                            Data="m5.604 5.604l-.53-.53zM4.338 6.871l-.75.003a.75.75 0 0 0 .746.747zm2.542.762a.75.75 0 1 0 .007-1.5zM5.075 4.321a.75.75 0 1 0-1.5.008zm-1.248 6.464a.75.75 0 1 0-1.486-.204zm15.035-5.647c-3.82-3.82-9.993-3.86-13.788-.064l1.06 1.06c3.2-3.199 8.423-3.18 11.668.064zM5.138 18.862c3.82 3.82 9.993 3.86 13.788.064l-1.06-1.06c-3.2 3.199-8.423 3.18-11.668-.064zm13.788.064c3.795-3.795 3.756-9.968-.064-13.788l-1.06 1.06c3.244 3.245 3.263 8.468.064 11.668zM5.074 5.074L3.807 6.34L4.868 7.4l1.266-1.266zm-.74 2.547l2.546.012l.007-1.5l-2.545-.012zm.754-.754L5.075 4.32l-1.5.008l.013 2.545zM2.34 10.58a9.81 9.81 0 0 0 2.797 8.281l1.06-1.06a8.31 8.31 0 0 1-2.371-7.017z"
                            Fill="#3ea5e1"
                            Opacity="0.5"
                            StrokeThickness="2" />
                    </Canvas>
                </Viewbox>
            </Button>

            <!--  Help Button  -->
            <Button
                x:Name="HelpBTN"
                Grid.Column="4"
                Width="35"
                Height="35"
                Click="HelpBTN_Click"
                Cursor="Hand"
                Style="{StaticResource IconButtonStyle}"
                ToolTip="Access plugin documentation and guides.">
                <Viewbox
                    Width="33"
                    Height="33"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Stretch="Uniform">
                    <Canvas Width="16" Height="16">
                        <Path Fill="#3ea5e1" Opacity="0.5">
                            <Path.Data>
                                <PathGeometry Figures="M8 15c-3.86 0-7-3.14-7-7s3.14-7 7-7s7 3.14 7 7s-3.14 7-7 7M8 2C4.69 2 2 4.69 2 8s2.69 6 6 6s6-2.69 6-6s-2.69-6-6-6" />
                            </Path.Data>
                        </Path>
                        <Path Fill="#3ea5e1">
                            <Path.Data>
                                <PathGeometry Figures="M8 4.5c-1.11 0-2 .89-2 2h1c0-.55.45-1 1-1s1 .45 1 1c0 1-1.5.88-1.5 2.5h1c0-1.12 1.5-1.25 1.5-2.5c0-1.11-.89-2-2-2" />
                            </Path.Data>
                        </Path>
                        <Ellipse
                            Canvas.Left="7.38"
                            Canvas.Top="10.38"
                            Width="1.24"
                            Height="1.24"
                            Fill="#3ea5e1" />
                        <Ellipse
                            Canvas.Left="6"
                            Canvas.Top="6"
                            Width="1"
                            Height="1"
                            Fill="#3ea5e1" />
                        <Ellipse
                            Canvas.Left="7.5"
                            Canvas.Top="8.5"
                            Width="1"
                            Height="1"
                            Fill="#3ea5e1" />
                    </Canvas>
                </Viewbox>
            </Button>

        </Grid>

        <StackPanel
            Grid.Row="4"
            Width="40"
            Height="20"
            Margin="0,0,10,5"
            HorizontalAlignment="Right"
            VerticalAlignment="Bottom"
            Orientation="Horizontal">
            <TextBlock
                FontFamily="{StaticResource PoppinsFont}"
                FontSize="13"
                FontWeight="Medium"
                Foreground="#FF2897D7"
                Text="v1.0.0" />
        </StackPanel>

    </Grid>
</UserControl>
