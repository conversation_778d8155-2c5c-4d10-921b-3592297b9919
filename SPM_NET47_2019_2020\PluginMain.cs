﻿using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Runtime;
using Autodesk.AutoCAD.Windows;
using SPM_NET47_2019_2020.Managers;
using System;
using System.Diagnostics;
using System.Drawing;
using System.Net;
using System.Net.Http;

// Assembly attributes
[assembly: ExtensionApplication(typeof(SPM_NET47_2019_2020.PluginMain))]
[assembly: CommandClass(typeof(SPM_NET47_2019_2020.PluginMain))]

namespace SPM_NET47_2019_2020
{

    public class PluginMain : IExtensionApplication
    {
        #region Constants and Static Fields

        private const string CMD_GROUP = "SPM"; // Internal command group name
        private const string PLUGIN_DISPLAY_NAME = "Survey Point Manager"; // User-facing name

        // --- Palettes GUIDs & Titles ---
        // MAIN_PALETTE_TITLE is already "Survey Point Manager" - GOOD
        private const string MAIN_PALETTE_TITLE = "Survey Point Manager";
        // MAIN_PALETTE_INTERNAL_NAME is for AutoCAD's internal tracking, can remain PointFlow related
        private const string MAIN_PALETTE_INTERNAL_NAME = "SPMMainTools";
        private static readonly Guid MAIN_PALETTE_SET_GUID = new Guid("C6CA2521-7CF9-4FA1-BA5F-328AFB71C072");

        // ENTITLEMENT_PALETTE_TITLE should reflect the plugin it's for
        private const string ENTITLEMENT_PALETTE_TITLE = "Survey Point Manager Registration";
        // ENTITLEMENT_PALETTE_INTERNAL_NAME can remain PointFlow related
        private const string ENTITLEMENT_PALETTE_INTERNAL_NAME = "SPMEntitlementPrompt";
        private static readonly Guid ENTITLEMENT_PALETTE_GUID = Guid.NewGuid();

        // --- Tab Indices for Main Palette ---
        private const int TAB_INDEX_HOME = 0;
        private const int TAB_INDEX_IMPORT = 1;
        private const int TAB_INDEX_EXPORT = 2;
        private const int TAB_INDEX_HISTORY = 3;
        private const int TAB_INDEX_SETTINGS = 4;

        // --- Palette Instances ---
        public static PaletteSet MainPaletteSet { get; private set; }
        private static PaletteSet _entitlementPromptPaletteSet;

        // --- UI Control Instances ---
        private static Views.EntitlementPromptControl _entitlementPromptControl;
        private static Views.HomePalette _homePaletteView;
        private static Views.ImportPalette _importPaletteView;
        private static Views.ExportPalette _exportPaletteView;
        private static Views.HistoryPalette _historyPaletteView;
        private static Views.SettingsPalette _settingsPaletteView;

        // --- State Management ---
        private static Document _associatedDocument = null;
        public static bool IsUserVerifiedThisSession { get; private set; } = false;
        private static EntitlementManager _entitlementManager;

        // --- Static Instance for Navigation ---
        private static PluginMain _instance;

        #endregion

        #region AutoCAD Helper Properties

        private static DocumentCollection DocumentManager => Application.DocumentManager;
        private static Document ActiveDocument => DocumentManager.MdiActiveDocument;
        private static Database ActiveDatabase => ActiveDocument?.Database;
        private static Editor ActiveEditor => ActiveDocument?.Editor;

        #endregion

        #region IExtensionApplication Implementation

        public void Initialize()
        {
            try
            {
                _instance = this; // Set static instance
                _entitlementManager = new EntitlementManager();
                // Use PLUGIN_DISPLAY_NAME for user messages
                WriteToCommandLine($"\n{PLUGIN_DISPLAY_NAME} plugin initialized. Use _SURVEYPOINTMANAGER command.");
                DocumentManager.DocumentToBeDestroyed += OnDocumentToBeDestroyed;
            }
            catch (System.Exception ex) { LogException("Error during plugin initialization", ex); }
        }

        public void Terminate()
        {
            try
            {
                if (_entitlementPromptPaletteSet != null && !_entitlementPromptPaletteSet.IsDisposed)
                {
                    _entitlementPromptPaletteSet.Dispose();
                }

                if (MainPaletteSet != null && !MainPaletteSet.IsDisposed)
                {
                    MainPaletteSet.Dispose();
                }

                MainPaletteSet = null; _entitlementPromptPaletteSet = null;
                _associatedDocument = null; _entitlementPromptControl = null;
                _homePaletteView = null; _importPaletteView = null; _exportPaletteView = null;
                _historyPaletteView = null; _settingsPaletteView = null;
                _entitlementManager = null;

                DocumentManager.DocumentToBeDestroyed -= OnDocumentToBeDestroyed;
                // Use PLUGIN_DISPLAY_NAME
                WriteToCommandLine($"\n{PLUGIN_DISPLAY_NAME} plugin terminated.");
            }
            catch (System.Exception ex) { LogException("Error during plugin termination", ex); }
        }

        #endregion

        #region Event Handlers

        private void OnDocumentToBeDestroyed(object sender, DocumentCollectionEventArgs e)
        {
            if (e.Document == _associatedDocument)
            {
                if (MainPaletteSet != null && !MainPaletteSet.IsDisposed && MainPaletteSet.Visible)
                {
                    try { MainPaletteSet.Visible = false; }
                    catch (System.Exception ex) { LogException($"Error hiding {MAIN_PALETTE_TITLE} on close of '{e.Document?.Name ?? "document"}'", ex); }
                }
                _associatedDocument = null;
            }
        }

        private void OnEntitlementSucceededFromPrompt()
        {
            try
            {
                IsUserVerifiedThisSession = true;
                // Use PLUGIN_DISPLAY_NAME
                WriteToCommandLine($"\n{PLUGIN_DISPLAY_NAME}: Entitlement verified via prompt.");
                if (_entitlementPromptPaletteSet != null && _entitlementPromptPaletteSet.Visible)
                {
                    _entitlementPromptPaletteSet.Visible = false;
                }
                ShowMainApplicationPalette(ActiveDocument);
            }
            catch (System.Exception ex) { LogException("Error transitioning from prompt to main palette", ex); }
        }

        #endregion

        #region AutoCAD Commands

        [CommandMethod(CMD_GROUP, "SURVEYPOINTMANAGER", CommandFlags.Session)]
        public void ShowSurveyPointManagerCommand()
        {
            if (IsUserVerifiedThisSession)
            {
                ShowMainApplicationPalette(ActiveDocument);
                return;
            }

            if (_entitlementManager == null)
            {
                LogException("EntitlementManager not initialized in Command.", new InvalidOperationException("EntitlementManager is null."));
                _entitlementManager = new EntitlementManager();
            }

            EntitlementManager.CachedEntitlementStatus cacheStatus = _entitlementManager.GetCachedStatus();
            if (cacheStatus == EntitlementManager.CachedEntitlementStatus.Entitled)
            {
                IsUserVerifiedThisSession = true;
                // Use PLUGIN_DISPLAY_NAME
                ActiveEditor?.WriteMessage($"\n{PLUGIN_DISPLAY_NAME}: License verified from cache for this session.");
                ShowMainApplicationPalette(ActiveDocument);
                return;
            }
            ShowEntitlementPromptPalette();
        }

        #endregion

        #region Palette Management Methods

        private void ShowMainApplicationPalette(Document currentDoc)
        {
            if (currentDoc == null)
            {
                // Use PLUGIN_DISPLAY_NAME
                ActiveEditor?.WriteMessage($"\n{PLUGIN_DISPLAY_NAME}: Main tools require an active drawing.");
                if (MainPaletteSet != null && !MainPaletteSet.IsDisposed && MainPaletteSet.Visible)
                {
                    MainPaletteSet.Visible = false;
                }

                _associatedDocument = null;
                return;
            }
            try
            {
                if (_entitlementPromptPaletteSet != null && !_entitlementPromptPaletteSet.IsDisposed && _entitlementPromptPaletteSet.Visible)
                {
                    _entitlementPromptPaletteSet.Visible = false;
                }

                if (MainPaletteSet == null || MainPaletteSet.IsDisposed)
                {
                    InitializeMainPaletteSet();
                }

                if (MainPaletteSet.Visible && _associatedDocument == currentDoc)
                {
                    MainPaletteSet.Visible = false;
                    // Use MAIN_PALETTE_TITLE or PLUGIN_DISPLAY_NAME
                    WriteToCommandLine($"\n{MAIN_PALETTE_TITLE} hidden for '{currentDoc.Name}'.");
                }
                else
                {
                    MainPaletteSet.Visible = true;
                    _associatedDocument = currentDoc;
                    ActivateMainPaletteTab(TAB_INDEX_HOME, "Home");
                    // Use MAIN_PALETTE_TITLE or PLUGIN_DISPLAY_NAME
                    WriteToCommandLine($"\n{MAIN_PALETTE_TITLE} shown and associated with '{currentDoc.Name}'.");
                }
            }
            catch (System.Exception ex) { LogException($"Error showing main palette for '{currentDoc?.Name ?? "N/A"}'", ex); }
        }

        private void ShowEntitlementPromptPalette()
        {
            try
            {
                if (MainPaletteSet != null && !MainPaletteSet.IsDisposed && MainPaletteSet.Visible)
                {
                    MainPaletteSet.Visible = false;
                }

                if (_entitlementPromptPaletteSet == null || _entitlementPromptPaletteSet.IsDisposed)
                {
                    _entitlementPromptPaletteSet = new PaletteSet(ENTITLEMENT_PALETTE_TITLE, ENTITLEMENT_PALETTE_INTERNAL_NAME, ENTITLEMENT_PALETTE_GUID)
                    {
                        DockEnabled = DockSides.None,
                        Size = new System.Drawing.Size(500, 750),
                        MinimumSize = new System.Drawing.Size(500, 750),
                        Style = PaletteSetStyles.ShowCloseButton | PaletteSetStyles.Snappable,
                        Icon = Properties.Resources.Plugin_Small_Logo_Illustrator_32x32
                    };
                    if (_entitlementPromptControl == null)
                    {
                        if (_entitlementManager == null)
                        {
                            LogException("EntitlementManager null in ShowEntitlementPromptPalette.", new InvalidOperationException());
                            _entitlementManager = new EntitlementManager();
                        }
                        _entitlementPromptControl = new Views.EntitlementPromptControl(_entitlementManager);
                        _entitlementPromptControl.EntitlementSucceeded += OnEntitlementSucceededFromPrompt;
                    }
                    _entitlementPromptPaletteSet.AddVisual("Verify License", _entitlementPromptControl);
                }
                _entitlementPromptPaletteSet.Visible = true;
            }
            catch (System.Exception ex) { LogException("Error showing entitlement prompt palette", ex); }
        }

        private void InitializeMainPaletteSet()
        {
            // Use PLUGIN_DISPLAY_NAME or MAIN_PALETTE_TITLE for log message
            WriteToCommandLine($"\nInitializing {MAIN_PALETTE_TITLE}...");
            MainPaletteSet = new PaletteSet(MAIN_PALETTE_TITLE, MAIN_PALETTE_INTERNAL_NAME, MAIN_PALETTE_SET_GUID)
            {
                DockEnabled = DockSides.Left | DockSides.Right | DockSides.Top | DockSides.Bottom,
                Size = new Size(500, 750),
                MinimumSize = new Size(500, 750),
                Style = PaletteSetStyles.ShowPropertiesMenu | PaletteSetStyles.ShowAutoHideButton | PaletteSetStyles.ShowCloseButton | PaletteSetStyles.Snappable,
                TitleBarLocation = PaletteSetTitleBarLocation.Left,
                Icon = Properties.Resources.Plugin_Small_Logo_Illustrator_32x32
            };
            try
            {
                if (_homePaletteView == null)
                {
                    _homePaletteView = new Views.HomePalette();
                }

                if (_importPaletteView == null)
                {
                    _importPaletteView = new Views.ImportPalette();
                }

                if (_exportPaletteView == null)
                {
                    _exportPaletteView = new Views.ExportPalette();
                }

                if (_historyPaletteView == null)
                {
                    _historyPaletteView = new Views.HistoryPalette();
                }

                if (_settingsPaletteView == null)
                {
                    _settingsPaletteView = new Views.SettingsPalette();
                }

                MainPaletteSet.AddVisual("Home", _homePaletteView);
                MainPaletteSet.AddVisual("Import", _importPaletteView);
                MainPaletteSet.AddVisual("Export", _exportPaletteView);
                MainPaletteSet.AddVisual("History", _historyPaletteView);
                MainPaletteSet.AddVisual("Settings", _settingsPaletteView);
                // Use PLUGIN_DISPLAY_NAME or MAIN_PALETTE_TITLE
                WriteToCommandLine($"\n{MAIN_PALETTE_TITLE}: Main palettes added.");
            }
            catch (System.Exception ex) { LogException("Error adding main palettes", ex); }
        }

        private void ActivateMainPaletteTab(int tabIndex, string tabNameForLogging)
        {
            if (MainPaletteSet == null || MainPaletteSet.IsDisposed || !MainPaletteSet.Visible)
            {
                return;
            }

            if (tabIndex >= 0 && tabIndex < MainPaletteSet.Count)
            {
                MainPaletteSet.Activate(tabIndex);
            }
            else if (MainPaletteSet.Count > 0)
            {
                MainPaletteSet.Activate(TAB_INDEX_HOME);
            }
        }

     
        

        public static void ShowHomePalette() {
            Debug.WriteLine("_instance", _instance);
            _instance?.EnsureEntitledAndActivateTab(TAB_INDEX_HOME, "Home"); 
        }
        public static void ShowImportPalette() { _instance?.EnsureEntitledAndActivateTab(TAB_INDEX_IMPORT, "Import"); }
        public static void ShowExportPalette() { _instance?.EnsureEntitledAndActivateTab(TAB_INDEX_EXPORT, "Export"); }
        public static void ShowHistoryPalette() { _instance?.EnsureEntitledAndActivateTab(TAB_INDEX_HISTORY, "History"); }
        public static void ShowSettingsPalette() { _instance?.EnsureEntitledAndActivateTab(TAB_INDEX_SETTINGS, "Settings"); }

        private void EnsureEntitledAndActivateTab(int tabIndex, string tabNameForLogging)
        {
            if (IsUserVerifiedThisSession)
            {
                ShowMainApplicationPalette(ActiveDocument);
                if (MainPaletteSet != null && MainPaletteSet.Visible && _associatedDocument != null)
                {
                    ActivateMainPaletteTab(tabIndex, tabNameForLogging);
                }
            }
            else
            {
                // Use PLUGIN_DISPLAY_NAME
                ActiveEditor?.WriteMessage($"\n{PLUGIN_DISPLAY_NAME}: License verification required to access '{tabNameForLogging}'.");
                ShowEntitlementPromptPalette();
            }
        }

        #endregion

        #region Test Commands
        // Test commands remain async void as they directly await the entitlement check
        // User-facing messages in test commands can also use PLUGIN_DISPLAY_NAME if desired.

        [CommandMethod(CMD_GROUP, "SPM_TEST_FULL_ENTITLEMENT", CommandFlags.Session)]
        public async void TestFullEntitlementCheckCommand()
        {
            Editor ed = ActiveEditor;
            if (ed == null)
            {
                return;
            }

            ed.WriteMessage($"\n--- Starting FULL Entitlement Check Test for {PLUGIN_DISPLAY_NAME} ---");
            try
            {
                if (_entitlementManager == null)
                {
                    _entitlementManager = new EntitlementManager();
                }

                EntitlementStatus status = await _entitlementManager.CheckUserEntitlementAsync();
                ed.WriteMessage($"\nFULL Entitlement Result: {status}");
                IsUserVerifiedThisSession = status == EntitlementStatus.Entitled || status == EntitlementStatus.OfflineCacheValid;
                if (IsUserVerifiedThisSession)
                {
                    if (_entitlementPromptPaletteSet != null && _entitlementPromptPaletteSet.Visible)
                    {
                        _entitlementPromptPaletteSet.Visible = false;
                    }

                    ShowMainApplicationPalette(ActiveDocument);
                }
                else
                {
                    ShowEntitlementPromptPalette();
                }
            }
            catch (System.Exception ex) { LogException("Error in TestFullEntitlementCheckCommand", ex); }
            ed.WriteMessage($"\n--- FULL Entitlement Check Test COMPLETE for {PLUGIN_DISPLAY_NAME} ---");
        }

        [CommandMethod(CMD_GROUP, "SPM_TEST_ONLINE_ENTITLEMENT_ONLY", CommandFlags.Session)]
        public async void TestOnlineEntitlementOnlyCommand()
        {
            Editor ed = ActiveEditor;
            if (ed == null)
            {
                return;
            }

            ed.WriteMessage($"\n--- Starting ONLINE-ONLY Entitlement Check Test for {PLUGIN_DISPLAY_NAME} ---");
            try
            {
                var testEntitlementManager = new EntitlementManager();
                EntitlementStatus status = await testEntitlementManager.CheckUserEntitlementAsync();
                ed.WriteMessage($"\nONLINE-Focused Entitlement Result: {status}");
            }
            catch (System.Exception ex) { LogException("Error in SPM_TEST_ONLINE_ENTITLEMENT_ONLY command", ex); }
            ed.WriteMessage($"\n--- ONLINE-ONLY Entitlement Check Test COMPLETE for {PLUGIN_DISPLAY_NAME} ---");
        }

        [CommandMethod(CMD_GROUP, "SPM_TEST_OFFLINE_CACHE_ONLY", CommandFlags.Session)]
        public async void TestOfflineCacheOnlyCommand()
        {
            Editor ed = ActiveEditor;
            if (ed == null)
            {
                return;
            }

            ed.WriteMessage($"\n--- Starting OFFLINE CACHE Entitlement Check Test for {PLUGIN_DISPLAY_NAME} ---");
            try
            {
                var testEntitlementManager = new EntitlementManager();
                EntitlementStatus status = await testEntitlementManager.CheckUserEntitlementAsync();
                ed.WriteMessage($"\nOFFLINE CACHE Entitlement Result: {status}");
            }
            catch (System.Exception ex) { LogException("Error in SPM_TEST_OFFLINE_CACHE_ONLY command", ex); }
            ed.WriteMessage($"\n--- OFFLINE CACHE Entitlement Check Test COMPLETE for {PLUGIN_DISPLAY_NAME} ---");
        }

        [CommandMethod(CMD_GROUP, "SPM_TEST_WEB_CLIENT", CommandFlags.Session)]
        public async void TestWebClientCommand()
        {
            Editor ed = ActiveEditor;
            if (ed == null)
            {
                return;
            }

            ed.WriteMessage("\n--- Starting Basic Web Client Entitlement Test ---");
            const string TEST_USER_ID = "9EHFNJRGCKGR";
            const string TEST_APP_ID_20_CHAR = "5391612840032124388";
            const string AUTODESK_API_BASE_URL = "https://apps.autodesk.com";
            string requestUrl = $"{AUTODESK_API_BASE_URL}/webservices/checkentitlement?userid={WebUtility.UrlEncode(TEST_USER_ID)}&appid={WebUtility.UrlEncode(TEST_APP_ID_20_CHAR)}";
            ed.WriteMessage($"\nRequesting URL: {requestUrl}");
            using (var client = new HttpClient())
            {
                client.Timeout = TimeSpan.FromSeconds(20);
                try
                {
                    HttpResponseMessage response = await client.GetAsync(requestUrl);
                    ed.WriteMessage($"\nResponse Status Code: {response.StatusCode}");
                    string responseBody = await response.Content.ReadAsStringAsync();
                    ed.WriteMessage("\n--- Raw Response Body ---\n" + responseBody + "\n--- End of Raw Response Body ---");
                    if (response.IsSuccessStatusCode)
                    {
                        ed.WriteMessage("\nRequest successful (HTTP 2xx).");
                    }
                    else
                    {
                        ed.WriteMessage($"\nRequest failed with status: {response.StatusCode}");
                    }
                }
                catch (System.Exception ex) { ed.WriteMessage($"\nWeb Client Test Exception: {ex.ToString()}"); }
            }
            ed.WriteMessage("\n--- Basic Web Client Entitlement Test COMPLETE ---");
        }

        #endregion

        #region Utilities

        private void WriteToCommandLine(string message)
        {
            ActiveEditor?.WriteMessage(message);
        }

        private void LogException(string contextMessage, System.Exception ex)
        {
            Editor editor = ActiveEditor;
            // Use PLUGIN_DISPLAY_NAME in error logs
            editor?.WriteMessage($"\nERROR in {PLUGIN_DISPLAY_NAME} ({contextMessage}): {ex.Message}");
#if DEBUG
            editor?.WriteMessage($"\n  Type: {ex.GetType().FullName}");
            if (ex.InnerException != null) { editor?.WriteMessage($"\n  Inner Exception: {ex.InnerException.Message}"); }
            editor?.WriteMessage($"\n  Stack Trace:\n{ex.StackTrace}");
#endif
        }

        #endregion
    }

}
