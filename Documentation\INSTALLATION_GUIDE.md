# Survey Points Manager - Installation Guide

*Professional AutoCAD Plugin for Survey Data Management*

## Welcome

Thank you for purchasing Survey Points Manager! This guide will help you install and activate your professional survey data management plugin.

## Automatic Version Detection

Survey Points Manager uses **automatic version detection** - you don't need to choose a version! The plugin bundle contains all versions and automatically loads the correct one for your AutoCAD:

| AutoCAD Version | Auto-Loaded DLL | Framework | Status |
|-----------------|-----------------|-----------|---------|
| **2025, 2026** | **SPM_NET8_2025_2026.dll** | .NET 8.0 | ✅ **Latest** |
| 2021-2024 | SPM_NET48_2021_2024.dll | .NET Framework 4.8 | 🔧 Supported |
| 2019-2020 | SPM_NET47_2019_2020.dll | .NET Framework 4.7 | 🔧 Supported |
| 2017-2018 | SPM_NET46_2017_2018.dll | .NET Framework 4.6 | 🔧 Supported |
| **2015-2016** | **SPM_NET45_2015_2016.dll** | **.NET Framework 4.5** | 🔧 **Supported** |

**The plugin automatically detects your AutoCAD version and loads the appropriate DLL - no manual selection required!**

## Quick Installation

### Prerequisites (All Versions)
- **AutoCAD**: Appropriate version installed and licensed
- **Windows**: Windows 10/11 (64-bit recommended)
- **Administrator privileges**: For installation
- **Available disk space**: 50MB minimum

### Licensing Requirements
- **Valid License**: You must have a valid Survey Points Manager license
- **License Key**: Provided with your purchase confirmation
- **Internet Connection**: Required for initial activation
- **Administrator Rights**: Needed for installation and first-time activation

### Version-Specific Prerequisites

#### SPM_NET8_2025_2026 (Recommended)
- **AutoCAD 2025 or 2026** (required)
- **.NET 8 Runtime**: Included with AutoCAD 2025/2026
- **Memory**: 4GB RAM minimum (8GB recommended)

#### Legacy Versions
- **Appropriate .NET Framework**: Usually included with AutoCAD
- **Memory**: 2GB RAM minimum (4GB recommended)

## Installation Methods

### Method 1: Bundle Installation (Recommended - Automatic Loading)

#### Step 1: Download and Extract
1. Download the Survey Points Manager bundle
2. Extract the `SurveyPointsManager.bundle` folder
3. The bundle contains all versions - no need to choose!

#### Step 2: Install Bundle
**Option A: User Installation**
1. Copy `SurveyPointsManager.bundle` to:
   ```
   %APPDATA%\Autodesk\ApplicationPlugins\
   ```

**Option B: System-wide Installation**
1. Copy `SurveyPointsManager.bundle` to:
   ```
   %ALLUSERSPROFILE%\Autodesk\ApplicationPlugins\
   ```

#### Step 3: Restart AutoCAD
1. Close AutoCAD completely
2. Restart AutoCAD (any supported version)
3. The plugin will **automatically load** the correct version
4. The SPM palette should appear automatically
5. If not visible, type `SPM` to show the palette

### Method 2: Manual NETLOAD (For Testing/Development)

#### For Developers or Testing
1. Open AutoCAD
2. Type `NETLOAD` in the command line
3. Browse to `SurveyPointsManager.bundle\Contents\`
4. AutoCAD will automatically select the correct DLL for your version
5. Click "Load"

### Method 2: Startup Suite (Auto-Load)

#### For All Versions
1. **Access Startup Suite**
   - In AutoCAD, type `APPLOAD`
   - Click "Startup Suite" button

2. **Add Plugin**
   - Click "Add" in the Startup Suite dialog
   - Browse to the appropriate `.dll` file
   - Select the file and click "Add"

3. **Configure Auto-Loading**
   - Ensure the plugin is checked in the list
   - Click "Close" to save settings
   - Plugin will load automatically on AutoCAD startup

### Method 3: Registry Installation (Advanced Users)

#### Registry Configuration
1. **Open Registry Editor** (regedit.exe)
2. **Navigate to AutoCAD Registry Key**:
   - AutoCAD 2025/2026: `HKEY_CURRENT_USER\Software\Autodesk\AutoCAD\R25.0\ACAD-xxxx\Applications`
   - AutoCAD 2021-2024: `HKEY_CURRENT_USER\Software\Autodesk\AutoCAD\R24.0\ACAD-xxxx\Applications`
   - (Adjust version number accordingly)

3. **Create Plugin Entry**
   - Create new key with plugin name (e.g., `SPM_NET8_2025_2026`)
   - Set required registry values

4. **Registry Values**
   - `LOADCTRLS` = `14`
   - `LOADER` = `[full path to .dll file]`
   - `MANAGED` = `1`

## File Structure

### Required Files (All Versions)

#### SPM_NET8_2025_2026
```
SPM_Plugin/
├── SPM_NET8_2025_2026.dll          # Main plugin assembly
├── Newtonsoft.Json.dll              # JSON processing library
├── SPM_NET8_2025_2026.dll.config   # Configuration file (if present)
└── Resources/                       # Plugin resources (if any)
```

#### Legacy Versions
```
SPM_Plugin/
├── [Version].dll                    # Main plugin assembly
├── Newtonsoft.Json.dll              # JSON processing library
├── [Version].dll.config             # Configuration file
└── Resources/                       # Plugin resources
```

### File Permissions
- Ensure all files have read permissions
- Plugin folder should be accessible to AutoCAD
- Avoid placing in system-protected directories

## Configuration

### Initial Setup (All Versions)
1. **First Launch**
   - Plugin loads automatically after installation
   - SPM palette appears on the right side
   - Default settings are applied

2. **Settings Configuration**
   - Go to Settings tab in SPM palette
   - Configure default paths for import/export
   - Set coordinate system preferences
   - Adjust performance settings if needed

### Version-Specific Configuration

#### SPM_NET8_2025_2026
- **Performance Settings**: Configure for large datasets
- **Memory Management**: Adjust caching options
- **UI Responsiveness**: Set update frequency

#### Legacy Versions
- **Compatibility Settings**: Ensure AutoCAD compatibility
- **Memory Limits**: Set safe usage limits
- **Batch Processing**: Configure processing sizes

## Verification

### Test Installation (All Versions)
1. **Load Test**
   - Restart AutoCAD
   - Verify SPM palette loads automatically
   - Check all tabs are functional

2. **Basic Functionality**
   - Try importing a small CSV file
   - Verify points display in the grid
   - Test export functionality

3. **AutoCAD Integration**
   - Test point insertion into drawing
   - Verify OSNAP functionality
   - Check table generation features

### Performance Verification

#### SPM_NET8_2025_2026
- Test with larger datasets (10,000+ points)
- Verify smooth UI responsiveness
- Check memory usage efficiency

#### Legacy Versions
- Test with moderate datasets (1,000-5,000 points)
- Monitor memory usage
- Verify stable operation

## Troubleshooting Installation

### Common Issues (All Versions)

#### Plugin Won't Load
- **Check AutoCAD Version**: Ensure correct SPM version for your AutoCAD
- **Verify Dependencies**: Ensure all required files are present
- **Check Permissions**: Verify file and folder permissions
- **Framework Requirements**: Confirm .NET Framework/.NET 8 availability

#### Missing Dependencies
- **Newtonsoft.Json.dll**: Must be present in plugin folder
- **Blocked Files**: Right-click files → Properties → Unblock if needed
- **File Integrity**: Re-download if files are corrupted

#### Palette Not Visible
- Type `SPM` command to show palette
- Check if palette is docked or floating
- Reset AutoCAD workspace if necessary
- Verify plugin loaded successfully

### Version-Specific Issues

#### SPM_NET8_2025_2026
- **AutoCAD Version**: Must be 2025 or 2026
- **.NET 8 Runtime**: Included with AutoCAD 2025/2026
- **Windows Compatibility**: Requires Windows 10/11

#### Legacy Versions
- **Framework Version**: Ensure correct .NET Framework installed
- **AutoCAD Compatibility**: Match version requirements exactly
- **Memory Limitations**: May require more frequent restarts

### Performance Issues
- **Close Unnecessary Applications**: Free up system resources
- **Increase Available Memory**: Add more RAM if possible
- **Check AutoCAD Graphics Settings**: Adjust for performance
- **Disk Space**: Ensure adequate free space

## Network Installation

### Shared Network Drive
1. **Setup Network Location**
   - Place plugin files on accessible network drive
   - Ensure all users have read permissions
   - Test network connectivity and speed

2. **Client Configuration**
   - Each client loads plugin from network location
   - Use UNC paths for reliability
   - Consider local caching for performance

### Deployment Script Example
```batch
@echo off
echo Installing SPM Plugin...
set VERSION=SPM_NET8_2025_2026
copy "\\server\share\%VERSION%\*.*" "C:\%VERSION%\"
echo Plugin files copied.
echo.
echo To complete installation:
echo 1. Open AutoCAD 2025/2026
echo 2. Type NETLOAD
echo 3. Select C:\%VERSION%\%VERSION%.dll
echo 4. Click Load
pause
```

## Updates and Maintenance

### Updating the Plugin
1. **Backup Current Version**
   - Save current plugin files
   - Export user settings if needed

2. **Install New Version**
   - Close AutoCAD completely
   - Replace plugin files with new version
   - Restart AutoCAD

3. **Verify Update**
   - Check version information in About dialog
   - Test critical functionality
   - Restore user settings if needed

### Version Migration

#### Upgrading to .NET 8 Version
1. **Prerequisites**
   - Upgrade to AutoCAD 2025 or 2026
   - Backup existing data and settings

2. **Migration Process**
   - Uninstall old version
   - Install SPM_NET8_2025_2026
   - Import existing data files (fully compatible)
   - Reconfigure settings as needed

3. **Benefits**
   - 25% performance improvement
   - Better memory management
   - Enhanced features and stability

### Maintenance Tasks
- **Regular Cleanup**: Clear temporary files and history
- **Settings Backup**: Export settings before major updates
- **Performance Monitoring**: Monitor memory usage with large datasets
- **License Validation**: Ensure license remains valid

## Uninstallation

### Remove Plugin (All Versions)
1. **From Startup Suite**
   - Type `APPLOAD` in AutoCAD
   - Click "Startup Suite"
   - Select SPM plugin and click "Remove"

2. **Manual Removal**
   - Close AutoCAD completely
   - Delete plugin files from installation folder
   - Remove registry entries (if used Method 3)

3. **Clean Uninstall**
   - Clear AutoCAD user settings (optional)
   - Remove any custom configuration files
   - Restart AutoCAD to verify removal

## Support and Resources

### Getting Help
- **Documentation**: Complete guides in this repository
- **Version Compatibility**: Ensure correct version selection
- **Error Codes**: Reference troubleshooting guides
- **Technical Support**: Contact with system information

### System Information for Support
When contacting support, provide:
- SPM version and AutoCAD version
- Windows version and architecture
- Error messages or symptoms
- System specifications (RAM, CPU, etc.)
- Installation method used

---

**Installation Complete!**

Your Survey Points Manager plugin is now ready to use. Refer to the USER_GUIDE.md for detailed usage instructions and the README.md for technical information.

**Choose the right version for your AutoCAD installation and enjoy enhanced survey data management capabilities!**
