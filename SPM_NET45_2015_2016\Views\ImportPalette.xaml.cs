﻿using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using SPM_NET45_2015_2016.Helpers;
using SPM_NET45_2015_2016.Managers;
using SPM_NET45_2015_2016.Models;
using SPM_NET45_2015_2016.Services;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Data;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Media;
using System.Windows.Media.Animation;


namespace SPM_NET45_2015_2016.Views
{
    /// <summary>
    /// A UserControl for importing survey points from various file formats.
    /// This control provides functionality for importing and processing point data.
    /// </summary>
    public partial class ImportPalette : UserControl
    {
        private readonly ImportViewModel _viewModel;
        private ICollectionView _pointsView; // WPF view with filter
        private string _selectedFilePath;
        // Holds the auto-detected variant ("GSI 8" or "GSI 16")
        private string _autoDetectedGsiVariant = "GSI 16";

        public ImportPalette()
        {
            InitializeComponent();
            _viewModel = new ImportViewModel();
            DataContext = _viewModel;


            // wrap the ObservableCollection in a filterable view
            _pointsView = CollectionViewSource.GetDefaultView(_viewModel.Points);
            _pointsView.Filter = FilterPoints;

            // Set up event handlers for point collection changes
            _viewModel.Points.CollectionChanged += Points_CollectionChanged;

            // Set up keyboard event handlers for the DataGrid
            PointsDataGrid.PreviewKeyDown += PointsDataGrid_PreviewKeyDown;
        }

        #region AutoCAD Properties
        /// <summary>
        /// Gets the currently active AutoCAD document.
        /// </summary>
        private Document ActiveDocument => Autodesk.AutoCAD.ApplicationServices.Application.DocumentManager.MdiActiveDocument;

        /// <summary>
        /// Gets the database of the currently active AutoCAD document.
        /// </summary>
        private Database ActiveDatabase => ActiveDocument?.Database;

        /// <summary>
        /// Gets the editor of the currently active AutoCAD document.
        /// </summary>
        private Editor ActiveEditor => ActiveDocument?.Editor;
        #endregion


        #region Point Counter Management

        /// <summary>
        /// Event handler for when the Points collection changes
        /// </summary>
        private void Points_CollectionChanged(object sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            // Update the point counter in the view model
            _viewModel.UpdatePointCounter();
        }

        /// <summary>
        /// Event handler for keyboard events on the DataGrid to handle deletions
        /// </summary>
        private void PointsDataGrid_PreviewKeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            if (e.Key == System.Windows.Input.Key.Delete)
            {
                // Get selected items
                var selectedItems = PointsDataGrid.SelectedItems;
                if (selectedItems.Count > 0)
                {
                    var itemsToRemove = new List<SurveyPoint>();

                    // Create a list of items to remove (can't modify collection during enumeration)
                    foreach (SurveyPoint point in selectedItems)
                    {
                        itemsToRemove.Add(point);
                    }

                    // Add to undo stack before removing
                    _viewModel.AddToUndoStack(new List<SurveyPoint>(_viewModel.Points));

                    // Remove the items
                    foreach (var point in itemsToRemove)
                    {
                        _viewModel.Points.Remove(point);
                    }
                }
            }
        }

        #endregion


        #region File Import Handling

        /// <summary>
        /// Handles the import button click event, allowing user to select a file for import.
        /// </summary>
        private void btnImportFile_Click(object sender, RoutedEventArgs e)
        {
            var dlg = new Microsoft.Win32.OpenFileDialog
            {
                Filter = "CSV files (*.csv)|*.csv|TXT files (*.txt)|*.txt|" +
                        "KML files (*.kml)|*.kml|IDX files (*.idx)|*.idx|" +
                        "SDR files (*.sdr)|*.sdr|GSI files (*.gsi)|*.gsi",
                Title = "Select a file to import"
            };

            if (dlg.ShowDialog() == true)
            {
                _selectedFilePath = dlg.FileName;

                txtSelectedFileName.Text = System.IO.Path.GetFileName(_selectedFilePath);
                txtSelectedFileName.ToolTip = _selectedFilePath;

                string extension = System.IO.Path.GetExtension(_selectedFilePath).ToLower();

                // Hide SDRInfoButton when a new file is selected
                SDRInfoButton.Visibility = System.Windows.Visibility.Collapsed;

                IDXInfoButton.Visibility = System.Windows.Visibility.Collapsed;

                ToggleSettingsVisibility(extension);

                // Reset all formats to normal style
                ResetAllFormatStyles();

                // Apply style to the selected format
                switch (extension)
                {
                    case ".gsi":
                        AutoDetectGsiVariant();
                        HighlightFormat(runGsi);
                        break;
                    case ".csv":
                        CSVHelper.DetectCSVFormat(_selectedFilePath, CSVFormatComboBox);
                        HighlightFormat(runCsv);
                        break;
                    case ".txt":
                        TXTHelper.DetectTXTFormat(_selectedFilePath, CSVFormatComboBox);
                        HighlightFormat(runTxt);
                        break;
                    case ".kml":
                        HighlightFormat(runKml);
                        break;
                    case ".idx":
                        HighlightFormat(runIdx);
                        break;
                    case ".sdr":
                        HighlightFormat(runSdr);
                        break;
                }


                // Enable button and start animation
                btnDisplayPoints.IsEnabled = true;

                // Use Dispatcher to ensure UI is updated before animation starts
                btnDisplayPoints.Dispatcher.BeginInvoke(new Action(() =>
                {
                    StartBlinkingAnimation(btnDisplayPoints);
                }), System.Windows.Threading.DispatcherPriority.Render);
            }
        }


        /// <summary>
        /// Shows or hides specific setting panels based on the selected file format.
        /// </summary>
        private void ToggleSettingsVisibility(string extension)
        {
            pnlEmptyFileSettings.Visibility = System.Windows.Visibility.Collapsed;
            pnlCsvFileSettings.Visibility = System.Windows.Visibility.Collapsed;
            pnlKmlFileSettings.Visibility = System.Windows.Visibility.Collapsed;
            pnlGsiFileSettings.Visibility = System.Windows.Visibility.Collapsed;

            switch (extension)
            {
                case ".csv":
                case ".txt":
                    pnlCsvFileSettings.Visibility = System.Windows.Visibility.Visible;
                    break;
                case ".kml":
                    pnlKmlFileSettings.Visibility = System.Windows.Visibility.Visible;
                    break;
                case ".gsi":
                    pnlGsiFileSettings.Visibility = System.Windows.Visibility.Visible;
                    break;
                default:
                    pnlEmptyFileSettings.Visibility = System.Windows.Visibility.Visible;
                    break;
            }
        }

        #endregion


        #region Import Processing

        /// <summary>
        /// Handles the display button click event and routes to appropriate import method.
        /// </summary>
        private void btnDisplayPoints_Click(object sender, RoutedEventArgs e)
        {
            // Stop blinking effect
            StopBlinkingAnimation(btnDisplayPoints);

            // Add current points to undo stack before importing new ones
            if (_viewModel.Points.Count > 0)
            {
                _viewModel.AddToUndoStack(new List<SurveyPoint>(_viewModel.Points));
            }

            string fileExtension = System.IO.Path.GetExtension(_selectedFilePath).ToLower();
            switch (fileExtension)
            {
                case ".csv":
                    ImportCSVPoints();
                    break;
                case ".txt":
                    ImportTXTPoints();
                    break;
                case ".kml":
                    ImportKMLPoints();
                    break;
                case ".sdr":
                    ImportSDRPoints();
                    break;
                case ".idx":
                    ImportIDXPoints();
                    break;
                case ".gsi":
                    ImportGSIPoints();
                    break;
                default:
                    MessageBox.Show("Unsupported file type selected.");
                    break;
            }
        }

        private Storyboard _blinkingStoryboard;

        // Fixed Animation Functions
        private void StartBlinkingAnimation(Button button)
        {
            // Stop any existing animation
            StopBlinkingAnimation(button);

            // Set border properties directly on button
            button.BorderThickness = new Thickness(2);
            button.BorderBrush = new SolidColorBrush(Colors.Red);

            // Create a simple color animation
            _blinkingStoryboard = new Storyboard();

            // Create a thick border
            ThicknessAnimation borderAnimation = new ThicknessAnimation
            {
                From = new Thickness(2),
                To = new Thickness(0),
                Duration = new Duration(TimeSpan.FromSeconds(0.5)),
                AutoReverse = true,
                RepeatBehavior = RepeatBehavior.Forever
            };

            // Attach the animation directly to the button's BorderThickness
            Storyboard.SetTarget(borderAnimation, button);
            Storyboard.SetTargetProperty(borderAnimation, new PropertyPath(Button.BorderThicknessProperty));

            _blinkingStoryboard.Children.Add(borderAnimation);
            _blinkingStoryboard.Begin();
        }

        private void StopBlinkingAnimation(Button button)
        {
            if (_blinkingStoryboard != null)
            {
                _blinkingStoryboard.Stop();
                _blinkingStoryboard = null;

                // Reset the button properties
                button.BorderThickness = new Thickness(0);
                button.BorderBrush = null;
            }
        }

        /// <summary>
        /// Processes CSV file import with specified options.
        /// </summary>
        /*
        private void ImportCSVPoints()
        {
            // Adjust DataGrid columns first if needed (handled in UI code)
            AdjustDataGridColumns();

            // Get the selected CSV format tag from the ComboBox.
            string formatTag = (CSVFormatComboBox.SelectedItem as ComboBoxItem)?.Tag?.ToString() ?? "ENZ";

            var importedPoints = ImportService.ProcessCSVPoints(_selectedFilePath, formatTag);

            _viewModel.Points.Clear();
            foreach (var pt in importedPoints)
            {
                _viewModel.Points.Add(pt);
            }

            HistoryManager.Instance.AddRecord(System.IO.Path.GetFileName(_selectedFilePath), "Import", "CSV", importedPoints.Count);
            MessageBox.Show($"Successfully imported {importedPoints.Count} points.");
        }
        */

        private void ImportCSVPoints()
        {
            AdjustDataGridColumns();

            string formatTag = (CSVFormatComboBox.SelectedItem as ComboBoxItem)?.Tag?.ToString();
            if (string.IsNullOrEmpty(formatTag))
            {
                MessageBox.Show("Please select a valid CSV format.", "Import Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var importedPoints = ImportService.ProcessCSVPoints(_selectedFilePath, formatTag);
            if (importedPoints == null || importedPoints.Count == 0)
            {
                MessageBox.Show("No valid points were imported. Check the file format and content.", "Import Warning", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            _viewModel.Points.Clear();
            foreach (var pt in importedPoints)
            {
                _viewModel.Points.Add(pt);
            }

            HistoryManager.Instance.AddRecord(System.IO.Path.GetFileName(_selectedFilePath), "Import", "CSV", importedPoints.Count);
            MessageBox.Show($"Successfully imported {importedPoints.Count} points.");
        }

        /// <summary>
        /// Processes TXT file import with specified options.
        /// </summary>
       /*
        private void ImportTXTPoints()
        {

            // Adjust DataGrid columns first if needed (handled in UI code)
            AdjustDataGridColumns();

            // Assume CSVFormatComboBox is used to select the format even for TXT import.
            string formatTag = (CSVFormatComboBox.SelectedItem as ComboBoxItem)?.Tag?.ToString() ?? "ENZ";

            try
            {
                var importedPoints = ImportService.ProcessTXTPoints(_selectedFilePath, formatTag);

                _viewModel.Points.Clear();
                foreach (var pt in importedPoints)
                {
                    _viewModel.Points.Add(pt);
                }

                HistoryManager.Instance.AddRecord(System.IO.Path.GetFileName(_selectedFilePath), "Import", "TXT", importedPoints.Count);
                MessageBox.Show($"Successfully imported {importedPoints.Count} points.");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error importing TXT file: {ex.Message}");
            }
        }
        */

        private void ImportTXTPoints()
        {
            AdjustDataGridColumns();

            string formatTag = (CSVFormatComboBox.SelectedItem as ComboBoxItem)?.Tag?.ToString();
            if (string.IsNullOrEmpty(formatTag))
            {
                MessageBox.Show("Please select a valid TXT format.", "Import Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                var importedPoints = ImportService.ProcessTXTPoints(_selectedFilePath, formatTag);
                if (importedPoints == null || importedPoints.Count == 0)
                {
                    MessageBox.Show("No valid points were imported. Check the file format and content.", "Import Warning", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                _viewModel.Points.Clear();
                foreach (var pt in importedPoints)
                {
                    _viewModel.Points.Add(pt);
                }

                HistoryManager.Instance.AddRecord(System.IO.Path.GetFileName(_selectedFilePath), "Import", "TXT", importedPoints.Count);
                MessageBox.Show($"Successfully imported {importedPoints.Count} points.");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error importing TXT file: {ex.Message}", "Import Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Processes KML file import with specified coordinate system settings.
        /// </summary>
        private void ImportKMLPoints()
        {
            // Reset the DataGrid columns to match the SDR file format
            AdjustDataGridColumnsForSDR();

            string selectedHemisphere = (cboKmlHemisphere.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "Not Selected";
            string selectedZone = (cboKmlUtmZone.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "Not Selected";

            var points = ImportService.ProcessKMLPoints(_selectedFilePath, selectedHemisphere, selectedZone);

            if (points.Count > 0)
            {
                _viewModel.Points.Clear();
                foreach (var point in points)
                {
                    _viewModel.Points.Add(point);
                }

                string fileName = System.IO.Path.GetFileName(_selectedFilePath);
                HistoryManager.Instance.AddRecord(fileName, "Import", "KML", points.Count);
                MessageBox.Show($"Successfully imported {points.Count} points.");
            }
        }


        private SDRFileInfo _currentSDRInfo; // Stores SDR metadata

        /// <summary>
        /// Handles SDR file import by processing file and updating UI.
        /// </summary>
        private void ImportSDRPoints()
        {
            // Reset the DataGrid columns to match the SDR file format
            AdjustDataGridColumnsForSDR();

            var sdrData = ImportService.ProcessSDRFile(_selectedFilePath);

            _viewModel.Points.Clear();
            foreach (var pt in sdrData.SurveyPoints)
            {
                _viewModel.Points.Add(pt);
            }

            // Store SDR metadata
            _currentSDRInfo = sdrData.FileInfo;

            // Update SDR info button visibility  TODO: OTHER FORMATS VISIBLITY PROBLEM
            SDRInfoButton.Visibility = (_currentSDRInfo != null &&
                                       (!string.IsNullOrWhiteSpace(_currentSDRInfo.JobName) ||
                                        !string.IsNullOrWhiteSpace(_currentSDRInfo.InstrumentModel) ||
                                        _currentSDRInfo.TargetHeight.HasValue ||
                                        _currentSDRInfo.Scale.HasValue))
                                       ? System.Windows.Visibility.Visible : System.Windows.Visibility.Collapsed;

            // Add to history
            string fileName = System.IO.Path.GetFileName(_selectedFilePath);
            HistoryManager.Instance.AddRecord(fileName, "Import", "SDR", sdrData.SurveyPoints.Count);

            MessageBox.Show($"Successfully imported {sdrData.SurveyPoints.Count} points.");
        }

        /// <summary>
        /// Handles IDX file import by invoking ProcessIDXPoints.
        /// </summary>
        private IDXFileInfo _currentIDXInfo;

        private void ImportIDXPoints()
        {
            AdjustDataGridColumnsForSDR();

            try
            {
                var idxData = ImportService.ProcessIDXFile(_selectedFilePath);

                _viewModel.Points.Clear();
                foreach (var pt in idxData.SurveyPoints)
                {
                    _viewModel.Points.Add(pt);
                }

                // Store IDX metadata.
                _currentIDXInfo = idxData.FileInfo;

                // Show IDXInfoButton only if metadata exists.
                IDXInfoButton.Visibility = _currentIDXInfo != null &&
                                            (!string.IsNullOrWhiteSpace(_currentIDXInfo.InstrumentModel) ||
                                             !string.IsNullOrWhiteSpace(_currentIDXInfo.LinearUnit) ||
                                             !string.IsNullOrWhiteSpace(_currentIDXInfo.ProjectName) ||
                                             !string.IsNullOrWhiteSpace(_currentIDXInfo.Operator) ||
                                             !string.IsNullOrWhiteSpace(_currentIDXInfo.CreationDate))
                                            ? System.Windows.Visibility.Visible : System.Windows.Visibility.Collapsed;

                HistoryManager.Instance.AddRecord(System.IO.Path.GetFileName(_selectedFilePath), "Import", "IDX", idxData.SurveyPoints.Count);
                MessageBox.Show($"Successfully imported {idxData.SurveyPoints.Count} points.");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error importing IDX file: {ex.Message}");
            }
        }



        /// <summary>
        /// Handles GSI file import by invoking ProcessGSIPoints.
        /// </summary>
        private void ImportGSIPoints()
        {
            AdjustDataGridColumnsForSDR();

            // Determine variant: if cboGsiFormatVariant is set, use it; otherwise, fall back on auto-detection.
            bool isGsi16 = false;
            if (cboGsiFormatVariant?.SelectedItem is ComboBoxItem selectedItem)
            {
                string selectedVariant = selectedItem.Content.ToString();
                isGsi16 = selectedVariant.Equals("GSI 16", StringComparison.OrdinalIgnoreCase);
            }
            else
            {
                isGsi16 = _autoDetectedGsiVariant.Equals("GSI 16", StringComparison.OrdinalIgnoreCase);
            }

            try
            {
                var points = ImportService.ProcessGSIPoints(_selectedFilePath, isGsi16);

                _viewModel.Points.Clear();
                foreach (var point in points)
                {
                    _viewModel.Points.Add(point);
                }

                MessageBox.Show($"Successfully imported {points.Count} points.");
            }
            catch (Exception ex)
            {
                MessageBox.Show("Error importing GSI file: " + ex.Message);
            }
        }

        /// <summary>
        /// Auto-detects the GSI variant as soon as the file is imported,
        /// updates the ComboBox and label accordingly.
        /// </summary>
        private void AutoDetectGsiVariant()
        {
            try
            {
                if (string.IsNullOrEmpty(_selectedFilePath))
                {
                    return;
                }

                bool isGsi16 = false;

                using (StreamReader reader = new StreamReader(_selectedFilePath))
                {
                    string line;
                    while ((line = reader.ReadLine()) != null)
                    {
                        if (string.IsNullOrWhiteSpace(line))
                        {
                            continue;
                        }

                        // Split the line into words using whitespace.
                        string[] words = line.Split((char[])null, StringSplitOptions.RemoveEmptyEntries);
                        if (words.Length == 0)
                        {
                            continue;
                        }

                        // Auto-detect: if the first word's length > 16, assume GSI 16.
                        isGsi16 = words[0].Length > 16;
                        break;
                    }
                }

                _autoDetectedGsiVariant = isGsi16 ? "GSI 16" : "GSI 8";

                // Update the ComboBox to reflect the auto-detected variant.
                if (cboGsiFormatVariant != null)
                {
                    var autoItem = cboGsiFormatVariant.Items
                        .Cast<ComboBoxItem>()
                        .FirstOrDefault(x => x.Content.ToString().Equals(_autoDetectedGsiVariant, StringComparison.OrdinalIgnoreCase));
                    if (autoItem != null)
                    {
                        cboGsiFormatVariant.SelectedItem = autoItem;
                    }
                }

                // Update the label.
                if (txtGsiSelectionInfo != null)
                {
                    txtGsiSelectionInfo.Text = $"Auto detected: {_autoDetectedGsiVariant}";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Error during auto-detection: " + ex.Message);
            }
        }

        /// <summary>
        /// Handles manual override of the GSI variant selection.
        /// Updates the label to indicate manual selection along with the auto-detected variant.
        /// </summary>
        private void GSIFileVariant_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (cboGsiFormatVariant?.SelectedItem is ComboBoxItem item)
            {
                string selected = item.Content.ToString();
                if (txtGsiSelectionInfo != null)
                {
                    if (!selected.Equals(_autoDetectedGsiVariant, StringComparison.OrdinalIgnoreCase))
                    {
                        txtGsiSelectionInfo.Text = $"Manual selected: {selected} (Auto detected: {_autoDetectedGsiVariant})";
                    }
                    else
                    {
                        txtGsiSelectionInfo.Text = $"Auto detected: {selected}";
                    }
                }
            }
        }


        #endregion


        #region Helpers Methods

        private void AdjustDataGridColumns()
        {
            if (CSVFormatComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                string formatTag = selectedItem.Tag?.ToString();
                PointsDataGrid.Columns.Clear();

                switch (formatTag)
                {
                    case "PEN":
                        AddColumn("PointNumber", "PN");
                        AddColumn("Easting", "Easting");
                        AddColumn("Northing", "Northing");
                        break;

                    case "PENZ":
                        AddColumn("PointNumber", "PN");
                        AddColumn("Easting", "Easting");
                        AddColumn("Northing", "Northing");
                        AddColumn("Elevation", "Elevation");
                        break;

                    case "PENZD":
                        AddColumn("PointNumber", "PN");
                        AddColumn("Easting", "Easting");
                        AddColumn("Northing", "Northing");
                        AddColumn("Elevation", "Elevation");
                        AddColumn("Description", "Description");
                        break;

                    case "PNE":
                        AddColumn("PointNumber", "PN");
                        AddColumn("Northing", "Northing");
                        AddColumn("Easting", "Easting");
                        break;

                    case "PNEZ":
                        AddColumn("PointNumber", "PN");
                        AddColumn("Northing", "Northing");
                        AddColumn("Easting", "Easting");
                        AddColumn("Elevation", "Elevation");
                        break;

                    case "PNEZD":
                        AddColumn("PointNumber", "PN");
                        AddColumn("Northing", "Northing");
                        AddColumn("Easting", "Easting");
                        AddColumn("Elevation", "Elevation");
                        AddColumn("Description", "Description");
                        break;

                    case "EN":
                        AddColumn("Easting", "Easting");
                        AddColumn("Northing", "Northing");
                        break;

                    case "ENZ":
                        AddColumn("Easting", "Easting");
                        AddColumn("Northing", "Northing");
                        AddColumn("Elevation", "Elevation");
                        break;

                    case "ENZD":
                        AddColumn("Easting", "Easting");
                        AddColumn("Northing", "Northing");
                        AddColumn("Elevation", "Elevation");
                        AddColumn("Description", "Description");
                        break;

                    case "NE":
                        AddColumn("Northing", "Northing");
                        AddColumn("Easting", "Easting");
                        break;

                    case "NEZ":
                        AddColumn("Northing", "Northing");
                        AddColumn("Easting", "Easting");
                        AddColumn("Elevation", "Elevation");
                        break;

                    case "NEZD":
                        AddColumn("Northing", "Northing");
                        AddColumn("Easting", "Easting");
                        AddColumn("Elevation", "Elevation");
                        AddColumn("Description", "Description");
                        break;

                    default:
                        // Handle case where no valid format is selected (e.g., "Select format...")
                        PointsDataGrid.Columns.Clear();
                        break;
                }
            }
        }

        private void AdjustDataGridColumnsForSDR()
        {
            PointsDataGrid.Columns.Clear();
            // Add columns for SDR points (you may adjust the width and styles as needed)
            AddColumn("PointNumber", "PN");
            AddColumn("Easting", "Easting");
            AddColumn("Northing", "Northing");
            AddColumn("Elevation", "Elevation");
            AddColumn("Description", "Description");
        }

        private void AddColumn(string bindingPath, string header)
        {
            DataGridTextColumn column = new DataGridTextColumn
            {
                Header = header,
                Binding = new Binding(bindingPath),
                ElementStyle = (Style)FindResource("DataGridTextCellStyle"),
                HeaderStyle = (Style)FindResource("DataGridHeaderCellStyle"),
                Width = new DataGridLength(1, DataGridLengthUnitType.Star)
            };

            PointsDataGrid.Columns.Add(column);
        }

        private void ResetAllFormatStyles()
        {
            // Reset all Run elements to normal style
            foreach (Run run in new[] { runCsv, runTxt, runKml, runIdx, runSdr, runGsi })
            {
                run.FontWeight = FontWeights.Normal;
                run.Foreground = new SolidColorBrush(Color.FromRgb(0x68, 0x7A, 0x99));
            }
        }

        private void HighlightFormat(Run run)
        {
            run.FontWeight = FontWeights.Bold;
            run.Foreground = new SolidColorBrush(Color.FromRgb(104, 122, 153));
        }


        /// <summary>
        /// Shows the SDR information popup when SDRInfoButton is clicked.
        /// </summary>
        private void SDRInfoButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentSDRInfo == null)
            {
                MessageBox.Show("No SDR file information available.");
                return;
            }

            // Update UI elements inside the popup
            txtJobName.Text = string.IsNullOrWhiteSpace(_currentSDRInfo.JobName) ? "N/A" : _currentSDRInfo.JobName;
            txtInstrumentModel.Text = string.IsNullOrWhiteSpace(_currentSDRInfo.InstrumentModel) ? "N/A" :
                                      _currentSDRInfo.InstrumentModel.Split(' ')[0]; // Extract only first word
            txtTargetHeight.Text = _currentSDRInfo.TargetHeight.HasValue ? _currentSDRInfo.TargetHeight.Value.ToString("F3") : "N/A";
            txtScale.Text = _currentSDRInfo.Scale.HasValue ? _currentSDRInfo.Scale.Value.ToString("F3") : "N/A";

            // Show the popup
            SDRPopup.IsOpen = true;
        }

        private void CloseSDRPopup_Click(object sender, RoutedEventArgs e)
        {
            SDRPopup.IsOpen = false;
        }

        // Called when the close button in the IDX popup is clicked.
        private void CloseIDXPopup_Click(object sender, RoutedEventArgs e)
        {
            IDXPopup.IsOpen = false;
        }

        // Called when the IDX info button is clicked.
        private void IDXInfoButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentIDXInfo == null)
            {
                MessageBox.Show("No IDX file information available.", "Info", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            // Update text fields with IDX metadata.
            txtIDXInstrumentModel.Text = string.IsNullOrWhiteSpace(_currentIDXInfo.InstrumentModel)
                ? "N/A"
                : _currentIDXInfo.InstrumentModel.Split(' ')[0]; // Only the first word
            txtIDXLinearUnit.Text = string.IsNullOrWhiteSpace(_currentIDXInfo.LinearUnit)
                ? "N/A"
                : _currentIDXInfo.LinearUnit;
            txtIDXProjectName.Text = string.IsNullOrWhiteSpace(_currentIDXInfo.ProjectName)
                ? "N/A"
                : _currentIDXInfo.ProjectName;
            txtIDXOperator.Text = string.IsNullOrWhiteSpace(_currentIDXInfo.Operator)
                ? "N/A"
                : _currentIDXInfo.Operator;
            txtIDXCreationDate.Text = string.IsNullOrWhiteSpace(_currentIDXInfo.CreationDate)
                ? "N/A"
                : _currentIDXInfo.CreationDate;

            // Open the popup.
            IDXPopup.IsOpen = true;
        }

        #endregion


        #region Drawing Functions

        /// <summary>
        /// Handles drawing the points table in the AutoCAD model space.
        /// Retrieves both the header texts and point data directly from the DataGrid.
        /// </summary>
        /*
        private void DrawPointsTableBTN_Click(object sender, RoutedEventArgs e)
        {
            // Build a list of header strings from the DataGrid's columns.
            var headers = new List<string>();
            foreach (DataGridColumn column in PointsDataGrid.Columns)
            {
                if (column.Header != null)
                {
                    headers.Add(column.Header.ToString());
                }
            }

            // Build a list of row strings from the DataGrid's items.
            var pointsList = new List<string>();
            foreach (var item in PointsDataGrid.Items)
            {
                if (item is SurveyPoint point)
                {
                    // Format each SurveyPoint as a comma-separated string.
                    string rowData = $"{point.PointNumber}, {point.Easting}, {point.Northing}, {point.Elevation}, {point.Description}";
                    pointsList.Add(rowData);
                }
            }

            if (pointsList.Count == 0)
            {
                MessageBox.Show("No points to draw. Please import points first.");
                return;
            }

            // Create an instance of TableManager
            TableManager tableManager = new TableManager();

            // Call the method to create the AutoCAD table
            tableManager.CreateAutoCADTable(pointsList, headers, maxRowsPerTable: 10);
        }
        */



        /// <summary>
        /// Handles drawing the points table in the AutoCAD model space.
        /// Retrieves both the header texts and point data directly from the DataGrid, 
        /// ensuring only visible columns are included.
        /// </summary>
        private void AddTableToDrawingButton_Click(object sender, RoutedEventArgs e)
        {
            // Get visible columns in display order
            var visibleColumns = PointsDataGrid.Columns
                .OrderBy(col => col.DisplayIndex)
                .ToList();

            // Build headers from visible columns
            var headers = visibleColumns.Select(col => col.Header.ToString()).ToList();

            // Build pointsList with only the visible fields, in the correct order
            var pointsList = new List<string>();
            foreach (var item in PointsDataGrid.Items)
            {
                if (item is SurveyPoint point)
                {
                    var rowData = new List<string>();
                    foreach (var col in visibleColumns)
                    {
                        if (col is DataGridTextColumn textColumn)
                        {
                            var binding = textColumn.Binding as Binding;
                            if (binding != null)
                            {
                                string propertyName = binding.Path.Path;
                                var value = point.GetType().GetProperty(propertyName)?.GetValue(point)?.ToString() ?? "";
                                rowData.Add(value);
                            }
                        }
                    }
                    pointsList.Add(string.Join(", ", rowData));
                }
            }

            if (pointsList.Count == 0)
            {
                MessageBox.Show("No points to draw. Please import points first.");
                return;
            }

            // Create an instance of TableManager
            TableManager tableManager = new TableManager();

            // Call the method to create the AutoCAD table
            tableManager.CreateAutoCADTable(pointsList, headers, maxRowsPerTable: Properties.Settings.Default.MaxRowPerTable);
        }

        /// <summary>
        /// Handles placing points in the AutoCAD model space.
        /// Retrieves point data from the DataGrid and creates point and text entities in AutoCAD.
        /// </summary>
        private void AddPointsToDrawingButton_Click(object sender, RoutedEventArgs e)
        {
            // Check if the DataGrid contains any items.
            if (PointsDataGrid.Items.Count == 0)
            {
                MessageBox.Show("No points to import. The DataGrid is empty.", "No Data", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            int pointsImported = 0;

            // Lock the document and start a transaction.
            using (DocumentLock docLock = ActiveDocument.LockDocument())
            {
                using (Transaction trans = ActiveDatabase.TransactionManager.StartTransaction())
                {
                    // Always create layers based on the user settings.
                    Managers.LayerManager.CreateLayer(ActiveDatabase, trans, Properties.Settings.Default.PointLayer, Properties.Settings.Default.PointLayerColor);
                    Managers.LayerManager.CreateLayer(ActiveDatabase, trans, Properties.Settings.Default.PointNumberLayer, Properties.Settings.Default.PointNumberLayerColor);

                    // Set the point style and size using user settings.
                    Helpers.DrawingHelper.SetPointStyle(Properties.Settings.Default.PointDisplayImage, Properties.Settings.Default.PointSize);

                    // Iterate through the DataGrid items.
                    foreach (var item in PointsDataGrid.Items)
                    {
                        // We expect each item to be a SurveyPoint.
                        if (item is SurveyPoint point)
                        {
                            double easting = point.Easting;
                            double northing = point.Northing;
                            double elevation = point.Elevation;
                            string pointNumber = point.PointNumber ?? "";

                            // Create a 3D point.
                            Point3d pt = new Point3d(easting, northing, elevation);

                            // Draw the point on the "APM_Point" layer.
                            Services.DrawingService.DrawPoint(ActiveDocument, pt, Properties.Settings.Default.PointLayer);

                            // Draw the point number as text on the "APM_PointNumber" layer.
                            Services.DrawingService.DrawPointNumber(ActiveDocument, pt, point.PointNumber, Properties.Settings.Default.PointNumberTextHeight, Properties.Settings.Default.PointNumberLayer);

                            pointsImported++;
                        }
                    }

                    trans.Commit();
                }
            }



            // Zoom to the first point if available.
            if (PointsDataGrid.Items.Count > 0)
            {
                int count = PointsDataGrid.Items.Count;
                int midIndex = (count % 2 == 0) ? (count / 2) - 1 : count / 2;
                if (PointsDataGrid.Items[midIndex] is SurveyPoint middlePoint)
                {
                    // Create a Point3d using the middlePoint point's coordinates.
                    Point3d centerPoint = new Point3d(middlePoint.Easting, middlePoint.Northing, middlePoint.Elevation);
                    // Use the documented Zoom method.
                    Services.DrawingService.Zoom(new Point3d(), new Point3d(), centerPoint, 0.5);
                }
            }



            MessageBox.Show($"Done, {pointsImported} points imported.", "Import Complete", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        #endregion


        #region Undo/Redo Functions

        /// <summary>
        /// Handles the delete all points button click event
        /// </summary>
        private void DeleteAllPointsBTN_Click(object sender, RoutedEventArgs e)
        {
            if (_viewModel.Points.Count > 0)
            {
                // Store current state for undo
                _viewModel.AddToUndoStack(new List<SurveyPoint>(_viewModel.Points));

                // Clear all points
                _viewModel.Points.Clear();
            }
        }

        /// <summary>
        /// Handles the undo button click event
        /// </summary>
        private void UndoBTN_Click(object sender, RoutedEventArgs e)
        {
            if (_viewModel.CanUndo())
            {
                // Store current state for redo
                _viewModel.AddToRedoStack(new List<SurveyPoint>(_viewModel.Points));

                // Restore points from undo stack
                var previousPoints = _viewModel.Undo();

                _viewModel.Points.Clear();
                foreach (var point in previousPoints)
                {
                    _viewModel.Points.Add(point);
                }
            }
        }

        /// <summary>
        /// Handles the redo button click event
        /// </summary>
        private void RedoBTN_Click(object sender, RoutedEventArgs e)
        {
            if (_viewModel.CanRedo())
            {
                // Store current state for undo
                _viewModel.AddToUndoStack(new List<SurveyPoint>(_viewModel.Points));

                // Restore points from redo stack
                var nextPoints = _viewModel.Redo();

                _viewModel.Points.Clear();
                foreach (var point in nextPoints)
                {
                    _viewModel.Points.Add(point);
                }
            }
        }

        #endregion


        #region Export Points

        private void ExportOptionsButton_Click(object sender, RoutedEventArgs e)
        {
            // Show the popup
            ExportDialogPopup.IsOpen = true;
        }

        // Event handler for the cancel button
        private void CloseExportPopupButton_Click(object sender, RoutedEventArgs e)
        {
            // Close the popup without exporting
            ExportDialogPopup.IsOpen = false;
        }

        private void ExportFileFormatComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (ExportFileFormatComboBox.SelectedItem is ComboBoxItem selectedItem && selectedItem.Tag != null)
            {
                string selectedExtension = selectedItem.Tag.ToString();
                ToggleExportSettingsVisibility(selectedExtension);
            }
        }

        /// <summary>
        /// Shows or hides specific setting panels based on the selected export file format.
        /// </summary>
        private void ToggleExportSettingsVisibility(string extension)
        {
            if (ExEmptySettings != null)
            {
                ExEmptySettings.Visibility = System.Windows.Visibility.Collapsed;
            }

            if (ExCSVSettings != null)
            {
                ExCSVSettings.Visibility = System.Windows.Visibility.Collapsed;
            }

            if (ExKMLSettings != null)
            {
                ExKMLSettings.Visibility = System.Windows.Visibility.Collapsed;
            }

            if (ExGSISettings != null)
            {
                ExGSISettings.Visibility = System.Windows.Visibility.Collapsed;
            }

            switch (extension.ToLower())
            {
                case ".csv":
                case ".txt":
                    if (ExCSVSettings != null)
                    {
                        ExCSVSettings.Visibility = System.Windows.Visibility.Visible;
                    }

                    break;
                case ".kml":
                    if (ExKMLSettings != null)
                    {
                        ExKMLSettings.Visibility = System.Windows.Visibility.Visible;
                    }

                    break;
                case ".gsi":
                    if (ExGSISettings != null)
                    {
                        ExGSISettings.Visibility = System.Windows.Visibility.Visible;
                    }

                    break;
                default:
                    if (ExEmptySettings != null)
                    {
                        ExEmptySettings.Visibility = System.Windows.Visibility.Visible;
                    }

                    break;
            }
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            if (ExportFileFormatComboBox.SelectedItem is ComboBoxItem selectedItem && selectedItem.Tag != null)
            {
                string fileFormat = selectedItem.Tag.ToString().ToLower();
                HandleExport(fileFormat);
            }
            else
            {
                System.Windows.MessageBox.Show("Please select an export file format.", "Export Error", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void HandleExport(string fileFormat)
        {
            switch (fileFormat)
            {
                case ".csv":
                    ExportCSVPoints();
                    break;
                case ".txt":
                    ExportTXTPoints();
                    break;
                case ".kml":
                    ExportKMLPoints();
                    break;
                case ".sdr":
                    ExportSDRPoints();
                    break;
                case ".idx":
                    ExportIDXPoints();
                    break;
                case ".gsi":
                case ".gsi-8":
                case ".gsi-16":
                    ExportGSIPoints();
                    break;
                default:
                    System.Windows.MessageBox.Show("Unsupported file type selected.", "Export Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    break;
            }
        }

        private void ExportTXTPoints()
        {
            bool includeHeaders = FileHasHeadersCheckBox.IsChecked == true;
            bool includePointNumber = PointsHaveNumberCheckBox.IsChecked == true;
            bool isXYOrder = XYRadioButton.IsChecked == true;

            Services.ExportService.ExportTXTPoints(
                _viewModel.Points,
                includeHeaders,
                includePointNumber,
                isXYOrder);
        }

        private void ExportCSVPoints()
        {
            bool includeHeaders = FileHasHeadersCheckBox.IsChecked == true;
            bool includePointNumber = PointsHaveNumberCheckBox.IsChecked == true;
            bool isXYOrder = XYRadioButton.IsChecked == true;

            Services.ExportService.ExportCSVPoints(
                _viewModel.Points,
                includeHeaders,
                includePointNumber,
                isXYOrder);
        }

        private void ExportKMLPoints()
        {
            // Retrieve selected zone and hemisphere safely
            string selectedZone = (ZoneCB.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "Not Selected";
            int zone = int.TryParse(selectedZone, out int parsedZone) ? parsedZone : 0;
            string selectedHemisphere = (HemisphereCB.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "North";
            bool isNorthernHemisphere = selectedHemisphere == "North";

            Services.ExportService.ExportKMLPoints(
                _viewModel.Points,
                zone,
                isNorthernHemisphere);
        }

        private void ExportSDRPoints()
        {
            Services.ExportService.ExportSDRPoints(_viewModel.Points);
        }

        private void ExportIDXPoints()
        {
            Services.ExportService.ExportIDXPoints(_viewModel.Points);
        }

        private void ExportGSIPoints()
        {
            Services.ExportService.ExportGSIPoints(_viewModel.Points, GSIFormatCB.SelectedItem as ComboBoxItem);
        }
        #endregion


        #region Search Points
        /// <summary>
        /// Called on every change in the search box.
        /// Refreshes the view filter.
        /// </summary>
        private void SearchPointsTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            _pointsView.Refresh();
        }

        /// <summary>
        /// The actual predicate used by the CollectionView.
        /// Returns true if the point matches the search term or if no term specified.
        /// </summary>
        private bool FilterPoints(object obj)
        {
            if (obj is SurveyPoint pt)
            {
                var text = SearchPointsTextBox.Text?.Trim();
                if (string.IsNullOrEmpty(text))
                {
                    return true;
                }

                text = text.ToLowerInvariant();

                return pt.PointNumber?.ToLowerInvariant().Contains(text) == true
                    || pt.Description?.ToLowerInvariant().Contains(text) == true
                    || pt.Easting.ToString(CultureInfo.InvariantCulture).Contains(text)
                    || pt.Northing.ToString(CultureInfo.InvariantCulture).Contains(text)
                    || pt.Elevation.ToString(CultureInfo.InvariantCulture).Contains(text);
            }
            return false;
        }
        #endregion
    }

    /// <summary>
    /// ViewModel for managing the imported survey points collection.
    /// Contains properties and undo/redo functionality specific to the import process.
    /// </summary>
    public class ImportViewModel : INotifyPropertyChanged
    {
        private int _pointCounter;
        private readonly Stack<List<SurveyPoint>> _undoStack;
        private readonly Stack<List<SurveyPoint>> _redoStack;

        /// <summary>
        /// Gets the collection of imported survey points.
        /// </summary>
        public ObservableCollection<SurveyPoint> Points { get; }

        /// <summary>
        /// Gets or sets the point counter.
        /// </summary>
        public int PointCounter
        {
            get => _pointCounter;
            set
            {
                if (_pointCounter != value)
                {
                    _pointCounter = value;
                    OnPropertyChanged(nameof(PointCounter));
                }
            }
        }

        /// <summary>
        /// Initializes a new instance of the ImportViewModel class.
        /// </summary>
        public ImportViewModel()
        {
            Points = new ObservableCollection<SurveyPoint>();
            _undoStack = new Stack<List<SurveyPoint>>();
            _redoStack = new Stack<List<SurveyPoint>>();
            _pointCounter = Points.Count;

            // Update the point counter automatically when the Points collection changes.
            Points.CollectionChanged += (s, e) => UpdatePointCounter();
        }

        /// <summary>
        /// Updates the point counter based on the number of items in the Points collection.
        /// </summary>
        public void UpdatePointCounter()
        {
            PointCounter = Points.Count;
        }

        #region Undo/Redo Operations

        /// <summary>
        /// Adds the current state of points to the undo stack.
        /// </summary>
        /// <param name="points">The current list of points.</param>
        public void AddToUndoStack(List<SurveyPoint> points)
        {
            _undoStack.Push(points);
        }

        /// <summary>
        /// Adds the current state of points to the redo stack.
        /// </summary>
        /// <param name="points">The current list of points.</param>
        public void AddToRedoStack(List<SurveyPoint> points)
        {
            _redoStack.Push(points);
        }

        /// <summary>
        /// Restores the previous state from the undo stack.
        /// </summary>
        /// <returns>A list of SurveyPoint objects representing the previous state.</returns>
        public List<SurveyPoint> Undo()
        {
            if (_undoStack.Any())
            {
                return _undoStack.Pop();
            }
            return new List<SurveyPoint>();
        }

        /// <summary>
        /// Restores the next state from the redo stack.
        /// </summary>
        /// <returns>A list of SurveyPoint objects representing the next state.</returns>
        public List<SurveyPoint> Redo()
        {
            if (_redoStack.Any())
            {
                return _redoStack.Pop();
            }
            return new List<SurveyPoint>();
        }

        /// <summary>
        /// Determines if an undo operation can be performed.
        /// </summary>
        /// <returns>True if undo is possible; otherwise, false.</returns>
        public bool CanUndo() => _undoStack.Count > 0;

        /// <summary>
        /// Determines if a redo operation can be performed.
        /// </summary>
        /// <returns>True if redo is possible; otherwise, false.</returns>
        public bool CanRedo() => _redoStack.Count > 0;

        /// <summary>
        /// Clears the redo stack.
        /// </summary>
        public void ClearRedoStack()
        {
            _redoStack.Clear();
        }

        #endregion

        #region INotifyPropertyChanged Implementation

        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
