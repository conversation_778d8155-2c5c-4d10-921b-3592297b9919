using System;

namespace SPM_NET46_2017_2018.Models
{
    public class SurveyPoint
    {
        public string PointNumber { get; set; }
        public double Easting { get; set; }
        public double Northing { get; set; }
        public double Elevation { get; set; }
        public string Description { get; set; }

        public SurveyPoint()
        {
            PointNumber = string.Empty;
            Description = string.Empty;
        }

        public SurveyPoint(string pointNumber, double easting, double northing, double elevation, string description)
        {
            PointNumber = pointNumber;
            Easting = Math.Round(easting, 3);
            Northing = Math.Round(northing, 3);
            Elevation = Math.Round(elevation, 3);
            Description = description;
        }
    }
}
