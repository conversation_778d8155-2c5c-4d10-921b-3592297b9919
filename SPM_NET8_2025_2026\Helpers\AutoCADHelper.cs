﻿using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;


namespace SPM_NET8_2025_2026.Helpers
{
    public static class AutoCADHelper
    {
        public static void ExecuteInTransaction(Action<Transaction> action)
        {
            Document doc = Autodesk.AutoCAD.ApplicationServices.Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                try
                {
                    action(trans);
                    trans.Commit();
                }
                catch
                {
                    trans.Abort();
                    throw;
                }
            }
        }
    }
}
