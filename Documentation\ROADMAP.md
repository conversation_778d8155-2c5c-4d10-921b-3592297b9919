# Survey Points Manager - Roadmap

This document tracks planned features, bug fixes, UI improvements, and enhancements for the Survey Points Manager AutoCAD plugin across all versions.

---

## 🎯 Current Focus

### Primary Development Target
- **SPM_NET8_2025_2026** (.NET 8 for AutoCAD 2025/2026) - Active development
- **Legacy Versions** - Maintenance mode (critical bug fixes only)

### Version Strategy
- ✅ **New Features**: Primarily developed for .NET 8 version
- 🔧 **Bug Fixes**: Applied to all supported versions when feasible
- 📦 **Legacy Support**: Maintained for existing users

---

## 🚀 Planned Features

### Import/Export Enhancements
- [ ] **GSI Info Popup**: Get and display GSI info popup (similar to SDR and IDX formats)
- [ ] **Batch Import**: Import multiple files at once (same format for simplicity; each format has its own settings)
- [ ] **Import Options Enhancement**:
  - [ ] Option to add to current point list
  - [ ] Option to clear existing points and replace with imported ones
- [ ] **KML Export Customization**: Allow user to select Google Earth point icons and size in KML export
- [ ] **Smart UTM Zone Detection**:
  - [ ] Auto-detect or assist user in selecting Google Earth UTM Zone
  - [ ] Ask user for visible zone (e.g., "36R")
  - [ ] Allow entering one coordinate to automatically determine the correct UTM zone

### User Experience Improvements
- [ ] **Description Auto-Fill**: Add description textbox in Export Palette where user writes once, description auto-fills picked points
- [ ] **Settings Reorganization**: Organize and improve Settings for better user experience
- [ ] **Path History**: Add history for output directory (recently used paths)
- [ ] **Table Title Customization**: Add two separate input fields for table titles:
  - [ ] Export Table Title (e.g., "Exported Points")
  - [ ] Import Table Title (e.g., "Imported Points")
  - [ ] Remove reliance on single hardcoded "Coordinates" textbox

### Major Features
- [ ] **As-Built Comparison** (Big Update):
  - [ ] Compare design plan points to actual survey points
  - [ ] Generate detailed discrepancy reports
  - [ ] Tolerance settings and analysis
  - [ ] Visual highlighting of discrepancies
- [ ] **Multi-language Support**: Broader usability with internationalization
- [ ] **Cloud Integration**: Feature to let user sync point data to their Google Drive account
- [ ] **Font Enhancement**: Use Rubik font for better visual appeal

---

## 🛠️ Technical Enhancements

### Data Management
- [ ] **Real-time Synchronization**: Changes in DataGrid (coordinates or point numbers) immediately reflect in:
  - [ ] Drawing points
  - [ ] Point numbers
  - [ ] Coordinate tables
- [ ] **Improved Sorting**: Correctly handle in DataGrid:
  - [ ] Numerical sorting
  - [ ] Alphabetical sorting
  - [ ] Mixed (numbers and text) sorting

### Performance Optimization
- [ ] **Memory Optimization**: In Export Palette, allow selecting output directory first, then clicking Export button for large datasets
- [ ] **Background Processing**: Implement background processing for large dataset operations
- [ ] **Caching System**: Implement intelligent caching for frequently accessed data
- [ ] **Progress Indicators**: Add progress bars for long-running operations

### Architecture Improvements
- [ ] **MVVM Restructure**: Restructure project to follow MVVM (Model-View-ViewModel) architecture for better maintainability
- [ ] **Dependency Injection**: Implement DI container for better testability
- [ ] **Plugin Architecture**: Modular plugin system for extensibility
- [ ] **API Layer**: Create public API for third-party integrations

---

## 🎨 UI/UX Improvements

### Visual Design
- [ ] **AI-Enhanced UI**: Upgrade UI design using AI tools (Visily AI, Workik AI, etc.)
- [ ] **Theme Support**: Support light, dark, and system default themes
- [ ] **AutoCAD Integration**: Apply AutoCAD palette theme automatically (using `PaletteTheme` from `ObjectARX/inc/aduipalettes`)
- [ ] **Dynamic Theme Matching**: Match AutoCAD's default dark/light mode dynamically

### User Interface Enhancements
- [ ] **Responsive Design**: Improve layout responsiveness for different screen sizes
- [ ] **Accessibility**: Add accessibility features for users with disabilities
- [ ] **Keyboard Navigation**: Enhanced keyboard shortcuts and navigation
- [ ] **Context Menus**: Right-click context menus for common operations
- [ ] **Drag and Drop**: File drag-and-drop support for imports

---

## 🐞 Bug Fixes

### Critical Issues
- [ ] **Drawing Switching Fix**: When switching between drawings or closing a drawing, ensure plugin resets its data properly and doesn't continue working with old drawing data
- [ ] **Import/Export Feedback**: Inform user about ignored points or errors during Import/Export:
  - [ ] Show a message summarizing the process, e.g.:
    ```
    24 points imported
    3 points ignored
    2 errors encountered
    ```

### Data Integrity
- [ ] **Coordinate Validation**: Enhanced validation for coordinate data
- [ ] **File Format Validation**: Better error handling for corrupted or invalid files
- [ ] **Memory Leak Prevention**: Address potential memory leaks in large dataset processing
- [ ] **Thread Safety**: Ensure thread-safe operations for background processing

### User Experience Fixes
- [ ] **Error Message Clarity**: Improve error messages with actionable solutions
- [ ] **Undo/Redo Reliability**: Ensure undo/redo operations work consistently
- [ ] **Settings Persistence**: Fix issues with settings not being saved properly
- [ ] **File Path Handling**: Better handling of long file paths and special characters

---

## 📅 Release Planning

### Version 2025.2.0 (Q2 2025)
**Focus: User Experience and Performance**
- [ ] GSI Info Popup
- [ ] Batch Import functionality
- [ ] Import Options Enhancement
- [ ] Settings Reorganization
- [ ] Performance optimizations

### Version 2025.3.0 (Q3 2025)
**Focus: Advanced Features**
- [ ] As-Built Comparison (Phase 1)
- [ ] Smart UTM Zone Detection
- [ ] Real-time Data Synchronization
- [ ] Enhanced UI themes

### Version 2026.1.0 (Q1 2026)
**Focus: Integration and Extensibility**
- [ ] Cloud Integration
- [ ] Multi-language Support
- [ ] API Layer
- [ ] Plugin Architecture

### Long-term (2026+)
- [ ] Mobile companion app
- [ ] AI-powered data validation
- [ ] Advanced visualization features
- [ ] Real-time collaboration tools

---

## 🤝 Customer Input and Feedback

### How to Provide Feedback
- **Feature Requests**: Submit through customer support portal
- **Bug Reports**: Priority support for licensed customers
- **Workflow Feedback**: Share your industry-specific needs
- **Beta Testing**: Participate in early access programs

### Priority Guidelines
1. **Critical Bugs**: Immediate attention for licensed customers
2. **Customer-Requested Features**: High priority based on customer feedback
3. **Performance Improvements**: Ongoing priority for professional workflows
4. **Industry-Specific Features**: Developed based on surveying and engineering needs

---

## 📊 Progress Tracking

### Completion Status
- **Features**: 0/15 completed (0%)
- **Enhancements**: 0/12 completed (0%)
- **UI Improvements**: 0/10 completed (0%)
- **Bug Fixes**: 0/12 completed (0%)

### Recent Updates
- **2024-12**: Initial roadmap created
- **2024-12**: .NET 8 migration completed
- **2024-12**: Complete documentation suite added

---

## 📝 Notes

### Development Guidelines
- Each feature/bug can be checked off once completed and integrated
- Keep this file updated alongside major commits or releases
- Features should be developed with backward compatibility in mind
- All new features require documentation updates

### Checkbox Usage
- `[ ]` = ☐ Feature not started
- `[x]` = ☑ Feature completed
- In GitHub, you can click checkboxes to toggle them
- In text editors, manually change `[ ]` to `[x]`

### Version Targeting
- **New Features**: Primarily target SPM_NET8_2025_2026
- **Bug Fixes**: Apply to all supported versions when possible
- **Breaking Changes**: Only in major version releases

---

**Last Updated**: December 2024
**Next Review**: March 2025

For questions about the roadmap or to suggest new features, please [open an issue](https://github.com/[username]/survey-points-manager/issues) or start a [discussion](https://github.com/[username]/survey-points-manager/discussions).
